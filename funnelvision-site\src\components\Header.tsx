import React, { useState, useEffect } from "react";
import FadeIn from "./animations/FadeIn";
import funnelVisionLogo from "../assets/funnelvision-logo.svg";

const Header: React.FC = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [visible, setVisible] = useState(true);
  const [activeLink, setActiveLink] = useState('home');

  // Track previous scroll position to detect direction
  useEffect(() => {
    let lastScrollY = window.scrollY;
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;

          // Set scrolled state
          setScrolled(currentScrollY > 50);

          // Determine scroll direction with a threshold to prevent tiny movements
          if (currentScrollY < lastScrollY - 10) {
            // Scrolling UP - show the header
            setVisible(true);
          } else if (currentScrollY > lastScrollY + 10) {
            // Scrolling DOWN - hide the header
            setVisible(false);
          }

          lastScrollY = currentScrollY;
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when clicking outside or resizing
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (mobileMenuOpen && !target.closest('.mobile-menu-container')) {
        setMobileMenuOpen(false);
      }
    };

    const handleResize = () => {
      if (window.innerWidth >= 1240) { // xl breakpoint - close mobile menu on desktop
        setMobileMenuOpen(false);
      }
    };

    if (mobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    window.addEventListener('resize', handleResize);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
      document.body.style.overflow = '';
    };
  }, [mobileMenuOpen]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleLogoClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (window.location.pathname === '/' || window.location.pathname === '') {
      // On home page, scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      // On other pages, navigate to home
      window.location.href = '/';
    }
  };

  const handleLinkClick = (linkName: string) => {
    setActiveLink(linkName);
    setMobileMenuOpen(false);
  };

  const navigationItems = [
    { label: 'Home', href: '#top', key: 'home' },
    { label: 'Overview', href: '#problem', key: 'overview' },
    { label: 'Services', href: '#solutions', key: 'solutions' },
    { label: 'FAQs', href: '#faq', key: 'faq' },
  ];

  return (
    <FadeIn threshold={0} rootMargin="0px">
      <header
        className={`
          header breakpoint w-full max-w-full fixed top-0 left-0 z-[1000]
          transition-all duration-300 will-change-transform overflow-hidden box-border
          border-b border-header-border shadow-sm h-dia-13 min-h-dia-13 max-h-dia-13
          ${visible ? 'translate-y-0' : '-translate-y-full'}
          ${scrolled
            ? 'bg-header-background-scrolled backdrop-blur-[12px] shadow-md border-card-border-hover'
            : 'bg-header-background backdrop-blur-[8px]'
          }
        `}
      >
        <div
          className="container mx-auto dark:text-white max-w-screen-2xl pt-dia-5 pb-dia-2 px-dia-6"
        >
          <div className="relative flex items-center justify-between py-4.5 lg:py-3">
            {/* Logo */}
            <div className="flex items-center gap-dia-6 h-dia-10">
              <a
                href="/"
                onClick={handleLogoClick}
                className="flex items-center no-underline cursor-pointer transition-transform duration-200 hover:scale-[1.02] gap-dia-6 h-dia-9"
              >
                <img
                  src={funnelVisionLogo}
                  alt="FunnelVision Logo"
                  className="w-auto block h-dia-9"
                  style={{
                    filter: 'brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(1142%) hue-rotate(121deg) brightness(0%) contrast(91%)'
                  }}
                />
              </a>
            </div>

            {/* Hamburger Menu Button */}
            <button
              className="xl:hidden flex flex-col justify-center items-center cursor-pointer bg-transparent border-none w-dia-8 h-dia-8"
              onClick={toggleMobileMenu}
              aria-label="Toggle mobile menu"
            >
              <span
                className={`
                  block w-[var(--dia-space-6)] h-0.5 bg-text-primary transition-all duration-300 mb-[var(--dia-space-1)]
                  ${mobileMenuOpen ? 'rotate-45 translate-y-[var(--dia-space-1)]' : ''}
                `}
              ></span>
              <span
                className={`
                  block w-[var(--dia-space-6)] h-0.5 bg-text-primary transition-all duration-300 mb-[var(--dia-space-1)]
                  ${mobileMenuOpen ? 'opacity-0' : ''}
                `}
              ></span>
              <span
                className={`
                  block w-[var(--dia-space-6)] h-0.5 bg-text-primary transition-all duration-300
                  ${mobileMenuOpen ? '-rotate-45 -translate-y-[var(--dia-space-1)]' : ''}
                `}
              ></span>
            </button>

            {/* Desktop Navigation */}
            <nav className="hidden xl:flex items-center absolute left-1/2 transform -translate-x-1/2" style={{ gap: '0' }}>
              {navigationItems.map((item) => (
                <a
                  key={item.key}
                  href={item.href}
                  onClick={() => handleLinkClick(item.key)}
                  className={`
                    flex items-center relative transition-colors duration-200 text-text-secondary hover:text-text-primary
                    py-[var(--dia-space-1)] px-[var(--dia-space-2)] mr-[var(--dia-space-3)]
                    text-[var(--nav-link-size)]
                    ${activeLink === item.key ? 'text-text-primary font-semibold' : ''}
                  `}
                  style={{
                    fontWeight: activeLink === item.key ? 'var(--nav-link-active-weight)' : 'var(--nav-link-weight)',
                    lineHeight: 'var(--nav-link-line-height)',
                    textDecoration: 'none'
                  }}
                >
                  {item.label}
                </a>
              ))}
            </nav>

            {/* Header Actions */}
            <div className="hidden xl:flex items-center gap-[var(--dia-space-6)]">
              <a
                href="#next-steps"
                className="
                  inline-flex items-center bg-primary text-white border-none cursor-pointer
                  transition-all duration-200 hover:bg-black hover:-translate-y-0.5
                  py-[var(--dia-space-3)] px-[var(--dia-space-6)] gap-[var(--dia-space-1)]
                  text-[var(--btn-primary-size)] rounded-[var(--dia-radius-standard)]
                "
                style={{
                  lineHeight: 'var(--btn-primary-line-height)',
                  fontWeight: 'var(--btn-primary-weight)',
                  textDecoration: 'none'
                }}
              >
                <span className="cta-text">Get in Touch</span>
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="cta-arrow transition-transform duration-200"
                >
                  <path
                    d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z"
                    fill="currentColor"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          className={`
            xl:hidden absolute top-full left-0 right-0 z-[1001]
            bg-background border-b border-header-border transition-all duration-300
            ${mobileMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'}
          `}
        >
          <div className="w-full max-w-screen-2xl mx-auto p-[var(--dia-space-6)]">
            <nav className="flex flex-col gap-[var(--dia-space-6)]">
              {navigationItems.map((item) => (
                <a
                  key={item.key}
                  href={item.href}
                  onClick={() => {
                    handleLinkClick(item.key);
                    setMobileMenuOpen(false);
                  }}
                  className={`
                    text-text-secondary hover:text-text-primary transition-colors duration-200
                    py-[var(--dia-space-2)] text-[var(--nav-link-size)]
                    ${activeLink === item.key ? 'text-text-primary font-semibold' : ''}
                  `}
                  style={{
                    fontWeight: activeLink === item.key ? 'var(--nav-link-active-weight)' : 'var(--nav-link-weight)',
                    lineHeight: 'var(--nav-link-line-height)',
                    textDecoration: 'none'
                  }}
                >
                  {item.label}
                </a>
              ))}
              <div className="pt-[var(--dia-space-6)]">
                <a
                  href="#next-steps"
                  className="
                    inline-flex items-center justify-center w-full
                    bg-primary text-white border-none cursor-pointer
                    transition-all duration-200 hover:bg-black
                    my-[var(--dia-space-9)] py-[var(--dia-space-3)] px-[var(--dia-space-7)]
                    gap-[var(--dia-space-2)] rounded-[var(--dia-radius-standard)]
                    text-[var(--btn-primary-size)]
                  "
                  style={{
                    fontWeight: 'var(--btn-primary-weight)',
                    lineHeight: 'var(--btn-primary-line-height)',
                    textDecoration: 'none'
                  }}
                >
                  Get in Touch
                </a>
              </div>
            </nav>
          </div>
        </div>
      </header>
    </FadeIn>
  );
};

export default Header;
