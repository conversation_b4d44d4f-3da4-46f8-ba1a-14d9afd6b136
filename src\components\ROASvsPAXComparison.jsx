import React from "react";
import "./ROASvsPAXComparison.css";

const ComparisonItem = ({ icon, text, isPositive }) => (
  <div className={`comparison-item ${isPositive ? "positive" : "negative"}`}>
    <span className="comparison-icon">{icon}</span>
    <span className="comparison-text">{text}</span>
  </div>
);

const ROASvsPAXComparison = () => {
  const traditionalItems = [
    { text: "Optimizes for revenue" },
    { text: "Treats all products the same" },
    { text: "Focuses on CTR and CPC" },
    { text: "Assumes platform data is accurate" },
    { text: "Reactive campaign structure" },
    { text: "One-size-fits-all media buying" },
    { text: "Fluffy dashboards, no clarity" },
    { text: "Outsourced execution" }
  ];

  const paxItems = [
    { text: "Optimizes for profit" },
    { text: "Segments bids by margin tier" },
    { text: "Focuses on contribution margin" },
    { text: "Uses cross-validated source of truth" },
    { text: "Built from the margin up" },
    { text: "Custom strategy per client funnel" },
    { text: "Clear reporting built for CEOs + CMOs" },
    { text: "Founder-led strategy with senior operators" }
  ];

  // Green flag icons for PAX column
  const GreenFlag = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="currentColor"/>
      <line x1="4" y1="22" x2="4" y2="15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    </svg>
  );

  const paxIcons = Array(8).fill(<GreenFlag />);

  // Red flag icon for traditional ROAS
  const RedFlag = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="currentColor"/>
      <line x1="4" y1="22" x2="4" y2="15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    </svg>
  );

  return (
    <div className="comparison-container">
      <div className="comparison-cards">
        <div className="comparison-card roas-card">
          <h3 className="comparison-title">ROAS Agencies</h3>
          <div className="comparison-items">
            {traditionalItems.map((item, index) => (
              <ComparisonItem
                key={`trad-${index}`}
                icon={<RedFlag />}
                text={item.text}
                isPositive={false}
              />
            ))}
          </div>
        </div>
        <div className="comparison-card pax-card">
          <h3 className="comparison-title">FunnelVision with PAX™</h3>
          <div className="comparison-items">
            {paxItems.map((item, index) => (
              <ComparisonItem
                key={`pax-${index}`}
                icon={paxIcons[index]}
                text={item.text}
                isPositive={true}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ROASvsPAXComparison;
