/* Animated hero styles - using diabrowser design tokens */

.hero-animated {
  width: 100%;
  background-color: var(--dia-color-background);
  min-height: 100vh; /* Use min-height instead of fixed height */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--dia-space-12) var(--dia-space-6); /* Use diabrowser spacing */
  box-sizing: border-box;
}

/* Responsive height adjustments */
@media (min-width: 800px) {
  .hero-animated {
    min-height: 90vh; /* Slightly reduce on larger screens */
    padding: var(--dia-space-14) var(--dia-space-6);
  }
}

@media (min-width: 1200px) {
  .hero-animated {
    min-height: 85vh; /* Further optimize for large screens */
    padding: var(--dia-space-15) var(--dia-space-6);
  }
}

/* Hero uses standard .container class from diabrowser system */

/* Account for mobile browser chrome in iOS and Android */
@media screen and (max-width: 800px) {
  .hero-animated {
    height: calc(100vh - var(--dia-space-8));
    /* Account for header height */
    overflow-x: hidden; /* Prevent horizontal scrollbar on mobile */
    max-width: 100vw;
  }

  .hero-animated .container {
    padding: var(--dia-space-6);
    width: 100%;
    gap: var(--dia-space-9);
  }

  .hero-title-static,
  .hero-title-better,
  .hero-title-metrics {
    font-size: var(--dia-hero-title-size);
    letter-spacing: var(--dia-hero-title-spacing);
  }
  
  /* Hero description handled by main rule with diabrowser tokens */
  
  .hero-title {
    height: auto;
  }
  
  .hero-text-container {
    width: 100%;
    max-width: 100%;
  }
  
  /* Hero description handled by main rule with diabrowser tokens */
  
  .hero-buttons {
    width: 100%;
  }

  .call-to-action {
    flex-direction: column;
    width: 100%;
  }

  .call-to-action-container {
    width: 100%;
    justify-content: center;
  }
  
  .logos-section-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }
  
  .logos-title-container {
    width: 100%;
  }
  
  .logos-container {
    flex-wrap: wrap;
    justify-content: center;
  }
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--dia-space-11);
  width: 100%;
  text-align: center;
  position: relative;
  padding-top: var(--dia-space-8);
}

.hero-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0;
  width: 100%;
  position: relative;
}

.hero-title h1 {
  margin: 0;
  padding: 0;
}

/* Hero container padding handled by standard .container class */

.hero-title-static {
  color: var(--dia-color-text-primary);
  text-align: center;
  font-family: var(--font-family-inter);
  font-size: var(--dia-hero-title-size);
  font-style: normal;
  font-weight: var(--dia-hero-title-weight);
  line-height: var(--dia-hero-title-line-height);
  letter-spacing: var(--dia-hero-title-spacing);
  margin-bottom: var(--dia-space-3);
  display: block;
}

.hero-title-line {
  display: block;
  text-align: center;
  width: 100%;
  margin-top: var(--dia-space-1);
}

@media (max-width: 767px) {
  .hero-title-static {
    font-size: var(--dia-hero-title-size);
    line-height: var(--dia-hero-title-line-height);
  }

  .hero-title-better,
  .hero-title-metrics {
    font-size: var(--dia-hero-title-size);
    line-height: var(--dia-hero-title-line-height);
  }
}

.hero-title-better {
  color: var(--dia-color-text-primary);
  font-size: var(--dia-hero-title-size);
  font-weight: var(--dia-hero-title-weight);
  letter-spacing: var(--dia-hero-title-spacing);
  display: block;
  text-align: center;
}

.hero-title-metrics {
  color: var(--dia-color-text-primary); /* Simplified to monochromatic */
  font-style: normal;
  font-size: var(--dia-hero-title-size);
  font-weight: var(--dia-hero-title-weight);
  letter-spacing: var(--dia-hero-title-spacing);
}

.hero-text-container {
  display: flex;
  width: 100%;
  max-width: var(--width-843); /* Use design token for consistency */
  flex-direction: column;
  align-items: center;
  margin: var(--dia-space-3) auto 0 auto; /* Center with auto margins */
  position: relative;
}

.hero-description {
  color: var(--dia-color-text-secondary);
  text-align: center;
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size);
  font-style: normal;
  font-weight: var(--dia-body-text-weight);
  line-height: var(--dia-body-text-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  margin: 0 auto;
}

.hero-description strong {
  font-weight: 600;
}

/* Hero description responsive handled by diabrowser responsive system */

.hero-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--dia-space-5); /* Space between CTA buttons and rating */
  margin-top: var(--dia-space-6);
  position: relative;
  width: 100%;
}

/* Hero buttons layout handled by main rules above */

.hero-launch-btn {
  display: flex;
  padding: var(--dia-space-1) var(--dia-space-4);
  justify-content: center;
  align-items: center;
  gap: var(--dia-space-2);
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  background: var(--dia-color-teal-green);
  margin-bottom: var(--dia-space-3);
  position: relative;
}

.hero-launch-text {
  color: var(--dia-color-white);
  text-align: center;
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-xs);
  font-style: normal;
  font-weight: var(--dia-font-weight-normal);
  line-height: normal;
  letter-spacing: var(--dia-body-text-spacing);
  position: relative;
}

.btn-icon {
  width: var(--dia-space-7);
  height: var(--dia-space-7);
}

.call-to-action {
  display: flex;
  flex-direction: column; /* Stack on mobile */
  justify-content: center;
  align-items: center;
  gap: var(--dia-space-4);
  position: relative;
  width: 100%;
}

/* Responsive CTA layout - side by side on tablet+ */
@media (min-width: 600px) {
  .call-to-action {
    flex-direction: row;
    gap: var(--dia-space-5);
  }
}

@media (min-width: 800px) {
  .call-to-action {
    gap: var(--dia-space-6);
  }
}

@media (min-width: 1000px) {
  .call-to-action {
    gap: var(--dia-space-7);
  }
}

.call-to-action-container {
  display: flex;
  padding: var(--dia-space-4) var(--dia-space-8); /* Use diabrowser spacing */
  align-items: center;
  height: 50px;
  justify-content: center;
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  cursor: pointer;
  position: relative;
  min-width: 180px; /* Ensure consistent button width */
  text-align: center;
  flex-shrink: 0; /* Prevent buttons from shrinking */
}

/* Responsive button sizing */
@media (min-width: 600px) {
  .call-to-action-container {
    min-width: 200px;
    padding: var(--dia-space-4) var(--dia-space-9);
  }
}

@media (min-width: 800px) {
  .call-to-action-container {
    min-width: 220px;
    padding: var(--dia-space-5) var(--dia-space-9);
  }
}

@media (min-width: 1000px) {
  .call-to-action-container {
    min-width: 240px;
    padding: var(--dia-space-5) var(--dia-space-10);
  }
}

.call-to-action-primary {
  border: none;
  background: var(--dia-color-cta-background-primary); /* Use diabrowser black */
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.call-to-action-primary .cta-arrow {
  color: var(--dia-color-background); /* White arrow on dark background */
}

.call-to-action-primary:hover {
  background: var(--cta-primary-background-hover); /* Pure black for better contrast */
  box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.25);
  transform: translateY(-2px);
}

.call-to-action-primary:hover .cta-arrow {
  transform: var(--cta-arrow-hover-transform);
}

.call-to-action-primary:hover .cta-text {
  transform: var(--cta-text-hover-transform);
}

.call-to-action-secondary {
  border: 1px solid var(--dia-color-text-primary);
  background: var(--dia-color-cta-background-secondary);
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.call-to-action-secondary:hover {
  background: var(--dia-color-background); /* Keep same background */
  box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.call-to-action-secondary:hover .cta-arrow {
  transform: var(--cta-arrow-hover-transform);
}

.call-to-action-secondary:hover .cta-text {
  transform: var(--cta-text-hover-transform);
}

.button-text {
  display: flex;
  align-items: center;
  gap: var(--dia-space-2);
  position: relative;
}

.button-text-primary {
  color: var(--dia-color-background); /* White text on dark button */
  font-family: var(--font-family-inter);
  font-size: var(--dia-btn-cta-size);
  font-style: normal;
  font-weight: var(--dia-btn-cta-weight);
  line-height: var(--dia-btn-cta-line-height);
  letter-spacing: var(--dia-body-text-spacing);
}

.button-text-secondary {
  color: var(--dia-color-text-primary); /* Dark text on light button */
  font-family: var(--font-family-inter);
  font-size: var(--dia-btn-secondary-size);
  font-style: normal;
  font-weight: var(--dia-btn-secondary-weight);
  line-height: var(--dia-btn-secondary-line-height);
  letter-spacing: var(--dia-body-text-spacing);
}

.rating-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--dia-space-2); /* Use diabrowser spacing */
  margin-top: 0; /* Remove margin since it's handled by parent gap */
  position: relative;
  width: 100%;
  text-align: center;
}

.star-container {
  display: flex;
  gap: var(--dia-space-1);
  position: relative;
}

.rating-text {
  color: var(--dia-color-text-secondary); /* Use diabrowser secondary text color */
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-xs);
  font-style: normal;
  font-weight: var(--dia-font-weight-medium);
  line-height: normal;
  letter-spacing: var(--dia-body-text-spacing);
  position: relative;
}

.logos-section-container {
  display: flex;
  width: 100%;
  max-width: 800px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--dia-space-9);
  margin-top: var(--dia-space-11);
  position: relative;
}

.logos-title-container {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  position: relative;
}

.logos-title {
  color: var(--dia-color-text-primary);
  text-align: center;
  font-family: Inter, sans-serif;
  font-size: var(--dia-body-text-size);
  font-style: normal;
  font-weight: var(--dia-body-text-weight);
  line-height: var(--dia-body-text-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  position: relative;
}

.logos-carousel-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 60px;
  overflow: visible;
  max-width: 100%;
}

/* Override logo carousel styles to fit in hero section */
.logos-carousel-wrapper .logo-carousel-container {
  height: 60px;
  width: 100%;
  max-width: 100%;
  overflow: visible;
}

.logos-carousel-wrapper .logo-carousel-track {
  height: 60px;
  gap: 20px;
  display: flex;
  justify-content: space-between;
  width: 100%;
  overflow: visible;
}

.logos-carousel-wrapper .logo-column {
  height: 60px;
  width: auto;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 8px;
  overflow: visible;
  position: relative;
}

.logos-carousel-wrapper .logo-svg {
  height: 40px;
  width: auto;
  max-width: 80%;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  object-fit: contain;
}

.logos-carousel-wrapper .logo-item {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-item {
  display: flex;
  height: 60px;
  flex: 1;
  justify-content: center;
  align-items: center;
  position: relative;
}

.logo-inner-container {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  position: relative;
}

.logo-image {
  max-height: 100%;
  max-width: 100%;
  height: auto;
  opacity: 0.7;
}

/* Add responsive styles for mobile */
@media screen and (max-width: 768px) {
  /* Prevent horizontal scrollbar on mobile */
  body {
    overflow-x: hidden;
  }

  .hero-title-static,
  .hero-title-better,
  .hero-title-metrics {
    font-size: var(--dia-hero-title-size);
  }

  /* CTA layout handled by main responsive rules above */

  .logos-container {
    flex-wrap: wrap;
  }

  .logo-item {
    flex-basis: 50%;
  }

  .logos-section-container {
    padding: 0 var(--dia-space-3);
    max-width: 100%;
    overflow-x: hidden;
  }

  .logos-carousel-wrapper {
    width: 100%;
    height: 60px;
    margin: 0 auto;
    overflow-x: hidden;
    overflow-y: visible;
    max-width: calc(100vw - 2 * var(--dia-space-3));
  }

  .logos-carousel-wrapper .logo-carousel-container {
    height: 60px;
    width: 100%;
    overflow-x: hidden;
    overflow-y: visible;
  }

  .logos-carousel-wrapper .logo-carousel-track {
    width: 100%;
    justify-content: center;
    gap: 8px;
    overflow-x: hidden;
    overflow-y: visible;
  }

  .logos-carousel-wrapper .logo-column {
    height: 60px;
    width: 30%;
    max-width: 120px;
    margin: 0 2px;
    padding: 0;
    flex: 0 1 auto;
    overflow: hidden; /* Constrain logo animations within column */
  }
  
  .logos-carousel-wrapper .logo-svg {
    height: 38px;
    max-width: 100%;
    opacity: 0.9;
  }
  
  .logos-carousel-wrapper .logo-item {
    height: 60px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* Constrain logo animations within item */
  }
}

@media (min-width: var(--breakpoint-tablet)) and (max-width: var(--breakpoint-laptop)) {
  .hero-title-dynamic {
    height: var(--line-height-72);
  }
  
  /* Hero titles use diabrowser tokens - no override needed */
  
  /* Hero description handled by main rule with diabrowser tokens */
  
  .logos-carousel-wrapper {
    height: 55px;
  }
  
  .logos-carousel-wrapper .logo-column {
    height: 55px;
    margin-right: 10px;
  }
  
  .logos-carousel-wrapper .logo-svg {
    height: 36px;
  }
  
  .logos-carousel-wrapper .logo-item {
    height: 55px;
  }
}

@media (min-width: 768px) {
  .hero-title-dynamic {
    height: var(--line-height-72);
  }
}

/* Responsive container constraints for hero title */
@media (min-width: 768px) and (max-width: 1023px) {
  .hero-title {
    max-width: 736px;
    margin: 0 auto;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    max-width: 1000px;
    margin: 0 auto;
  }

  .hero-title-dynamic {
    height: var(--line-height-96);
    max-width: 1000px;
  }
}
