/* LogoCarousel Component Styles */
.logo-carousel-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-family: var(--font-family-inter);
  letter-spacing: -0.015em; /* Adjusted for Inter font */
  overflow: hidden;
  margin: 2rem 0;
}

/* Logo column styles */
.logo-column {
  position: relative;
  height: 3.5rem;
  width: 6rem;
  overflow: hidden;
  margin-right: 1rem;
}

/* Logo item positioning */
.logo-item {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* SVG logo styling */
.logo-svg {
  max-height: 80%;
  max-width: 80%;
  height: 5rem;
  width: 5rem;
  object-fit: contain;
  transition: filter 0.3s ease, opacity 0.3s ease;
  filter: grayscale(1);
  opacity: 0.8;
}

.logo-svg:hover {
  filter: grayscale(0);
  opacity: 1;
}

/* Container for all logo columns */
.logo-carousel-track {
  display: flex;
  align-items: center;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .logo-carousel-container {
    max-width: 100vw;
  }
}

@media (min-width: 768px) {
  .logo-column {
    height: 6rem;
    width: 12rem;
  }

  .logo-svg {
    height: 8rem;
    width: 8rem;
  }
}


/* Gradient Heading Component Styles */
.gradient-heading {
  font-family: var(--font-family-inter);
  letter-spacing: -0.015em; /* Adjusted for Inter font */
  padding-bottom: 0.75rem;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.gradient-heading-default {
  background-image: linear-gradient(to top, #404040, #303030);
}

.gradient-heading-secondary {
  background-image: linear-gradient(to top, #737373, #525252);
}

.gradient-heading-pink {
  background-image: linear-gradient(to top, var(--color-accent, #d946ef), #d946ef90);
}

.gradient-heading-light {
  background-image: linear-gradient(to top, #e5e5e5, #d4d4d4);
}

/* Typography sizes */
.heading-xxs {
  font-size: var(--font-size-base, 1rem);
  font-weight: 700;
}

.heading-xs {
  font-size: var(--font-size-md, 1.125rem);
  font-weight: 700;
}

.heading-sm {
  font-size: var(--font-size-lg, 1.25rem);
  font-weight: 700;
}

.heading-md {
  font-size: var(--font-size-xl, 1.5rem);
  font-weight: 700;
}

.heading-lg {
  font-size: var(--font-size-3xl, 2rem);
  font-weight: 700;
}

.heading-xl {
  font-size: var(--font-size-4xl, 2.25rem);
  font-weight: 700;
}

.heading-xxl {
  font-size: var(--font-size-6xl, 3rem);
  font-weight: 700;
}

/* Responsive styles */
@media (min-width: 768px) {
  .logo-column {
    height: 6rem;
    width: 12rem;
  }
  
  .logo-svg {
    height: 8rem;
    width: 8rem;
  }
  
  .heading-xxs {
    font-size: var(--font-size-md, 1.125rem);
  }
  
  .heading-xs {
    font-size: var(--font-size-lg, 1.25rem);
  }
  
  .heading-sm {
    font-size: var(--font-size-xl, 1.5rem);
  }
  
  .heading-md {
    font-size: var(--font-size-3xl, 2rem);
  }
  
  .heading-lg {
    font-size: var(--font-size-4xl, 2.25rem);
  }
  
  .heading-xl {
    font-size: var(--font-size-6xl, 3rem);
  }
  
  .heading-xxl {
    font-size: var(--font-size-56, 3.5rem);
  }
}
