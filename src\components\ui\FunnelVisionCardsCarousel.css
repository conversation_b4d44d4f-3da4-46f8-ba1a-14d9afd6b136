/* FunnelVision Cards Carousel Styles - Unified Container System */

/* Container styles - unified with diabrowser system */
.funnelvision-carousel-container {
  position: relative;
  width: 100%;
  max-width: 100%; /* Use 100% instead of 100vw to avoid scrollbar issues */
  overflow: hidden;
  margin: 0 auto;
  margin-bottom: var(--dia-space-8);
  z-index: 0;
  box-sizing: border-box;
  /* Remove custom padding - let parent container handle it */
}

/* Content wrapper to allow for proper scrolling */
.funnelvision-carousel-content {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Carousel track for card positioning */
.funnelvision-carousel-track {
  display: flex;
  padding: var(--dia-space-8) 0; /* Only vertical padding */
  margin: var(--dia-space-3) 0; /* Remove auto centering */
  gap: var(--dia-space-4); /* Smaller gap to fit more cards */
  overflow-x: auto;
  overflow-y: hidden; /* Prevent vertical overflow */
  flex-wrap: nowrap; /* Keep everything in a single row */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  justify-content: center; /* Center cards for better visual balance */
}

.funnelvision-carousel-track::-webkit-scrollbar {
  display: none;
}

/* Ensure direct children of the track never shrink and stay in a single line */
.funnelvision-carousel-track > * {
  flex: 0 0 auto;
}

/* Individual carousel card styling */
.funnelvision-carousel-card {
  width: 350px; /* Fixed width, let container handle responsiveness */
  min-width: 260px; /* Allow smaller cards on narrow screens */
  max-width: 350px; /* Maximum width */
  border-radius: var(--card-border-radius);
  margin-right: 0; /* Using gap instead for spacing */
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: var(--card-transition);
  margin-bottom: var(--dia-space-2);
  margin-top: 0;
  border: 1px solid var(--dia-color-card-border); /* Universal thin gray border */
  box-shadow: var(--card-box-shadow);
  box-sizing: border-box;
}

/* All carousel cards now use white background universally */
.funnelvision-carousel-card {
  background-color: var(--dia-color-card-background); /* Pure white for all cards */
}

/* Card hover effect */
.funnelvision-carousel-card:hover:not(.card-revealed) {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
}

/* Card image styling */
.funnelvision-carousel-card-image {
  width: 100%;
  height: 220px;
  position: relative;
  overflow: hidden;
}

.funnelvision-carousel-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.funnelvision-carousel-card:hover .funnelvision-carousel-card-image img {
  transform: scale(1.05);
}

/* Card content styling */
.funnelvision-carousel-card-content {
  padding: var(--dia-space-10); /* Match other cards' generous padding */
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

/* Card category/tag using card label tokens */
.funnelvision-carousel-card-tag {
  font-family: var(--font-family-inter);
  font-size: var(--dia-card-tag-size);
  font-weight: var(--dia-card-tag-weight);
  line-height: var(--dia-card-tag-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  color: var(--dia-color-text-secondary);
  text-transform: uppercase;
  margin-bottom: var(--dia-space-2);
}

/* Responsive adjustments for card tag */
@media (min-width: 768px) {
  .funnelvision-carousel-card-tag {
    font-size: var(--font-size-card-label-tablet);
  }
}

@media (min-width: 1920px) {
  .funnelvision-carousel-card-tag {
    font-size: var(--font-size-card-label-desktop);
  }
}

/* Card title using card title tokens */
.funnelvision-carousel-card-title {
  font-family: var(--font-family-inter);
  font-size: var(--dia-carousel-card-title-size);
  font-weight: var(--dia-carousel-card-title-weight);
  line-height: var(--dia-carousel-card-title-line-height);
  color: var(--color-text-primary);
  margin: var(--funnelvision-carousel-title-margin);
}

/* Responsive adjustments for card title */
/* Responsive scaling handled by diabrowser tokens */

/* Card overlay styling */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--dia-color-overlay-background);
  color: var(--dia-color-text-primary);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--card-border-radius);
  padding: var(--dia-space-8);
  z-index: 10;
  animation: fadeIn 0.3s ease-in-out;
  gap: var(--dia-space-6);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Card revealed state */
.card-revealed {
  cursor: default;
  min-height: 560px; /* allow overlay content to fit without scrolling */
}

/* Card overlay content styling */
.card-overlay-content {
  text-align: left;
  width: 100%;
  padding-right: 10px;
}

/* Card overlay title using diabrowser tokens */
.card-overlay-title {
  font-family: var(--font-family-inter);
  font-size: var(--dia-carousel-card-title-size);
  font-weight: var(--dia-carousel-card-title-weight);
  line-height: var(--dia-carousel-card-title-line-height);
  color: var(--dia-color-text-primary);
  margin-bottom: var(--dia-space-4);
}

/* Responsive scaling handled by diabrowser tokens */

/* Card overlay description using diabrowser tokens */
.card-overlay-description {
  font-family: var(--font-family-inter);
  font-size: var(--dia-carousel-card-description-size);
  font-weight: var(--dia-carousel-card-description-weight);
  line-height: var(--dia-carousel-card-description-line-height);
  color: var(--dia-color-text-secondary);
  margin-bottom: var(--dia-space-6);
}

/* Responsive scaling handled by diabrowser tokens */

/* Card expanded content container */
.card-expanded-content {
  color: var(--dia-color-text-primary);
  margin-bottom: 1.5rem;
}

/* Styles for expanded content in card overlay */
/* Card overlay heading using diabrowser tokens */
.card-overlay-content h4 {
  font-family: var(--font-family-inter);
  font-size: var(--dia-carousel-card-subtitle-size);
  font-weight: var(--dia-carousel-card-subtitle-weight);
  line-height: var(--dia-carousel-card-subtitle-line-height);
  color: var(--dia-color-text-primary);
  margin-top: var(--dia-space-5);
  margin-bottom: var(--dia-space-3);
}

/* Responsive scaling handled by diabrowser tokens */

.card-overlay-content ul {
  list-style-type: disc;
  padding-left: var(--dia-space-6);
  margin-bottom: var(--dia-space-4);
  color: var(--dia-color-text-primary);
}

.card-overlay-content li {
  margin-bottom: var(--dia-space-2);
  font-size: var(--dia-carousel-card-content-size);
  line-height: var(--dia-carousel-card-content-line-height);
  color: var(--dia-color-text-primary);
}

.card-overlay-content p {
  font-size: var(--dia-carousel-card-content-size);
  line-height: var(--dia-carousel-card-content-line-height);
  margin-top: var(--dia-space-3);
  margin-bottom: var(--dia-space-3);
  color: var(--dia-color-text-primary);
}

/* Bullet styling for card content lists */
.funnelvision-carousel-modal-body ul,
.funnelvision-carousel-card-content ul {
  list-style-type: disc;
  padding-left: 1.25rem;
  margin: 0.75rem 0;
}

.funnelvision-carousel-modal-body li,
.funnelvision-carousel-card-content li {
  margin-bottom: 0.5rem;
  font-size: var(--dia-card-content-size);
  line-height: var(--dia-card-content-line-height);
  color: var(--dia-color-text-secondary);
}

/* Overlay CTA link using diabrowser tokens */
.card-overlay-cta {
  font-family: var(--font-family-inter);
  font-size: var(--dia-carousel-card-cta-size);
  font-weight: var(--dia-carousel-card-cta-weight);
  line-height: var(--dia-carousel-card-cta-line-height);
  color: var(--dia-color-text-primary);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: color var(--transition-base);
}

.overlay-arrow {
  width: var(--dia-space-6);
  height: var(--dia-space-6);
  margin-left: var(--dia-space-1);
  transition: transform var(--transition-base);
}

.card-overlay-cta:hover {
  color: var(--dia-color-text-secondary);
}

.card-overlay-cta:hover .overlay-arrow {
  transform: translateX(var(--dia-space-1));
}

/* Toggle buttons */
.card-toggle-button {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: var(--dia-color-black);
  transition: transform 0.2s ease, background-color 0.2s ease;
  z-index: 10;
}

.card-toggle-button:hover {
  transform: scale(1.1);
  background-color: white;
}

.card-toggle-close {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  background-color: var(--dia-color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  color: var(--dia-color-black);
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.card-toggle-close:hover {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.3);
}

/* Contextual colors for the + toggle button */
/* Cards with white background need a grey + button */
.capabilities-section .card-toggle-button,
.devices-section .card-toggle-button {
  background-color: var(--dia-color-grey);
}

/* Card image container */
.funnelvision-carousel-card-image {
  width: 100%;
  height: 400px;
  position: relative;
  overflow: hidden;
}

/* Image display */
.funnelvision-carousel-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

/* Card content container - duplicate definition removed, using above definition */

/* Category tag */
.funnelvision-carousel-card-tag {
  font-family: var(--font-family-inter);
  font-size: var(--dia-card-tag-size);
  font-weight: var(--dia-card-tag-weight);
  color: var(--dia-color-text-primary);
  text-transform: uppercase;
  letter-spacing: -0.02em; /* Adjusted for Inter font metrics */
  margin-bottom: 0.5rem;
}

/* Card title */
.funnelvision-carousel-card-title {
  font-family: var(--font-family-inter);
  font-size: var(--dia-card-title-size);
  font-weight: var(--dia-card-title-weight);
  line-height: var(--dia-card-title-line-height);
  color: var(--dia-color-text-secondary);
  letter-spacing: var(--letter-spacing-tight); /* Slightly adjusted for Inter font metrics */
  margin: 0 0 1.5rem 0;
}

/* Enhanced navigation buttons with light mode styling */
.funnelvision-carousel-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 2;
  color: var(--dia-color-text-primary);
  transition: var(--card-transition);
}

.funnelvision-carousel-nav-button:hover {
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  box-shadow: var(--card-hover-box-shadow);
  transform: translateY(-50%) scale(1.05);
  border-top: var(--card-border-top-highlight);
}

.funnelvision-carousel-nav-button:active {
  transform: translateY(-50%) scale(0.95);
}

.funnelvision-carousel-nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.funnelvision-carousel-nav-button.left {
  left: 0.5rem;
}

.funnelvision-carousel-nav-button.right {
  right: 0.5rem;
}

/* Modal styles */
.funnelvision-carousel-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: var(--space-lg) var(--space-md);
  backdrop-filter: blur(8px);
}

.funnelvision-carousel-modal {
  width: 90%;
  max-width: 920px;
  max-height: 90vh;
  background-color: var(--dia-color-white);
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  padding: 0;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.funnelvision-carousel-modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-grey-95);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.funnelvision-carousel-modal-title {
  font-family: var(--font-family-inter);
  font-size: var(--font-size-28);
  font-weight: 700;
  color: var(--color-grey-5);
  letter-spacing: -0.015em; /* Adjusted for Inter font metrics */
  margin: 0;
  padding: 0;
}

.funnelvision-carousel-modal-close {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-grey-98);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  color: var(--color-grey-20);
  transition: background-color 0.2s ease;
}

.funnelvision-carousel-modal-close:hover {
  background-color: var(--color-grey-95);
}

.funnelvision-carousel-modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

/* Content block styles for modal content */
.content-block {
  margin-bottom: 3rem;
}

.content-text {
  font-family: var(--font-family-inter);
  font-size: var(--font-size-18);
  line-height: var(--line-height-28);
  color: var(--color-grey-25);
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em; /* Slight adjustment for Inter font */
}

.content-highlight {
  font-weight: 700;
  color: var(--color-grey-10);
}

.content-image {
  width: 100%;
  height: auto;
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  margin-bottom: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .funnelvision-carousel-track {
    padding-left: 2rem;
  }
}

@media (max-width: 768px) {
  .funnelvision-carousel-card {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }

  /* funnelvision-carousel-container padding now handled by parent container */

  .funnelvision-carousel-track {
    padding: var(--dia-space-6) 0; /* No horizontal padding */
    gap: var(--dia-space-3); /* Smaller gap on mobile */
  }

  .funnelvision-carousel-card-image {
    height: 300px;
  }

  .funnelvision-carousel-modal {
    width: 95%;
    max-height: 85vh;
  }

  .funnelvision-carousel-modal-content {
    flex-direction: column;
    height: auto;
  }

  .funnelvision-carousel-modal-image {
    width: 100%;
    height: 300px;
    max-height: 30vh;
  }

  .funnelvision-carousel-card-title {
    font-size: var(--font-size-20);
    line-height: var(--line-height-28);
  }
  
  .funnelvision-carousel-modal-title {
    font-size: var(--font-size-24);
  }
}

@media (max-width: 480px) {
  .funnelvision-carousel-card {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }

  /* funnelvision-carousel-container padding now handled by parent container */

  .funnelvision-carousel-track {
    padding: var(--dia-space-4) 0; /* No horizontal padding */
    gap: var(--dia-space-2); /* Minimal gap on small screens */
  }

  .funnelvision-carousel-card-image {
    height: 250px;
  }
}
