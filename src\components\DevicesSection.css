/* Devices section styles */
#devices,
.devices-section {
  background-color: var(--color-background-secondary);
  width: 100%;
  overflow: hidden;
}

.devices-section {
  padding: var(--dia-space-15) 0;
}


/* Dorsey subsection styles */
.dorsey-container {
  margin-bottom: var(--dia-space-15);
}

.dorsey-title {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-80);
  font-weight: 700;
  line-height: var(--line-height-96);
  letter-spacing: var(--letter-spacing-tight);
  margin: 0 0 var(--dia-space-11) 0;
  text-align: center;
}

.dorsey-description {
  max-width: var(--width-616);
  margin: 0 auto var(--dia-space-14) auto;
  text-align: center;
}

.dorsey-description p {
  color: var(--color-grey-25);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-28);
  font-weight: 400;
  line-height: var(--line-height-36);
  margin: 0;
}

.dorsey-video {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--dia-space-14);
  padding: 0 var(--dia-space-15);
}

.dorsey-video .video-container {
  width: 100%;
  max-width: 1000px; /* Consistent with diabrowser card container standards */
  height: 601px; /* 16:9 ratio for widescreen video */
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, #e8f0fe 0%, #d4e4fa 50%, #c2d9f5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.dorsey-video .video-container::before {
  content: "";
  position: absolute;
  inset: 1px; /* Creates inner border effect */
  border-radius: 22px; /* 24px - 2px border */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Inner highlight border */
}

.dorsey-video .video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.video-play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-primary);
  cursor: pointer;
  transition: transform 0.2s;
}

.video-play-button:hover {
  transform: scale(1.1);
}

.dorsey-story {
  max-width: var(--width-616);
  margin: 0 auto;
}

.dorsey-story p {
  color: #5f6368;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-28);
  font-weight: 400;
  line-height: var(--line-height-36);
  text-align: center;
  margin: 0;
}

.devices-section {
  padding: var(--dia-space-15) 0;
  background-color: #ffffff;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.devices-header {
  text-align: center;
  margin-bottom: var(--dia-space-14);
  max-width: var(--width-843);
  margin-left: auto;
  margin-right: auto;
  margin-bottom: var(--dia-space-14);
}

.devices-title {
  color: #202124;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-48);
  font-weight: 400;
  line-height: var(--line-height-56);
  letter-spacing: -0.3px; /* Adjusted for Inter font */
  margin: 0 0 var(--dia-space-11) 0;
}

.devices-description {
  max-width: var(--width-616);
  margin: 0 auto;
}

.devices-description p {
  color: #5f6368;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-28);
  font-weight: 400;
  line-height: var(--line-height-36);
  margin: 0;
  text-align: center;
}

.devices-grid {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: var(--dia-space-12);
  max-width: 1200px; /* Consistent with diabrowser card container standards */
  margin: 0 auto;
}

.devices-bento-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--dia-space-9);
  max-width: 1200px; /* Consistent with diabrowser card container standards */
  margin: var(--dia-space-12) auto 0;
  padding: 0 var(--dia-space-9);
}

@media (min-width: 768px) {
  .devices-bento-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .devices-bento-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--dia-space-10);
  }
}

.device-card {
  width: 100%; /* Use full width within grid container */
  max-width: 400px; /* Consistent with diabrowser card standards */
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--color-grey-7);
  border-radius: 24px;
  border: 1px solid var(--color-white-15);
  overflow: hidden;
  padding-top: 0;
  padding-bottom: 0;
}

.device-image {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-image-placeholder {
  font-size: 120px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-content {
  padding: var(--dia-space-9);
  text-align: center;
}

.device-title {
  color: #202124;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-20);
  font-weight: 400;
  line-height: var(--line-height-28);
  margin: 0 0 var(--dia-space-2) 0;
}

.device-description {
  color: var(--color-grey-63);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-16);
  font-weight: 400;
  line-height: var(--line-height-24);
  margin: 0;
}

/* Responsive design */
@media (max-width: 1024px) {
  .dorsey-video {
    padding: 0 var(--dia-space-12);
  }

  .devices-grid {
    flex-direction: column;
    align-items: center;
    gap: var(--dia-space-9);
  }
}

@media (max-width: 768px) {
  .dorsey-title {
    font-size: 36px;
    line-height: 44px;
  }

  .dorsey-description p {
    font-size: var(--font-size-20);
    line-height: var(--line-height-28);
  }

  .dorsey-video .video-container {
    height: 300px;
  }

  .dorsey-story p {
    font-size: var(--font-size-20);
    line-height: var(--line-height-28);
  }

  .devices-section {
    padding: var(--dia-space-13) 0;
  }

  .devices-title {
    font-size: 36px;
    line-height: 44px;
  }

  .devices-description p {
    font-size: var(--font-size-20);
    line-height: var(--line-height-28);
  }

  .device-card {
    width: 100%;
    max-width: 500px;
  }

  .device-image {
    height: 300px;
  }

  .device-image-placeholder {
    font-size: 80px;
  }
}

@media (max-width: 480px) {
  .dorsey-title {
    font-size: var(--font-size-28);
    line-height: var(--line-height-36);
  }

  .dorsey-video .video-container {
    height: 250px;
  }

  .devices-title {
    font-size: var(--font-size-28);
    line-height: var(--line-height-36);
  }

  .devices-description p {
    font-size: var(--font-size-18);
    line-height: var(--line-height-24);
  }

  .device-image {
    height: 250px;
  }

  .device-image-placeholder {
    font-size: 60px;
  }
}

/* Testimonials styling */
.testimonials-container {
  max-width: 1200px; /* Consistent with diabrowser card container standards */
  margin: var(--dia-space-15) auto; /* Match spacing between major sections */
  padding: 0 var(--dia-space-9);
  position: relative;
  z-index: 10;
  width: 100%;
  text-align: center; /* Center title like other sections */
}

.testimonials-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 540px;
  margin: 0 auto;
}

.testimonials-pill-container {
  display: flex;
  justify-content: center;
}

.testimonials-pill {
  border: 1px solid var(--color-grey-25);
  padding: 4px 16px;
  border-radius: 8px;
  font-size: var(--font-size-14);
  color: var(--color-grey-25);
}

.testimonials-title {
  font-size: var(--font-size-20);
  line-height: var(--line-height-28);
  font-weight: 700;
  text-align: center;
  letter-spacing: -0.5px;
  margin-top: var(--dia-space-8);
  color: var(--color-grey-25);
}

.testimonials-subtitle {
  text-align: center;
  margin-top: var(--dia-space-8);
  opacity: 0.75;
  color: var(--color-grey-63);
  font-size: var(--font-size-14);
}

.testimonials-columns-wrapper {
  display: flex;
  justify-content: center;
  gap: var(--dia-space-9);
  margin-top: var(--dia-space-14); /* Match spacing from other sections */
  height: 540px;
  overflow: hidden;
  /* The mask image effect */
  mask-image: linear-gradient(to bottom, transparent, black 10%, black 90%, transparent);
  -webkit-mask-image: linear-gradient(to bottom, transparent, black 10%, black 90%, transparent);
}

/* Hide secondary columns on mobile/smaller screens */
.testimonial-column-md, 
.testimonial-column-lg {
  display: none;
}

/* Media queries for responsive testimonials */
@media (min-width: 640px) {
  .testimonials-title {
    font-size: var(--font-size-24);
    line-height: var(--line-height-32);
  }
}

@media (min-width: 768px) {
  .testimonial-column-md {
    display: block;
  }
  
  .testimonials-title {
    font-size: var(--font-size-30);
    line-height: var(--line-height-38);
  }
}

@media (min-width: 1024px) {
  .testimonial-column-lg {
    display: block;
  }
  
  .testimonials-title {
    font-size: var(--font-size-36);
    line-height: var(--line-height-44);
  }
}

@media (min-width: 1280px) {
  .testimonials-title {
    font-size: var(--font-size-48);
    line-height: var(--line-height-56);
  }
}
