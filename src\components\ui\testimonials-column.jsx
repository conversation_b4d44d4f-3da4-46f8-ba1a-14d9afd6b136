import React from 'react';
import PropTypes from 'prop-types';
import './testimonials-column.css';

// Helper function to format testimonial text while preserving React components
const formatTestimonialText = (text) => {
  if (!text) return null;
  
  // Split by ** markers to identify bold sections
  const parts = text.split(/(\*\*.*?\*\*)/g);
  
  return parts.map((part, i) => {
    // Check if this part is wrapped in ** markers
    if (part.startsWith('**') && part.endsWith('**')) {
      // Extract content between ** markers and wrap in strong
      const content = part.substring(2, part.length - 2);
      return <strong key={i} className="keyword-highlight">{content}</strong>;
    }
    // Regular text
    return part;
  });
};

const TestimonialsColumn = ({ testimonials, className, duration = 20 }) => {
  // Create two sets of testimonials for the infinite scroll effect
  const duplicatedTestimonials = [...testimonials, ...testimonials];
  
  // Calculate the CSS animation duration based on prop
  const animationStyle = {
    '--scroll-duration': `${duration}s`
  };
  
  return (
    <div 
      className={`testimonial-column ${className || ''}`} 
      style={animationStyle}
    >
      <div className="testimonial-column-inner animate-scroll">
        {duplicatedTestimonials.map((testimonial, index) => (
          <div key={index} className="testimonial-card">
            <p className="testimonial-text">{formatTestimonialText(testimonial.text)}</p>
            <div className="testimonial-author">
              <div className="testimonial-author-image">
                <img src={testimonial.image} alt={testimonial.name} />
              </div>
              <div className="testimonial-author-info">
                <div className="testimonial-author-name">{testimonial.name}</div>
                <div className="testimonial-author-role">{testimonial.role}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

TestimonialsColumn.propTypes = {
  testimonials: PropTypes.arrayOf(
    PropTypes.shape({
      text: PropTypes.string.isRequired,
      image: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      role: PropTypes.string.isRequired,
    })
  ).isRequired,
  className: PropTypes.string,
  duration: PropTypes.number,
};

export default TestimonialsColumn;
