import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook for managing carousel timer with viewport detection
 * @param {number} slideCount - Total number of slides
 * @param {number} duration - Duration per slide in milliseconds (default: 10000)
 * @returns {Object} Timer state and controls
 */
export const useCarouselTimer = (slideCount, duration = 10000) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isInView, setIsInView] = useState(false);
  
  const intervalRef = useRef(null);
  const startTimeRef = useRef(null);
  const pausedTimeRef = useRef(0);
  const carouselRef = useRef(null);

  // Intersection Observer for viewport detection
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { threshold: 0.5 }
    );

    if (carouselRef.current) {
      observer.observe(carouselRef.current);
    }

    return () => {
      if (carouselRef.current) {
        observer.unobserve(carouselRef.current);
      }
    };
  }, []);

  // Start/pause timer based on viewport visibility
  useEffect(() => {
    if (isInView && !isPlaying) {
      startTimer();
    } else if (!isInView && isPlaying) {
      pauseTimer();
    }
  }, [isInView]);

  // Timer logic
  const updateProgress = useCallback(() => {
    if (!startTimeRef.current) return;
    
    const elapsed = Date.now() - startTimeRef.current + pausedTimeRef.current;
    const newProgress = Math.min((elapsed / duration) * 100, 100);
    
    setProgress(newProgress);
    
    if (newProgress >= 100) {
      // Move to next slide
      setCurrentSlide(prev => (prev + 1) % slideCount);
      resetTimer();
    }
  }, [duration, slideCount]);

  const startTimer = useCallback(() => {
    if (isPlaying) return;
    
    setIsPlaying(true);
    startTimeRef.current = Date.now();
    
    intervalRef.current = setInterval(updateProgress, 50); // Update every 50ms for smooth animation
  }, [isPlaying, updateProgress]);

  const pauseTimer = useCallback(() => {
    if (!isPlaying) return;
    
    setIsPlaying(false);
    
    if (startTimeRef.current) {
      pausedTimeRef.current += Date.now() - startTimeRef.current;
    }
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [isPlaying]);

  const resetTimer = useCallback(() => {
    setProgress(0);
    pausedTimeRef.current = 0;
    startTimeRef.current = null;

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Keep playing state true if in view for continuous animation
    if (isInView) {
      setIsPlaying(false); // Briefly set to false to trigger restart
      setTimeout(() => {
        startTimer();
      }, 50); // Shorter delay for smoother transition
    } else {
      setIsPlaying(false);
    }
  }, [isInView, startTimer]);

  // Manual navigation
  const goToSlide = useCallback((slideIndex) => {
    if (slideIndex >= 0 && slideIndex < slideCount) {
      setCurrentSlide(slideIndex);
      resetTimer();
    }
  }, [slideCount, resetTimer]);

  const nextSlide = useCallback(() => {
    goToSlide((currentSlide + 1) % slideCount);
  }, [currentSlide, slideCount, goToSlide]);

  const prevSlide = useCallback(() => {
    goToSlide(currentSlide === 0 ? slideCount - 1 : currentSlide - 1);
  }, [currentSlide, slideCount, goToSlide]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    currentSlide,
    progress,
    isPlaying,
    isInView,
    carouselRef,
    goToSlide,
    nextSlide,
    prevSlide,
    startTimer,
    pauseTimer,
    resetTimer
  };
};
