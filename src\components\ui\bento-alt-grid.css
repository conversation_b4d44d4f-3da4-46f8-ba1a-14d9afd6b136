/* Bento Alt Grid Styles */
.bento-alt-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--dia-space-6);
  width: 100%;
  margin-top: var(--dia-space-12);
  margin-bottom: var(--dia-space-13);
}

/* Responsive grid layout */
@media (min-width: 800px) {
  .bento-alt-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--dia-space-6);
  }
}

@media (min-width: 1000px) {
  .bento-alt-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--dia-space-6);
  }
}
