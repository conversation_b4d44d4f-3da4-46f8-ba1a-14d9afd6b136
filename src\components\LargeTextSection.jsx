import React from "react";
import "./LargeTextSection.css";
import { RevealText } from "./TextRevealByWord";

const LargeTextSection = ({ children, className = "" }) => {
  return (
    <section className={`large-text-section ${className}`}>
      <div className="container">
        <div className="large-text-container">
          <div className="large-text-content section-subheading text-center">
            {children}
          </div>
        </div>
      </div>
    </section>
  );
};

export default LargeTextSection;
