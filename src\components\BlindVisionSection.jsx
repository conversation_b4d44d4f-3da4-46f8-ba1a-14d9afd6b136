import React from "react";
import "./BlindVisionSection.css";
import FadeIn from "./animations/FadeIn";
import ScaleIn from "./animations/ScaleIn";
import { Carousel, Card } from "./ui/FunnelVisionCardsCarousel";

// Content component for carousel cards
const CardContent = ({ title, description }) => {
  return (
    <div className="content-block">
      <p className="content-text">
        <span className="content-highlight">{title}</span>{" "}
        {description}
      </p>
    </div>
  );
};

const BlindVisionSection = () => {
  // Define cards data for the vision features
  const visionFeatureCardsData = [
    {
      category: "Google Ads",
      title: "Reactive visual descriptions",
      src: "https://images.unsplash.com/photo-1609250291996-fdebe6020a8f?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3",
      content: <CardContent 
        title="Dynamic visual awareness" 
        description="It reacts to changes in the visual world, like describing what it sees as the camera view moves. The system continuously analyzes the environment and provides contextual information in real-time." 
      />
    },
    {
      category: "YouTube Ads",
      title: "Works with existing tools",
      src: "https://images.unsplash.com/photo-1526570207772-784d36084510?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3",
      content: <CardContent 
        title="Seamless compatibility" 
        description="The prototype works with Google products like Maps, Photos and Lens to accurately identify objects and the environment they appear in, creating a cohesive experience for users." 
      />
    },
    {
      category: "AI Discovery",
      title: "Designed for accessibility",
      src: "https://images.unsplash.com/photo-1617791160505-6f00504e3519?q=80&w=2069&auto=format&fit=crop&ixlib=rb-4.0.3",
      content: <CardContent 
        title="User-centric design" 
        description="Developed with input from the blind and low-vision community, the interface provides audio descriptions that are clear, concise, and contextually relevant to users' needs." 
      />
    }
  ];
  
  const visionFeatureCards = visionFeatureCardsData.map((card, index) => (
    <Card key={card.title} card={card} index={index} />
  ));

  return (
    <section className="blind-vision-section">
      <div className="dia-container-cards">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="blind-vision-header">
            <h2 className="section-heading text-center">
              Your Complete Profit-First Search Marketing Suite
            </h2>
            <div className="section-description text-center">
              <p>
                FunnelVision transforms how DTC brands approach digital advertising. Instead of isolated channel strategies, we build integrated search ecosystems where every platform works together to maximize profit.
              </p>
            </div>
          </div>
        </FadeIn>

        <div className="blind-vision-features">
          <ScaleIn className="delay-200">
            <div className="vision-carousel-wrapper">
              <Carousel items={visionFeatureCards} />
            </div>
          </ScaleIn>
        </div>

        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-400">
          <div className="blind-vision-partnership">
            <h3 className="section-subheading text-center">
              Your campaigns benefit from our partnerships:
            </h3>
          </div>
        </FadeIn>

        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-500">
          <div className="blind-vision-cta-section">
            <div className="blind-vision-cta-description">
              <p className="section-subheading-description">
                We've partnered with Formen Norden Ecom Labs-the premier e-commerce research lab-to provide our clients with the latest and most impactful ROI-focused research, best practices, industry developments, and actionable strategies.
              </p>
            </div>
            <a href="#waitlist" className="btn btn-primary blind-vision-cta button-hover">
              Secure your spot
              <svg
                width="18"
                height="18"
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="btn-icon"
              >
                <path
                  d="M14.4912 14.7393H3.99121V4.23926H9.24121V2.73926H3.99121C3.59339 2.73926 3.21186 2.89729 2.93055 3.1786C2.64925 3.4599 2.49121 3.84143 2.49121 4.23926V14.7393C2.49121 15.1371 2.64925 15.5186 2.93055 15.7999C3.21186 16.0812 3.59339 16.2393 3.99121 16.2393H14.4912C15.3162 16.2393 15.9912 15.5643 15.9912 14.7393V9.48926H14.4912V14.7393ZM10.7412 2.73926V4.23926H13.4337L6.06121 11.6118L7.11871 12.6693L14.4912 5.29676V7.98926H15.9912V2.73926H10.7412Z"
                  fill="white"
                />
              </svg>
            </a>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default BlindVisionSection;
