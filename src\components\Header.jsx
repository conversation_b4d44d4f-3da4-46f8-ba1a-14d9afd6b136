import React, { useState, useEffect, useRef } from "react";
import "./Header.css";
import FadeIn from "./animations/FadeIn";
import funnelVisionLogo from "../assets/funnelvision-logo.svg";

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  const [showSecondaryMenu, setShowSecondaryMenu] = useState(false);
  const [activeLink, setActiveLink] = useState('');
  const [activeSection, setActiveSection] = useState('');
  
  // Track previous scroll position to detect direction
  const [prevScrollY, setPrevScrollY] = useState(0);
  const [visible, setVisible] = useState(true);
  
  // Mega menu state
  const [megaMenuOpen, setMegaMenuOpen] = useState(false);
  const megaMenuRef = useRef(null);
  
  // Mobile menu state
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileMegaMenuOpen, setMobileMegaMenuOpen] = useState(false);
  
  // Effect for scroll detection with reliable scroll direction detection
  useEffect(() => {
    let lastScrollY = window.scrollY;
    let ticking = false;
    
    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          
          // Determine scroll direction with a threshold to prevent tiny movements
          if (currentScrollY < lastScrollY - 10) {
            // Scrolling UP by at least 10px - show the header
            setVisible(true);
            // Dispatch custom event for header visibility change
            window.dispatchEvent(new CustomEvent('headerVisibilityChange', { detail: { visible: true } }));
          } else if (currentScrollY > lastScrollY + 10) {
            // Scrolling DOWN by at least 10px - hide the header
            setVisible(false);
            // Dispatch custom event for header visibility change
            window.dispatchEvent(new CustomEvent('headerVisibilityChange', { detail: { visible: false } }));
          }
          
          // Update the last scroll position
          lastScrollY = currentScrollY;
          ticking = false;
        });
        
        ticking = true;
      }
      
      // Apply scrolled style for background
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
      
      // Show secondary menu after scrolling past intro
      if (window.scrollY > 200) {
        setShowSecondaryMenu(true);
      } else {
        setShowSecondaryMenu(false);
      }
      
      // Determine active section based on scroll position
      const sections = [
        { id: 'enhancing', offset: 600 },
        { id: 'capabilities', offset: 1200 },
        { id: 'vision', offset: 1800 },
        { id: 'devices', offset: 2400 },
        { id: 'responsibility', offset: 3000 },
        { id: 'faq', offset: 3600 }
      ];
      
      for (let i = sections.length - 1; i >= 0; i--) {
        if (window.scrollY >= sections[i].offset) {
          setActiveSection(sections[i].id);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Function to handle link click and set active state
  const handleLinkClick = (linkId) => {
    setActiveLink(linkId);
  };
  
  // Scroll to section when clicking a menu item
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 120, // Offset for header height
        behavior: 'smooth'
      });
      setActiveSection(sectionId);
    }
  };

  // Handle logo click - go to home page or scroll to top
  const handleLogoClick = (e) => {
    e.preventDefault();

    // Check if we're on the home page (no hash or #top)
    const isHomePage = window.location.pathname === '/' &&
                      (!window.location.hash || window.location.hash === '#top');

    if (isHomePage) {
      // Already on home page, scroll to top
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
      setActiveSection('home');
    } else {
      // Navigate to home page
      window.location.href = '/';
    }
  };
  
  // Close the mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (mobileMenuOpen && !event.target.closest('.mobile-nav') && !event.target.closest('.hamburger-menu')) {
        setMobileMenuOpen(false);
        setMobileMegaMenuOpen(false);
        document.body.style.overflow = ''; // Re-enable scrolling
      }
    };

    // Close mobile menu on window resize if width > 1024px
    const handleResize = () => {
      if (window.innerWidth > 1024 && mobileMenuOpen) {
        setMobileMenuOpen(false);
        setMobileMegaMenuOpen(false);
        document.body.style.overflow = ''; // Re-enable scrolling
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('resize', handleResize);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
    };
  }, [mobileMenuOpen]);
  
  // Close mobile menu and reset mobile mega menu
  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    setMobileMegaMenuOpen(false);
    document.body.style.overflow = ''; // Re-enable scrolling
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
    if (mobileMegaMenuOpen) {
      setMobileMegaMenuOpen(false);
    }
    // Prevent scrolling on body when menu is open
    document.body.style.overflow = !mobileMenuOpen ? 'hidden' : '';
    
    // Force layout recalculation
    if (!mobileMenuOpen) {
      // Small delay to ensure CSS transitions work properly
      setTimeout(() => {
        const mobileNav = document.querySelector('.mobile-nav');
        if (mobileNav) {
          mobileNav.style.display = 'block';
        }
      }, 10);
    }
  };
  
  // Toggle mobile mega menu
  const toggleMobileMegaMenu = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setMobileMegaMenuOpen(!mobileMegaMenuOpen);
  };
  
  // Effect to ensure mobile mega menu fully expands and displays correctly
  useEffect(() => {
    if (mobileMegaMenuOpen) {
      // Ensure menu is fully visible after state update
      requestAnimationFrame(() => {
        const mobileMenu = document.querySelector('.mobile-mega-menu');
        if (mobileMenu) {
          mobileMenu.style.display = 'block';
          mobileMenu.style.opacity = '1';
          mobileMenu.style.height = 'auto';
        }
      });
    }
  }, [mobileMegaMenuOpen]);

  return (
    <header className={`header ${scrolled ? 'scrolled' : ''} ${!visible ? 'hidden' : ''} ${mobileMenuOpen ? 'mobile-menu-open' : ''}`}>
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="header-logo">
            <a href="/" onClick={handleLogoClick} className="logo-container">
              <img
                src={funnelVisionLogo}
                alt="FunnelVision Logo"
                className="header-logo-image"
              />
            </a>
          </div>
          
          {/* Hamburger Menu Button */}
          <button className="hamburger-menu" onClick={toggleMobileMenu}>
            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
          </button>
          
          {/* Desktop Navigation */}
          <nav className="header-nav">
            <a
              href="#top"
              className={`nav-link ${activeLink === 'home' ? 'active' : ''}`}
              onClick={() => handleLinkClick('home')}
            >
              Home
            </a>

            {/* Resource Center hidden per request */}

            <a
              href="#problem"
              className={`nav-link ${activeLink === 'overview' ? 'active' : ''}`}
              onClick={() => handleLinkClick('overview')}
            >
              Overview
            </a>
            <a
              href="#solutions"
              className={`nav-link ${activeLink === 'solutions' ? 'active' : ''}`}
              onClick={() => handleLinkClick('solutions')}
            >
              Services
            </a>
            <a
              href="#faq"
              className={`nav-link ${activeLink === 'faq' ? 'active' : ''}`}
              onClick={() => handleLinkClick('faq')}
            >
              FAQs
            </a>

            {/* Removed redundant CTA button */}
          </nav>
          <div className="header-actions">
            <a href="/book-a-call" className="btn btn-primary button-hover" onClick={(e) => {
              e.preventDefault();
              window.history.pushState({}, '', '/book-a-call');
              window.location.reload();
            }}>
              <span className="cta-text">Get in Touch</span>
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="cta-arrow"
              >
                <path
                  d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z"
                  fill="currentColor"
                />
              </svg>
            </a>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`mobile-nav ${mobileMenuOpen ? 'open' : ''}`}>
          <nav className="mobile-nav-links">
            <a
              href="#top"
              className={`mobile-nav-link ${activeLink === 'home' ? 'active' : ''}`}
              onClick={() => {
                handleLinkClick('home');
                setMobileMenuOpen(false);
              }}
            >
              Home
            </a>
            
            {/* Resource Center hidden per request */}
            
            <a
              href="#problem"
              className={`mobile-nav-link ${activeLink === 'overview' ? 'active' : ''}`}
              onClick={() => {
                handleLinkClick('overview');
                setMobileMenuOpen(false);
              }}
            >
              Overview
            </a>
            
            <a
              href="#solutions"
              className={`mobile-nav-link ${activeLink === 'solutions' ? 'active' : ''}`}
              onClick={() => {
                handleLinkClick('solutions');
                setMobileMenuOpen(false);
              }}
            >
              Services
            </a>
            
            <a
              href="#faq"
              className={`mobile-nav-link ${activeLink === 'faq' ? 'active' : ''}`}
              onClick={() => {
                handleLinkClick('faq');
                setMobileMenuOpen(false);
              }}
            >
              FAQs
            </a>
            
            {/* Mobile CTA Button with secondary style */}
            <a href="/book-a-call" className="btn btn-primary button-hover mobile-cta-button" onClick={(e) => {
              e.preventDefault();
              window.history.pushState({}, '', '/book-a-call');
              window.location.reload();
            }}>
              <span className="cta-text">Get in Touch</span>
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="cta-arrow"
              >
                <path
                  d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z"
                  fill="currentColor"
                />
              </svg>
            </a>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
