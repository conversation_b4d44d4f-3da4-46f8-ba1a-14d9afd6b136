/*
 * Problem Comparison Component - ROAS vs PAX
 * Implements side-by-side comparison with responsive design
 * Uses diabrowser design tokens for consistency
 */

.problem-comparison-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-16); /* Appropriate spacing between ROAS and PAX sections */
  margin: 0 auto;
}

/* Individual layout sections */
.problem-layout {
  display: flex;
  align-items: stretch;
  gap: var(--dia-space-6); /* Reasonable gap for 50/50 layout */
  width: 100%;
}

/* Ensure FadeIn containers don't break flex layout */
.problem-layout > * {
  flex: 1; /* Each direct child (FadeIn wrapper) gets equal space */
  display: flex; /* Make FadeIn wrapper also flex */
}

/* ROAS Layout - Title left, Card right */
.roas-layout {
  flex-direction: row;
}

/* PAX Layout - Card left, Title right */
.pax-layout {
  flex-direction: row-reverse;
}

/* Title section styling - 50/50 split */
.problem-title-section {
  flex: 1; /* Back to flex: 1 for 50% */
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--dia-space-6);
}

/* Apply section heading classes directly - remove custom styles */

/* Card styling - 50/50 split */
.problem-card {
  flex: 1; /* Back to flex: 1 for 50% */
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background);
  border-radius: var(--card-border-radius);
  border: var(--card-border);
  padding: var(--dia-space-10);
  box-shadow: var(--card-box-shadow);
  transition: var(--card-transition);
  display: flex;
  flex-direction: column;
}

.problem-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
}

/* Both ROAS and PAX cards now have white background */
.roas-card {
  background-color: var(--dia-color-card-background);
}

.pax-card {
  background-color: var(--dia-color-card-background); /* Changed to white */
}

.problem-card-title {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-4xl);
  font-weight: var(--dia-font-weight-medium);
  line-height: var(--line-height-tight);
  margin-bottom: var(--dia-space-10); /* Increased spacing after title */
  text-align: left;
  letter-spacing: 0;
}

/* Items container - More generous spacing */
.problem-items {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-6); /* Increased gap between items */
}

/* Individual problem item - More generous spacing with white background */
.problem-item {
  display: flex;
  align-items: flex-start;
  gap: var(--dia-space-5); /* Increased gap between icon and text */
  padding: var(--dia-space-4) var(--dia-space-4); /* Added horizontal padding */
  border-radius: var(--radius-standard);
  background-color: white; /* Pure white background */
  border: 1px solid var(--dia-color-card-border); /* Subtle border */
  margin-bottom: var(--dia-space-2); /* Small margin between items */
  transition: all 0.2s ease;
  cursor: default;
}

.problem-item:hover {
  background-color: white;
  border-color: var(--dia-color-card-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.problem-icon {
  flex-shrink: 0;
  width: 28px; /* Increased from 20px */
  height: 28px; /* Increased from 20px */
  margin-top: 1px; /* Slight alignment adjustment */
  transition: all 0.2s ease;
}

.problem-icon svg {
  width: 100%;
  height: 100%;
  transition: all 0.2s ease;
}

/* Negative items (ROAS problems) */
.problem-item.negative .problem-icon {
  color: #ef4444; /* Red color for X icons */
}

.problem-item.negative:hover .problem-icon {
  color: #dc2626; /* Darker red on hover */
  transform: scale(1.1);
}

/* Positive items (PAX solutions) */
.problem-item.positive .problem-icon {
  color: #22c55e; /* Green color for check icons */
}

.problem-item.positive:hover .problem-icon {
  color: #16a34a; /* Darker green on hover */
  transform: scale(1.1);
}

.problem-text {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-sm);
  font-weight: var(--dia-font-weight-normal);
  line-height: var(--line-height-base);
  flex: 1;
}

/* Responsive Design */

/* Tablet breakpoint - 800px */
@media (min-width: 800px) {
  .problem-card-title {
    font-size: var(--dia-font-size-4xl);
  }

  .problem-text {
    font-size: var(--dia-font-size-base);
  }

  .problem-card {
    padding: var(--dia-space-14); /* Even more generous padding on tablet+ */
  }

  .problem-layout {
    gap: var(--dia-space-6); /* Reasonable gap on tablet+ */
  }
}

/* Desktop breakpoint - 1000px */
@media (min-width: 1000px) {
  .problem-comparison-container {
    gap: var(--dia-space-16); /* Maximum spacing on desktop */
  }

  .problem-layout {
    gap: var(--dia-space-6); /* Reasonable spacing on desktop */
  }
}

/* Mobile responsive - below 800px */
@media (max-width: 799px) {
  .problem-layout {
    flex-direction: column !important;
    gap: var(--dia-space-6);
  }

  /* Reset flex properties on mobile for FadeIn containers */
  .problem-layout > * {
    flex: none !important; /* Reset FadeIn wrapper flex */
    display: block !important; /* Reset to block on mobile */
  }

  /* Reset flex properties on mobile for content containers */
  .problem-title-section {
    flex: none !important; /* Reset flex properties */
    padding: var(--dia-space-4);
    text-align: center;
    width: 100%; /* Full width on mobile */
  }

  .problem-card {
    flex: none !important; /* Reset flex properties */
    padding: var(--dia-space-8);
    width: 100%; /* Full width on mobile */
  }

  .problem-comparison-container {
    gap: var(--dia-space-10); /* Reduced spacing between sections on mobile */
  }

  .problem-items {
    gap: var(--dia-space-4); /* Reduced item spacing on mobile */
  }

  .problem-item {
    padding: var(--dia-space-3) var(--dia-space-4); /* Maintain horizontal padding */
    margin-left: var(--dia-space-2); /* Add left margin for icons */
    margin-right: var(--dia-space-2); /* Add right margin for icons */
  }

  .problem-card-title {
    font-size: var(--dia-font-size-lg);
    text-align: center;
    margin-bottom: var(--dia-space-6);
  }
}

/* Extra small mobile - below 480px */
@media (max-width: 479px) {
  .problem-comparison-container {
    gap: var(--dia-space-6);
  }

  .problem-layout {
    gap: var(--dia-space-3); /* Minimal gap to prevent breaking */
  }

  .problem-title-section {
    padding: var(--dia-space-3);
  }

  .problem-card {
    padding: var(--dia-space-6); /* Reduced padding on small mobile */
  }

  .problem-item {
    padding: var(--dia-space-2) var(--dia-space-3); /* Reduced padding */
    margin-left: var(--dia-space-3); /* More margin for icons */
    margin-right: var(--dia-space-3);
  }

  .problem-icon {
    width: 24px; /* Keep icons readable */
    height: 24px;
  }
}
