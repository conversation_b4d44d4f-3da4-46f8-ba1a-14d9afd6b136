import React from 'react';
import './testimonials-column.css';

export interface Testimonial {
  text: string;
  image: string;
  name: string;
  role: string;
}

interface TestimonialsColumnProps {
  testimonials: Testimonial[];
  className?: string;
  duration?: number;
}

const formatTestimonialText = (text: string) => {
  if (!text) return null;
  const parts = text.split(/(\*\*.*?\*\*)/g);
  return parts.map((part, i) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      const content = part.substring(2, part.length - 2);
      return (
        <strong key={i} className="keyword-highlight">
          {content}
        </strong>
      );
    }
    return part;
  });
};

const TestimonialsColumn: React.FC<TestimonialsColumnProps> = ({
  testimonials,
  className = '',
  duration = 20,
}) => {
  const duplicatedTestimonials = [...testimonials, ...testimonials];
  const animationStyle = {
    '--scroll-duration': `${duration}s`,
  } as React.CSSProperties;

  return (
    <div className={`testimonial-column ${className}`} style={animationStyle}>
      <div className="testimonial-column-inner animate-scroll">
        {duplicatedTestimonials.map((testimonial, index) => (
          <div key={index} className="testimonial-card">
            <p className="testimonial-text">
              {formatTestimonialText(testimonial.text)}
            </p>
            <div className="testimonial-author">
              <div className="testimonial-author-image">
                <img src={testimonial.image} alt={testimonial.name} />
              </div>
              <div className="testimonial-author-info">
                <div className="testimonial-author-name">
                  {testimonial.name}
                </div>
                <div className="testimonial-author-role">
                  {testimonial.role}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TestimonialsColumn;
