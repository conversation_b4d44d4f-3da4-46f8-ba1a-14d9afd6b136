/* Google Ads Card Styles */
.google-ads-card-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--dia-space-6);
  width: 100%;
}

.google-ads-card {
  position: relative;
  cursor: pointer;
  transition: all var(--transition-base);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.google-ads-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.google-ads-card:focus-visible {
  outline: 2px solid var(--dia-color-text-primary);
  outline-offset: 2px;
}

/* Image Container - Matches carousel dimensions */
.google-ads-card-image-container {
  position: relative;
  width: fit-content;
  height: fit-content;
  max-width: 1000px;
  max-height: 750px;
  overflow: hidden;
  border-radius: var(--radius-lg);
}

.google-ads-card-image {
  display: block;
  width: 900px; /* Matches carousel image width */
  height: auto; /* Maintains natural aspect ratio */
  object-fit: cover;
  object-position: center;
  transition: transform var(--transition-base);
}

.google-ads-card:hover .google-ads-card-image {
  transform: scale(1.02);
}

/* CTA Button - Bottom Right Corner */
.google-ads-card-cta {
  position: absolute;
  bottom: var(--dia-space-4); /* 12px from bottom */
  right: var(--dia-space-4); /* 12px from right */
  background-color: var(--dia-color-text-primary);
  color: var(--dia-color-background);
  padding: var(--dia-space-2) var(--dia-space-3); /* 8px vertical, 10px horizontal */
  border-radius: var(--radius-md); /* 8px border radius */
  display: flex;
  align-items: center;
  gap: var(--dia-space-2); /* 8px between text and icon */
  font-size: var(--dia-font-size-sm); /* 14px */
  font-weight: var(--dia-font-weight-medium); /* 500 */
  transition: all var(--transition-base);
  box-shadow: var(--shadow-md);
}

.google-ads-card:hover .google-ads-card-cta {
  background-color: var(--dia-color-text-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.google-ads-card-cta-text {
  white-space: nowrap;
}

.google-ads-card-cta-icon {
  transition: transform var(--transition-base);
  flex-shrink: 0;
}

.google-ads-card:hover .google-ads-card-cta-icon {
  transform: translate(1px, -1px);
}

/* Loading State */
.google-ads-card-loading {
  width: 900px;
  height: 675px; /* Maintains aspect ratio for 900px width */
  display: flex;
  align-items: center;
  justify-content: center;
}

.google-ads-card-skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--dia-color-background-overlay) 25%,
    var(--dia-color-card-border) 50%,
    var(--dia-color-background-overlay) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-lg);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 1000px) {
  .google-ads-card-image {
    width: 700px;
  }

  .google-ads-card-loading {
    width: 700px;
    height: 525px;
  }
}

@media (max-width: 800px) {
  .google-ads-card-image {
    width: 600px;
  }

  .google-ads-card-loading {
    width: 600px;
    height: 450px;
  }
}

@media (max-width: 640px) {
  .google-ads-card-image {
    width: 500px;
  }

  .google-ads-card-loading {
    width: 500px;
    height: 375px;
  }
}

@media (max-width: 480px) {
  .google-ads-card-container {
    gap: var(--dia-space-4);
  }

  .google-ads-card-image {
    width: 400px;
  }

  .google-ads-card-loading {
    width: 400px;
    height: 300px;
  }

  .google-ads-card-overlay-content {
    font-size: var(--dia-font-size-base);
  }
}
