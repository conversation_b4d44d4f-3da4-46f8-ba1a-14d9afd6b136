import React, { useEffect } from 'react';
import ReactD<PERSON> from 'react-dom';
import './MicrosoftAdsModal.css';

/**
 * MicrosoftAdsModal component - Full-screen overlay modal for Microsoft Ads service details
 * Features:
 * - Full-screen overlay covering entire viewport
 * - Responsive layout: horizontal split (desktop/tablet) vs vertical stack (mobile)
 * - Left/Top section: Overview content
 * - Right/Bottom section: Features list
 * - Click overlay background, ESC key, or close button to close
 * - Smooth open/close animations
 * - Uses diabrowser design tokens for consistency
 */
const MicrosoftAdsModal = ({ 
  isOpen, 
  onClose, 
  serviceName = "Microsoft Ads"
}) => {
  // Handle escape key, body scroll lock, and cleanup
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Lock body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // Restore body scroll when modal closes
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click to close
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const modalContent = (
    <div 
      className="microsoft-ads-modal-backdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="microsoft-ads-modal-title"
    >
      <div className="microsoft-ads-modal-panel">
        {/* Overview Section */}
        <div className="microsoft-ads-modal-overview">
          <div className="microsoft-ads-modal-content">
            <h2 id="microsoft-ads-modal-title" className="microsoft-ads-modal-title">
              Microsoft Ads
            </h2>
            <h3 className="microsoft-ads-modal-subtitle">
              Quiet profit channel most brands ignore
            </h3>
            <div className="microsoft-ads-modal-description">
              <p>
                We unlock incremental revenue across Bing, Yahoo & AOL Search, Microsoft Audience Network and Copilot answers—often at 30-60% lower CPCs than Google.
              </p>
              <p>
                Our campaigns mirror your best-converting Google structures, then layer in device, demographic and LinkedIn profile targeting unique to Microsoft. Result: fresh customers, higher profit density, zero cannibalisation.
              </p>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="microsoft-ads-modal-features">
          <div className="microsoft-ads-modal-content">
            <h3 className="microsoft-ads-modal-features-title">What's Included</h3>
            <div className="microsoft-ads-modal-features-list">
              <div className="microsoft-ads-modal-feature-item">
                <span className="microsoft-ads-modal-checkmark">✓</span>
                <span>Campaign Migration & Setup</span>
              </div>
              <div className="microsoft-ads-modal-feature-item">
                <span className="microsoft-ads-modal-checkmark">✓</span>
                <span>LinkedIn Profile Targeting</span>
              </div>
              <div className="microsoft-ads-modal-feature-item">
                <span className="microsoft-ads-modal-checkmark">✓</span>
                <span>Multi-Network Optimization</span>
              </div>
              <div className="microsoft-ads-modal-feature-item">
                <span className="microsoft-ads-modal-checkmark">✓</span>
                <span>Demographic & Device Targeting</span>
              </div>
              <div className="microsoft-ads-modal-feature-item">
                <span className="microsoft-ads-modal-checkmark">✓</span>
                <span>Copilot Integration Strategy</span>
              </div>
              <div className="microsoft-ads-modal-feature-item">
                <span className="microsoft-ads-modal-checkmark">✓</span>
                <span>Cross-Platform Performance Reports</span>
              </div>
            </div>
          </div>
        </div>

        {/* Close Button */}
        <button
          className="microsoft-ads-modal-close"
          onClick={onClose}
          aria-label="Close modal"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
  );

  // Render modal to document.body using React Portal
  return ReactDOM.createPortal(modalContent, document.body);
};

export default MicrosoftAdsModal;
