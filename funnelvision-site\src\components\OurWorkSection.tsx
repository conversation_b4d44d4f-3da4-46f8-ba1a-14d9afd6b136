import React from 'react';
import FadeIn from './animations/FadeIn';
import ScaleIn from './animations/ScaleIn';
import CaseStudyCard from './ui/CaseStudyCard';
import './OurWorkSection.css';

const OurWorkSection: React.FC = () => {
  const caseStudyCardsData = [
    {
      image: '/images/case-studies/case-1.webp',
      logo: '/images/logos/boulies-logo.svg',
      title: 'Cut wasted ad spend by 57% & lifted Profit by 2.4×.',
      ctaText: 'Read case',
      ctaLink: '#case-study-boulies',
    },
    {
      image: '/images/case-studies/case-2.webp',
      logo: '/images/logos/bunkie-life-logo.svg',
      title: 'Conversions up 59% while monthly spend fell 57%',
      ctaText: 'Read case',
      ctaLink: '#case-study-bunkie-life',
    },
    {
      image: '/images/case-studies/case-3.webp',
      logo: '/images/logos/kodiak-logo.svg',
      title: 'Eliminated 48% of low-profit spend and 8.4×-ed monthly revenue',
      ctaText: 'Read case',
      ctaLink: '#case-study-kodiak',
    },
  ];

  return (
    <div className="devices-section">
      <div className="dia-container-cards">
        <div className="ourwork-container">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
            <h2 className="section-heading text-center">Our Work</h2>
            <div className="section-description text-center">
              <p>What Happens When You Focus on Profit</p>
            </div>
          </FadeIn>
          <div className="case-studies-grid">
            <ScaleIn className="delay-200">
              <div className="case-studies-grid-wrapper">
                {caseStudyCardsData.map((card) => (
                  <CaseStudyCard key={card.title} {...card} />
                ))}
              </div>
            </ScaleIn>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OurWorkSection;
