import React from "react";
import "./PrivacyPolicy.css";
import FadeIn from "./animations/FadeIn";

const PrivacyPolicy = () => {
  return (
    <div className="privacy-policy-page">
      {/* Header Section */}
      <div className="privacy-header">
        <div className="container">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
            <div className="privacy-header-content">
              <h1 className="section-heading text-center">Privacy Policy</h1>
              <div className="section-subheading-description text-center">
                <p>
                  Your privacy is important to us. This policy explains how we collect, 
                  use, and protect your information when you use our services.
                </p>
              </div>
              <div className="privacy-meta text-center">
                <p className="privacy-last-updated">Last updated: January 1, 2025</p>
                <div className="privacy-navigation">
                  <a
                    href="/"
                    className="back-to-home-link"
                    onClick={(e) => {
                      e.preventDefault();
                      window.history.pushState({}, '', '/');
                      window.location.reload();
                    }}
                  >
                    ← Back to Home
                  </a>
                </div>
              </div>
            </div>
          </FadeIn>
        </div>
      </div>

      {/* Content Section */}
      <div className="privacy-content">
        <div className="container">
          <div className="privacy-content-wrapper">
            
            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
              <section className="privacy-section">
                <h2 className="section-subheading">Information We Collect</h2>
                <div className="privacy-text">
                  <p>
                    We collect information you provide directly to us, such as when you create an account, 
                    subscribe to our newsletter, or contact us for support. This may include:
                  </p>
                  <ul>
                    <li>Name and email address</li>
                    <li>Company information and website details</li>
                    <li>Communication preferences</li>
                    <li>Any other information you choose to provide</li>
                  </ul>
                </div>
              </section>
            </FadeIn>

            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-200">
              <section className="privacy-section">
                <h2 className="section-subheading">How We Use Your Information</h2>
                <div className="privacy-text">
                  <p>
                    We use the information we collect to provide, maintain, and improve our services. 
                    Specifically, we may use your information to:
                  </p>
                  <ul>
                    <li>Provide and deliver the services you request</li>
                    <li>Send you technical notices and support messages</li>
                    <li>Communicate with you about products, services, and events</li>
                    <li>Monitor and analyze trends and usage</li>
                    <li>Personalize and improve your experience</li>
                  </ul>
                </div>
              </section>
            </FadeIn>

            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-300">
              <section className="privacy-section">
                <h2 className="section-subheading">Information Sharing</h2>
                <div className="privacy-text">
                  <p>
                    We do not sell, trade, or otherwise transfer your personal information to third parties 
                    without your consent, except as described in this policy. We may share your information in the following circumstances:
                  </p>
                  <ul>
                    <li>With service providers who assist us in operating our business</li>
                    <li>To comply with legal obligations or protect our rights</li>
                    <li>In connection with a business transaction or reorganization</li>
                    <li>With your explicit consent</li>
                  </ul>
                </div>
              </section>
            </FadeIn>

            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-400">
              <section className="privacy-section">
                <h2 className="section-subheading">Data Security</h2>
                <div className="privacy-text">
                  <p>
                    We implement appropriate technical and organizational measures to protect your personal 
                    information against unauthorized access, alteration, disclosure, or destruction. However, 
                    no method of transmission over the internet is 100% secure.
                  </p>
                  <p>
                    We regularly review our security practices and update them as necessary to ensure 
                    the ongoing protection of your data.
                  </p>
                </div>
              </section>
            </FadeIn>

            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-500">
              <section className="privacy-section">
                <h2 className="section-subheading">Your Rights</h2>
                <div className="privacy-text">
                  <p>
                    Depending on your location, you may have certain rights regarding your personal information, including:
                  </p>
                  <ul>
                    <li>The right to access and receive a copy of your personal information</li>
                    <li>The right to rectify inaccurate personal information</li>
                    <li>The right to erase your personal information</li>
                    <li>The right to restrict processing of your personal information</li>
                    <li>The right to data portability</li>
                    <li>The right to object to processing</li>
                  </ul>
                </div>
              </section>
            </FadeIn>

            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-600">
              <section className="privacy-section">
                <h2 className="section-subheading">Cookies and Tracking</h2>
                <div className="privacy-text">
                  <p>
                    We use cookies and similar tracking technologies to collect information about your 
                    browsing activities and to provide you with a personalized experience. You can 
                    control cookies through your browser settings.
                  </p>
                  <p>
                    We may also use third-party analytics services to help us understand how our 
                    website is used and to improve our services.
                  </p>
                </div>
              </section>
            </FadeIn>

            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-700">
              <section className="privacy-section">
                <h2 className="section-subheading">Contact Us</h2>
                <div className="privacy-text">
                  <p>
                    If you have any questions about this Privacy Policy or our privacy practices, 
                    please contact us at:
                  </p>
                  <div className="contact-info">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Address:</strong> [Your Business Address]</p>
                  </div>
                  <p>
                    We will respond to your inquiry within a reasonable timeframe.
                  </p>
                </div>
              </section>
            </FadeIn>

          </div>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
