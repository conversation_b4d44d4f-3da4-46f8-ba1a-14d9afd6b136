/* Service Carousel Styles */

.service-carousel-container {
  width: fit-content;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--dia-space-6);
  outline: none;
}

.service-carousel-container:focus-visible {
  outline: 2px solid var(--dia-color-text-primary);
  outline-offset: 4px;
  border-radius: var(--radius-lg);
}

/* Image Container - Fit content but with max dimensions for consistency */
.service-carousel-image-container {
  position: relative;
  width: fit-content;
  height: fit-content;
  max-width: 1000px;  /* Generous max to allow Google Ads original size */
  max-height: 750px;  /* Generous max to allow Google Ads original size */
  overflow: hidden;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.service-carousel-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.service-carousel-image {
  display: block;
  width: 900px;  /* Fixed width for consistency - Google Ads original size */
  height: auto;
  object-fit: cover;
  object-position: center;
}

/* Text Slide Styles */
.service-carousel-text-slide {
  width: 900px;
  min-height: 675px; /* Maintains aspect ratio consistency with images */
  background-color: var(--dia-color-background-light); /* #F9F6F2 */
  border-radius: 16px; /* Site standard border radius */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--dia-space-10); /* 30px padding - reduced from 80px for more content space */
  box-sizing: border-box;
}

.service-carousel-text-content {
  max-width: 100%;
  text-align: left;
  overflow-y: auto;
  max-height: 100%;
}

.service-carousel-text-title {
  font-size: var(--dia-font-size-2xl); /* 22px - reduced from 32px */
  font-weight: var(--dia-font-weight-semibold); /* 600 */
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-4) 0; /* 12px bottom margin - reduced from 16px */
  line-height: 1.2;
}

.service-carousel-text-subtitle {
  font-size: var(--dia-font-size-lg); /* 18px - reduced from 20px */
  font-weight: var(--dia-font-weight-medium); /* 500 */
  color: var(--dia-color-text-secondary);
  margin: 0 0 var(--dia-space-6) 0; /* 16px bottom margin - reduced from 30px */
  line-height: 1.3;
}

.service-carousel-text-description {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-4); /* 12px between paragraphs - reduced from 16px */
}

.service-carousel-text-description p {
  font-size: var(--dia-font-size-sm); /* 14px - reduced from 16px */
  font-weight: var(--dia-font-weight-normal); /* 400 */
  color: var(--dia-color-text-primary);
  line-height: 1.5; /* Reduced from 1.6 for tighter spacing */
  margin: 0;
}

.service-carousel-text-list {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-4); /* 12px between items - reduced from 16px */
}

.service-carousel-text-item {
  display: flex;
  align-items: center;
  gap: var(--dia-space-3); /* 10px between checkmark and text - reduced from 12px */
  font-size: var(--dia-font-size-sm); /* 14px - reduced from 16px */
  font-weight: var(--dia-font-weight-normal); /* 400 */
  color: var(--dia-color-text-primary);
  line-height: 1.4; /* Reduced from 1.5 for tighter spacing */
}

.service-carousel-text-checkmark {
  color: var(--dia-color-teal-green); /* Brand accent color */
  font-weight: var(--dia-font-weight-semibold); /* 600 */
  font-size: var(--dia-font-size-base); /* 16px - reduced from 18px */
  flex-shrink: 0;
}

/* Loading State */
.service-carousel-loading {
  width: 900px;
  height: 675px;  /* Maintains aspect ratio for 900px width */
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-carousel-skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--dia-color-background-overlay) 25%,
    var(--dia-color-card-border) 50%,
    var(--dia-color-background-overlay) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-lg);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Navigation Container - Now positioned above carousel */
.carousel-navigation {
  display: flex;
  align-items: center;
  gap: var(--dia-space-4);
  margin-bottom: var(--dia-space-4);
}

/* Arrow Buttons */
.carousel-nav-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--dia-color-white);
  border: 1px solid var(--dia-color-grey);
  color: var(--dia-color-text-primary);
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.carousel-nav-arrow:hover {
  background-color: var(--dia-color-background-overlay);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.carousel-nav-arrow:active {
  transform: scale(0.95);
}

.carousel-nav-arrow:focus-visible {
  outline: 2px solid var(--dia-color-text-primary);
  outline-offset: 2px;
}

/* Navigation Indicators */
.carousel-nav-indicators {
  display: flex;
  align-items: center;
  gap: var(--dia-space-2);
  padding: 0 var(--dia-space-2);
}

/* Dots */
.carousel-nav-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--dia-color-grey);
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
}

.carousel-nav-dot:hover {
  background-color: var(--dia-color-text-secondary);
  transform: scale(1.2);
}

.carousel-nav-dot:focus-visible {
  outline: 2px solid var(--dia-color-text-primary);
  outline-offset: 2px;
}

/* Dash Container */
.carousel-nav-dash-container {
  position: relative;
  display: flex;
  align-items: center;
}

/* Dash Background */
.carousel-nav-dash {
  width: 24px;
  height: 8px;
  background-color: var(--dia-color-grey);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

/* Dash Fill (Timer Progress) */
.carousel-nav-dash-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: var(--dia-color-black);
  border-radius: 2px;
  transition: width 0.1s ease-out;
}

/* Debug Info (Development Only) */
.service-carousel-debug {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 1000px) {
  .service-carousel-image {
    width: 700px;
  }

  .service-carousel-loading {
    width: 700px;
    height: 525px;
  }
}

@media (max-width: 800px) {
  .service-carousel-image {
    width: 600px;
  }

  .service-carousel-loading {
    width: 600px;
    height: 450px;
  }

  .service-carousel-text-slide {
    width: 600px;
    height: 450px; /* Fixed height to match image slide dimensions exactly */
    padding: var(--dia-space-8); /* 20px padding on tablet - reduced from 50px */
  }

  .service-carousel-text-title {
    font-size: var(--dia-font-size-xl); /* 20px on tablet - reduced from 26px */
  }

  .service-carousel-text-subtitle {
    font-size: var(--dia-font-size-base); /* 16px on tablet - reduced from 18px */
  }

  .carousel-nav-arrow {
    width: 36px;
    height: 36px;
  }

  .carousel-nav-dash {
    width: 20px;
    height: 3px;
  }

  .carousel-nav-dot {
    width: 6px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .service-carousel-container {
    gap: var(--dia-space-4);
  }

  .service-carousel-image {
    width: 400px;
  }

  .service-carousel-loading {
    width: 400px;
    height: 300px;
  }

  .service-carousel-text-slide {
    width: 400px;
    height: 300px; /* Fixed height to match image slide dimensions exactly */
    padding: var(--dia-space-4); /* 12px padding on mobile - reduced from 20px */
  }

  .service-carousel-text-title {
    font-size: var(--dia-font-size-lg); /* 18px on mobile - reduced from 22px */
    margin-bottom: var(--dia-space-2); /* 8px bottom margin - reduced from 12px */
  }

  .service-carousel-text-subtitle {
    font-size: var(--dia-font-size-sm); /* 14px on mobile - reduced from 16px */
    margin-bottom: var(--dia-space-3); /* 10px bottom margin - reduced from 16px */
  }

  .service-carousel-text-description {
    gap: var(--dia-space-2); /* 8px between paragraphs on mobile - reduced from 12px */
  }

  .service-carousel-text-list {
    gap: var(--dia-space-2); /* 8px between items on mobile - reduced from 12px */
  }

  .service-carousel-text-item {
    gap: var(--dia-space-2); /* 8px between checkmark and text on mobile - reduced from 10px */
    font-size: var(--dia-font-size-xs); /* 12px on mobile - reduced from 14px */
  }

  .service-carousel-text-checkmark {
    font-size: var(--dia-font-size-sm); /* 14px on mobile - reduced from 16px */
  }

  .carousel-navigation {
    gap: var(--dia-space-3);
  }

  .carousel-nav-arrow {
    width: 32px;
    height: 32px;
  }

  .carousel-nav-indicators {
    gap: var(--dia-space-1);
  }
}
