import React, { useState, useEffect } from "react";
import "./SecondaryScrollMenu.css";

// Icon components for navigation items
const Icons = {
  Problem: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
      <polyline points="22 4 12 14.01 9 11.01"/>
    </svg>
  ),
  Solutions: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  ),
  OurWork: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <rect x="5" y="2" width="14" height="20" rx="2" ry="2"/>
      <line x1="12" y1="18" x2="12" y2="18"/>
    </svg>
  ),
  NextSteps: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
    </svg>
  )
};

const SecondaryScrollMenu = () => {
  const [visible, setVisible] = useState(false);
  const [activeSection, setActiveSection] = useState("");
  const [headerVisible, setHeaderVisible] = useState(true);

  // Listen for header visibility changes
  useEffect(() => {
    const handleHeaderVisibilityChange = (event) => {
      setHeaderVisible(event.detail.visible);
    };
    
    window.addEventListener('headerVisibilityChange', handleHeaderVisibilityChange);
    
    return () => {
      window.removeEventListener('headerVisibilityChange', handleHeaderVisibilityChange);
    };
  }, []);
  
  useEffect(() => {
    const handleScroll = () => {
      // Get the Problem and FAQ sections positions
      const problemSection = document.getElementById('problem');
      const faqSection = document.getElementById('faq');
      const nextStepsSection = document.getElementById('next-steps');
      
      if (problemSection && faqSection && nextStepsSection) {
        const problemPosition = problemSection.getBoundingClientRect().top;
        const faqPosition = faqSection.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        
        // Make menu visible only when we've scrolled to or past the Problem section
        // AND we haven't reached the FAQ section yet
        if (problemPosition <= windowHeight * 0.25 && faqPosition >= 0) {
          setVisible(true);
        } else {
          setVisible(false);
        }
      }

      // Check which section is most visible
      const sectionIds = ["problem", "solutions", "our-work", "next-steps"];
      
      // Find all sections by their IDs
      const sections = sectionIds.map(id => {
        const element = document.getElementById(id);
        return { id, element };
      }).filter(item => item.element); // Filter out any not found

      // Determine which section is most visible in the viewport
      let mostVisibleSection = null;
      let maxVisiblePercent = 0;
      
      sections.forEach(({ id, element }) => {
        const rect = element.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        
        // Calculate how much of the section is visible
        const visibleTop = Math.max(0, rect.top);
        const visibleBottom = Math.min(viewportHeight, rect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);
        
        // Calculate visible percentage relative to viewport
        const visiblePercent = visibleHeight / viewportHeight;
        
        if (visiblePercent > maxVisiblePercent) {
          maxVisiblePercent = visiblePercent;
          mostVisibleSection = id;
        }
      });
      
      if (mostVisibleSection) {
        setActiveSection(mostVisibleSection);
      }
    };

    // Call once on mount to initialize active section
    handleScroll();
    
    // Add scroll listener
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  
  // Scroll to a section smoothly
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      // Dynamically calculate offset based on header and navbar height
      const headerHeight = 64; // Header height in px
      const navbarHeight = 52; // Approximate navbar height with padding
      const scrollOffset = headerHeight + navbarHeight;
      
      window.scrollTo({
        top: element.offsetTop - scrollOffset,
        behavior: "smooth",
      });
      setActiveSection(sectionId);
    }
  };

  return (
    <div className={`secondary-scroll-menu ${visible ? "visible" : ""} ${!headerVisible ? "header-hidden" : ""}`}>
      <div className="tubelight-container">
        <button
          className={`tubelight-item ${activeSection === "problem" ? "tubelight-active" : ""}`}
          onClick={() => scrollToSection("problem")}
        >
          <span className="tubelight-text">Problem</span>
          <span className="tubelight-icon"><Icons.Problem /></span>
          {activeSection === "problem" && (
            <div className="tubelight-highlight">
              <div className="tubelight-glow">
                <div className="tubelight-glow-large" />
                <div className="tubelight-glow-medium" />
                <div className="tubelight-glow-small" />
              </div>
            </div>
          )}
        </button>
        
        <button
          className={`tubelight-item ${activeSection === "solutions" ? "tubelight-active" : ""}`}
          onClick={() => scrollToSection("solutions")}
        >
          <span className="tubelight-text">Solutions</span>
          <span className="tubelight-icon"><Icons.Solutions /></span>
          {activeSection === "solutions" && (
            <div className="tubelight-highlight">
              <div className="tubelight-glow">
                <div className="tubelight-glow-large" />
                <div className="tubelight-glow-medium" />
                <div className="tubelight-glow-small" />
              </div>
            </div>
          )}
        </button>
        
        <button
          className={`tubelight-item ${activeSection === "our-work" ? "tubelight-active" : ""}`}
          onClick={() => scrollToSection("our-work")}
        >
          <span className="tubelight-text">Our Work</span>
          <span className="tubelight-icon"><Icons.OurWork /></span>
          {activeSection === "our-work" && (
            <div className="tubelight-highlight">
              <div className="tubelight-glow">
                <div className="tubelight-glow-large" />
                <div className="tubelight-glow-medium" />
                <div className="tubelight-glow-small" />
              </div>
            </div>
          )}
        </button>
        
        <button
          className={`tubelight-item ${activeSection === "next-steps" ? "tubelight-active" : ""}`}
          onClick={() => scrollToSection("next-steps")}
        >
          <span className="tubelight-text">Get Started</span>
          <span className="tubelight-icon"><Icons.NextSteps /></span>
          {activeSection === "next-steps" && (
            <div className="tubelight-highlight">
              <div className="tubelight-glow">
                <div className="tubelight-glow-large" />
                <div className="tubelight-glow-medium" />
                <div className="tubelight-glow-small" />
              </div>
            </div>
          )}
        </button>
      </div>
    </div>
  );
};

export default SecondaryScrollMenu;
