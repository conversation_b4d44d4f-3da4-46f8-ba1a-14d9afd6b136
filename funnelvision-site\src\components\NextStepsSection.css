/* Next Steps Section styles */
.next-steps-section {
  padding: var(--dia-space-14) 0;
  width: 100%;
  overflow: hidden;
  background-color: var(--dia-color-background); /* Use site background #F8F8F8 */
}

/* Diabrowser migration: when .dia-typography is applied */
.next-steps-section.dia-typography {
  padding: var(--dia-space-12) 0;
  background-color: var(--dia-color-background);
}


/* Header and title styling */
.nextsteps-header {
  text-align: center;
  margin-bottom: var(--dia-space-14);
}

/* Diabrowser migration overrides */
.next-steps-section.dia-typography .nextsteps-header {
  margin-bottom: var(--dia-space-12);
}

.nextsteps-title {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-80);
  font-weight: 700;
  line-height: var(--line-height-96);
  letter-spacing: var(--letter-spacing-tight);
  margin: 0 0 var(--dia-space-11) 0;
  text-align: center;
}

.nextsteps-description {
  margin: 0 auto;
}

.nextsteps-description p {
  color: var(--color-grey-63);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-28);
  font-weight: 400;
  line-height: var(--line-height-36);
  margin: 0;
  text-align: center;
}

/* CTA section styling */
.cta-section-header {
  text-align: center;
  margin-top: var(--dia-space-15);
  margin-bottom: var(--dia-space-12);
}

/* Diabrowser migration overrides */
.next-steps-section.dia-typography .cta-section-header {
  margin-top: var(--dia-space-15);
  margin-bottom: var(--dia-space-12);
}

.cta-section-title {
  font-family: var(--font-family-inter);
  font-size: var(--font-size-subheading-mobile); /* 24px on mobile */
  font-weight: var(--font-weight-subheading); /* 600 */
  line-height: var(--line-height-subheading); /* 1.3 */
  color: var(--color-text-primary);
  margin: 0 0 var(--dia-space-2) 0;
  text-align: center;
}

.cta-container {
  display: flex;
  justify-content: center;
  margin-top: var(--dia-space-6);
}

/* Numbered Steps Layout */
.numbered-steps-container {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-11); /* Smaller gap between steps */
  margin: var(--dia-space-6) 0;
}

.numbered-step {
  display: flex;
  align-items: flex-start;
  gap: var(--dia-space-6); /* Smaller gap between number and content */
}

.step-number {
  flex-shrink: 0;
  width: var(--dia-space-12); /* 24px - much smaller */
  height: var(--dia-space-12);
  background-color: var(--dia-color-text-primary);
  color: var(--dia-color-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-18); /* Smaller font */
  font-weight: 600;
  line-height: 1;
}

.step-content {
  flex: 1;
}

.step-title {
  font-family: var(--font-family-inter);
  font-size: var(--dia-bento-title-size); /* Smaller title size */
  font-weight: 600; /* Medium weight */
  line-height: var(--dia-bento-title-line-height);
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-3) 0; /* Smaller margin */
}

.step-description {
  font-family: var(--font-family-inter);
  font-size: var(--dia-bento-description-size); /* Smaller description text */
  font-weight: var(--dia-bento-description-weight); /* Normal weight */
  line-height: var(--dia-bento-description-line-height); /* Tighter line height */
  color: var(--dia-color-text-secondary);
  margin: 0;
}

/* Responsive layout for numbered steps */
@media (min-width: 1000px) {
  .numbered-steps-container {
    flex-direction: row;
    gap: var(--dia-space-8); /* Smaller gap between columns */
    align-items: flex-start;
  }

  .numbered-step {
    flex: 1;
    flex-direction: column;
    text-align: left;
    gap: var(--dia-space-6); /* Smaller gap between number and content */
  }

  .step-number {
    align-self: flex-start;
  }
}

/* Responsive layout for numbered steps */
@media (min-width: 800px) {
  .numbered-steps-container {
    flex-direction: row;
    gap: var(--dia-space-8); /* Smaller gap between columns */
    align-items: flex-start;
  }

  .numbered-step {
    flex: 1;
    flex-direction: column;
    text-align: left;
    gap: var(--dia-space-6); /* Smaller gap between number and content */
  }

  .step-number {
    align-self: flex-start;
  }
}

/*
 * NEXT STEPS CTA BUTTONS - Using Unified CTA System
 * Consistent with hero section and site-wide standards
 */

/* NextSteps buttons extend the universal CTA system */
.btn {
  /* Use CTA base styling */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--cta-gap);
  padding: var(--cta-padding-mobile);
  border-radius: var(--cta-border-radius);
  height: var(--cta-height);
  min-width: var(--cta-min-width-mobile);
  cursor: pointer;
  position: relative;
  text-align: center;
  flex-shrink: 0;
  text-decoration: none;
  border: none;
  font-family: var(--font-family-inter);
  font-size: var(--dia-btn-cta-size);
  font-weight: var(--dia-btn-cta-weight);
  line-height: var(--dia-btn-cta-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  transition: var(--cta-transition);
  white-space: nowrap;
  outline: none;
}

.btn-primary {
  background: var(--cta-primary-background);
  color: var(--cta-primary-text-color);
  box-shadow: var(--cta-primary-shadow);
}

.btn-primary:hover {
  background: var(--cta-primary-background-hover);
  box-shadow: var(--cta-primary-shadow-hover);
  transform: var(--cta-hover-transform);
}

/* NextSteps CTA arrow animations */
.btn:hover .cta-arrow {
  transform: var(--cta-arrow-hover-transform);
}

.btn:hover .cta-text {
  transform: var(--cta-text-hover-transform);
}

/* NextSteps responsive sizing */
@media (min-width: 600px) {
  .btn {
    min-width: var(--cta-min-width-tablet);
    padding: var(--cta-padding-tablet);
  }
}

@media (min-width: 800px) {
  .btn {
    min-width: var(--cta-min-width-desktop);
    padding: var(--cta-padding-desktop);
  }
}

@media (min-width: 1000px) {
  .btn {
    min-width: var(--cta-min-width-large);
    padding: var(--cta-padding-large);
  }
}

.button-hover {
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

.button-hover:hover {
  transform: translateY(-2px);
}

.btn-icon {
  display: inline-block;
  transition: transform 0.3s ease;
}

.btn:hover .btn-icon {
  transform: translateX(4px);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .nextsteps-title {
    font-size: var(--font-size-64);
    line-height: var(--line-height-72);
  }
  
  .cta-section-title {
    font-size: var(--font-size-subheading-tablet); /* 28px on tablet */
  }
}

@media (max-width: 768px) {
  .nextsteps-title {
    font-size: var(--font-size-48);
    line-height: var(--line-height-56);
  }
  
  .nextsteps-description p {
    font-size: var(--font-size-20);
    line-height: var(--line-height-28);
  }
  
  .cta-section-title {
    font-size: var(--font-size-subheading-tablet); /* 28px on tablet */
  }
}

/* Desktop */
@media (min-width: 1920px) {
  .cta-section-title {
    font-size: var(--font-size-subheading-desktop); /* 32px on desktop */
  }
}

@media (max-width: 480px) {
  .nextsteps-title {
    font-size: var(--font-size-36);
    line-height: var(--line-height-44);
  }
  
  .nextsteps-description p {
    font-size: var(--font-size-18);
    line-height: var(--line-height-26);
  }
  
  .cta-section-title {
    font-size: var(--font-size-subheading-mobile); /* 24px on mobile */
  }
  
  .btn {
    padding: 12px 24px;
    font-size: var(--font-size-14);
  }
}
