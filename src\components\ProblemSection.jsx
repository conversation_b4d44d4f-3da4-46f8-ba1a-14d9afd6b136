import React from "react";
import "./ProblemSection.css";
import FadeIn from "./animations/FadeIn";
import { BentoAltGrid, BentoAltCard } from "./ui/bento-alt-grid";

const ProblemSection = () => {
  // Define bento-alt cards data
  const bentoAltCardsData = [
    {
      icon: () => (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M9 12l2 2 4-4"/>
          <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
        </svg>
      ),
      description: "**Only 16% of brands** know true profit per SKU.",
      revealText: "Most teams still see revenue, not real margin. When shipping, payment fees and returns hide inside 'overhead,' bids are blindfolded. We factor those costs to every order so you finally see contribution at the SKU level, and scale what actually makes money."
    },
    {
      icon: () => (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      ),
      description: "**56%** of product searches **start on Google.**",
      revealText: "Shoppers may start on Meta, but the sale still comes home to Google. We sync your profit-structured data to those discovery channels, then retarget the click back through high-intent Google campaigns."
    },
    {
      icon: () => (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
        </svg>
      ),
      description: "CAC has climbed **60% in five years.**",
      revealText: "Ad inflation turns a 'good' 3× ROAS into shrinking cash flow. PAX fights back with margin-based bidding, SKU pruning and payback-window targets that keep acquisition costs under control, even when clicks get pricier."
    }
  ];

  return (
    <div className="capabilities-section">
      <div className="dia-container-cards">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="capabilities-header">
            <h2 className="section-heading text-center">
              Why Most DTC Brands Waste 30% of Their Ad Spend
            </h2>
            <div className="section-description text-center">
              <p>
                Following vanity metrics can hide serious profit leaks.
              </p>
            </div>
          </div>
        </FadeIn>

        {/* Bento Alt cards grid */}
        <div className="capabilities-content">
          <BentoAltGrid>
            {bentoAltCardsData.map((card, index) => (
              <FadeIn key={index} threshold={0.1} rootMargin="0px" className={`fade-up delay-${(index + 1) * 100}`}>
                <BentoAltCard
                  icon={card.icon}
                  description={card.description}
                  revealText={card.revealText}
                />
              </FadeIn>
            ))}
          </BentoAltGrid>
        </div>



      </div>
    </div>
  );
};

export default ProblemSection;
