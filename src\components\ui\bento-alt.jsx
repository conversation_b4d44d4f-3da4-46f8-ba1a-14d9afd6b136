import React, { useState } from 'react';
import './bento-alt.css';

const BentoAltCard = ({ icon: Icon, description, revealText }) => {
  const [isRevealed, setIsRevealed] = useState(false);

  const toggleReveal = () => {
    setIsRevealed(!isRevealed);
  };

  // Function to render text with bold formatting
  const renderTextWithBold = (text) => {
    // Split text by ** markers and apply bold to text between them
    const parts = text.split(/\*\*(.*?)\*\*/g);
    return parts.map((part, index) => {
      // Every odd index (1, 3, 5...) is the text that was between ** **
      if (index % 2 === 1) {
        return <strong key={index}>{part}</strong>;
      }
      return part;
    });
  };

  const handleCardClick = (e) => {
    // Don't toggle if clicking on the toggle button itself
    if (e.target.closest('.bento-alt-toggle') || e.target.closest('.bento-alt-overlay-toggle')) {
      return;
    }
    toggleReveal();
  };

  return (
    <div className="bento-alt-card" onClick={handleCardClick}>
      {/* Icon */}
      <div className="bento-alt-icon-container">
        <Icon />
      </div>

      {/* Content */}
      <div className="bento-alt-content">
        <div className="bento-alt-description">
          {renderTextWithBold(description)}
        </div>

        {/* Toggle button container for right alignment */}
        <div className="bento-alt-toggle-container">
          <button
            className={`bento-alt-toggle ${isRevealed ? 'active' : ''}`}
            onClick={toggleReveal}
            aria-label={isRevealed ? 'Hide details' : 'Show details'}
          >
            <svg
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
        </div>
      </div>

      {/* Overlay */}
      <div className={`bento-alt-overlay ${isRevealed ? 'active' : ''}`} onClick={handleCardClick}>
        <div className="bento-alt-overlay-content">
          {renderTextWithBold(revealText)}
        </div>

        {/* Overlay toggle button container for right alignment */}
        <div className="bento-alt-overlay-toggle-container">
          <button
            className="bento-alt-overlay-toggle"
            onClick={toggleReveal}
            aria-label="Hide details"
          >
            <svg
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default BentoAltCard;
