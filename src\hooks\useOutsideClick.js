import { useEffect } from 'react';

/**
 * Hook that alerts when you click outside of the specified element
 * @param {React.RefObject} ref - React ref object for the element to monitor clicks outside of
 * @param {Function} handler - Callback function to execute when click outside is detected
 */
export const useOutsideClick = (ref, handler) => {
  useEffect(() => {
    /**
     * Alert if clicked on outside of element
     */
    function handleClickOutside(event) {
      if (ref.current && !ref.current.contains(event.target)) {
        handler();
      }
    }

    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler]);
};
