import React from 'react';
import './CaseStudyCard.css';

interface CaseStudyCardProps {
  image: string;
  logo: string;
  title: string;
  ctaText?: string;
  ctaLink?: string;
  className?: string;
}

const CaseStudyCard: React.FC<CaseStudyCardProps> = ({
  image,
  logo,
  title,
  ctaText = 'Read case',
  ctaLink = '#',
  className = '',
}) => (
  <div className={`case-study-card ${className}`}>
    <div className="case-study-card-background" style={{ backgroundImage: `url(${image})` }}>
      <div className="case-study-card-overlay">
        <div className="case-study-card-content">
          {logo && (
            <div className="case-study-card-logo">
              <img src={logo} alt="Company logo" />
            </div>
          )}
          <h3 className="case-study-card-title">{title}</h3>
          <a href={ctaLink} className="case-study-card-cta call-to-action-container call-to-action-secondary">
            <span className="button-text button-text-secondary">{ctaText}</span>
          </a>
        </div>
      </div>
    </div>
  </div>
);

export default CaseStudyCard;
