import React from "react";

/**
 * Sample logos data for use in the logo carousel
 * Each logo has a unique ID, name, and image component
 */

// Create SVG wrapper components for each logo image
const createSVGComponent = (id, name) => {
  // This creates a functional component that renders the SVG from the public directory
  return (props) => (
    <img 
      src={`/images/logos/${id}.svg`} 
      alt={name} 
      {...props} 
    />
  );
};

// Define logo objects with component references
export const logos = [
  { id: 1, name: "Apple", img: createSVGComponent(1, "Apple") },
  { id: 2, name: "Supabase", img: createSVGComponent(2, "Supabase") },
  { id: 3, name: "Vercel", img: createSVGComponent(3, "Vercel") },
  { id: 4, name: "<PERSON><PERSON>", img: createSVGComponent(4, "<PERSON><PERSON>") },
  { id: 5, name: "<PERSON>", img: createSVGComponent(5, "<PERSON>") },
  { id: 6, name: "<PERSON>", img: createSVGComponent(6, "<PERSON>") },
  { id: 7, name: "<PERSON>", img: createSVGComponent(7, "<PERSON>") },
  { id: 8, name: "<PERSON>", img: createSV<PERSON>omponent(8, "Claude") },
  { id: 9, name: "Nextjs", img: createSVGComponent(9, "Nextjs") },
  { id: 10, name: "Tailwind", img: createSVGComponent(10, "Tailwind") },
  { id: 11, name: "Logo11", img: createSVGComponent(11, "Logo11") },
  { id: 12, name: "Logo12", img: createSVGComponent(12, "Logo12") },
];

// Export sampleLogos for compatibility with other components
export const sampleLogos = logos;
