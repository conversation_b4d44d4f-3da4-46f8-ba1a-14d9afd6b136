/* Logo Carousel Section Styles */
.logo-carousel-section {
  width: 100%;
  max-width: 100%;
  padding: var(--dia-space-14) 0;
  background-color: var(--color-background, #FFFFFF);
  overflow: hidden;
}

.logo-carousel-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
  text-align: center;
}

.logo-carousel-heading {
  margin-bottom: var(--dia-space-6);
  font-family: var(--font-family-inter);
  letter-spacing: -0.015em; /* Adjusted for Inter font as noted in memory */
  color: var(--color-text-primary);
}

.logo-carousel-description {
  margin: 0 auto var(--dia-space-12);
  max-width: 600px;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-lg);
  line-height: 1.6;
  color: var(--color-text-secondary, #525252);
}

.logo-carousel-wrapper {
  margin: var(--dia-space-8) 0;
  padding: var(--dia-space-6);
  display: flex;
  justify-content: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .logo-carousel-section {
    padding: var(--dia-space-8) 0;
  }

  .logo-carousel-description {
    font-size: var(--font-size-base);
    margin-bottom: var(--dia-space-8);
  }
  
  .logo-carousel-wrapper {
    margin: 1rem 0;
    padding: 1rem;
  }
}
