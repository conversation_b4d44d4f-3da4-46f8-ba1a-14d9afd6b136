.capabilities-section {
  padding: var(--dia-space-14) 0;
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-secondary);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Diabrowser migration: when .dia-typography and .dia-spacing are applied */
.capabilities-section.dia-typography.dia-spacing {
  padding: var(--dia-space-12) 0;
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-secondary);
}

.capabilities-header {
  text-align: center;
  margin-bottom: var(--dia-space-12);
  /* Remove custom max-width - let container handle it */
}

/* Diabrowser migration overrides */
.capabilities-section.dia-spacing .capabilities-header {
  margin-bottom: var(--dia-space-12);
}

.capabilities-section.dia-spacing .capabilities-content {
  margin-bottom: var(--dia-space-12);
}

.capabilities-section.dia-spacing .natural-interaction-carousel {
  margin-top: var(--dia-space-10);
  margin-bottom: var(--dia-space-12);
  padding: var(--dia-space-8) 0;
}

/* capabilities-main-title now uses section-heading class styles */
/* capabilities-description now uses section-description class styles */

/* Video section */
.capabilities-video {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--dia-space-12);
  padding: 0 clamp(var(--dia-space-2), 5%, var(--dia-space-10));
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--dia-space-6);
  margin-top: var(--dia-space-8);
}

/* Statistical Cards Grid */
.statistical-cards-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--dia-space-6);
  margin-top: var(--dia-space-12);
  margin-bottom: var(--dia-space-13);
  width: 100%;
  /* Remove custom max-width and padding - let container handle it */
}

/* Responsive grid layout */
@media (min-width: 800px) {
  .statistical-cards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--dia-space-8);
  }
}

@media (min-width: 1000px) {
  .statistical-cards-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--dia-space-10);
  }
}

/* Generic video container is removed - we're using scoped containers instead */
/* See capabilities-video .video-container below */

.capabilities-video .video-container {
  width: 100%;
  max-width: 1070px;
  height: 601px;
  border-radius: var(--radius-lg);
  background-color: var(--color-background-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-base), box-shadow var(--transition-base);
}

.capabilities-video .video-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.capabilities-video .video-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    ellipse at center,
    rgba(66, 133, 244, 0.05) 0%,
    rgba(248, 249, 250, 0.4) 70%
  );
}

.capabilities-video .play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-primary);
  cursor: pointer;
  transition: transform 0.2s;
}

.capabilities-video .play-button:hover {
  transform: scale(1.1);
}

/* Content sections */
.capabilities-content {
  margin-bottom: var(--dia-space-12);
}

.capabilities-section-header {
  text-align: center;
  margin-bottom: var(--dia-space-12); /* Standardized spacing */
  /* Remove custom max-width - let container handle it */
}

/* capabilities-section-title now uses section-subheading class */

/* capabilities-section-description now uses section-subheading-description class */

/* Grid layouts */
.capabilities-grid {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: var(--dia-space-10);
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 2px;
}

.capabilities-grid--two-column {
  max-width: 1300px;
}

/* Enhanced capability cards with consistent light mode styling */
.capability-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 389px;
  margin-top: 0;
  outline: none;
  -webkit-tap-highlight-color: transparent;
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background);
  border-radius: var(--card-border-radius);
  padding: var(--dia-space-4);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
  transition: var(--card-transition);
}

.capability-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

.capability-card--offset {
  margin-top: var(--dia-space-12); /* Using diabrowser spacing token for offset effect */
}

.capability-image {
  margin-bottom: var(--dia-space-6);
  width: 393px;
}

/* Specific adjustment for context aware dialogue card */
.context-dialogue-card {
  margin-top: 0;
}

.capability-image-placeholder {
  width: 393px;
  height: 189px;
  border-radius: 24px;
  background-color: #f1f3f4;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: #202124;
  font-size: 18px;
  overflow: hidden;
}

.capability-image-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 24px;
}

.capability-image-placeholder::before {
  content: "";
  position: absolute;
  inset: 39px;
  border-radius: 24px;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.3s, transform 0.3s;
  z-index: 1;
  pointer-events: none;
}

.capability-card:hover .capability-image-placeholder::before {
  opacity: 1;
  transform: scale(1);
}

.capability-content {
  text-align: center;
  padding: 0 var(--dia-space-8);
}

.capability-title {
  /* Using diabrowser typography tokens */
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-2xl);
  font-weight: var(--dia-font-weight-medium);
  line-height: var(--dia-line-height-base);
  margin: 0 0 var(--dia-space-3) 0;
  color: var(--dia-color-text-primary);
}

.capability-description {
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-lg);
  font-weight: var(--dia-font-weight-normal);
  line-height: var(--dia-line-height-relaxed);
  color: var(--dia-color-text-secondary);
  margin: 0;
}

/* Video content blocks */
.content-block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--item-spacing-24) var(--item-spacing-40);
}

.content-text {
  color: #202124;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-24);
  line-height: var(--line-height-32);
  text-align: center;
  margin-bottom: var(--item-spacing-24);
}

.content-highlight {
  font-weight: 700;
  color: #202124;
}

.content-description {
  color: #5f6368;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-18);
  line-height: var(--line-height-24);
  text-align: center;
  max-width: 640px;
}

/* Super resolution demo */
.super-resolution-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: var(--item-spacing-40);
}

.super-resolution-toggle {
  display: flex;
  align-items: center;
  margin-bottom: var(--item-spacing-24);
}

.super-resolution-toggle-label {
  font-family: var(--font-family-inter);
  font-size: var(--font-size-16);
  color: #5f6368;
  margin: 0 var(--item-spacing-12);
}

.super-resolution-toggle-button {
  width: 44px;
  height: 24px;
  background-color: var(--color-primary);
  border-radius: 12px;
  position: relative;
  cursor: pointer;
}

.super-resolution-toggle-button::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
}

.super-resolution-toggle-button.active::after {
  transform: translateX(20px);
}

.super-resolution-images {
  display: flex;
  gap: var(--dia-space-9);
}

.super-resolution-image {
  width: 300px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.super-resolution-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.super-resolution-image-label {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: var(--dia-space-2) 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-14);
  text-align: center;
}

/* Typography handled by diabrowser tokens - no hardcoded overrides needed */

/* Responsive layout using diabrowser breakpoints */
@media (max-width: 1000px) {
  .capabilities-grid {
    flex-direction: column;
    align-items: center;
    gap: var(--dia-space-10);
  }

  .capability-card--offset {
    margin-top: 0;
  }

  .capabilities-video .video-container {
    height: auto;
    aspect-ratio: 16/9;
  }
}

@media (max-width: 800px) {
  .capabilities-section {
    padding: var(--dia-space-12) 0;
  }

  .capability-card {
    width: 100%;
    max-width: 389px;
  }

  .capability-image-placeholder {
    width: 100%;
    height: auto;
    aspect-ratio: 2/1;
  }
}

@media (max-width: 480px) {
  .capabilities-section {
    padding: var(--dia-space-10) 0;
  }

  .super-resolution-images {
    flex-direction: column;
  }
}

/* Responsive heading sizes */
@media (max-width: 768px) {
  .capabilities-main-title {
    font-size: 56px !important;
    line-height: 64px !important;
  }
}

@media (max-width: 480px) {
  .capabilities-main-title {
    font-size: 44px !important;
    line-height: 52px !important;
  }
}
