/* Enhanced <PERSON><PERSON> Alt Card Styles - Consistent light mode */
.bento-alt-card {
  position: relative;
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background);
  border-radius: var(--card-border-radius);
  border: var(--card-border);
  padding: var(--bento-alt-padding-desktop);
  cursor: pointer;
  transition: var(--card-transition);
  box-shadow: var(--card-box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 375px;
}

.bento-alt-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

/* Icon container */
.bento-alt-icon-container {
  width: var(--dia-space-12);
  height: var(--dia-space-12);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dia-color-black);
  background-color: #F9F6F2;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.bento-alt-icon-container svg {
  width: 100%;
  height: 100%;
}

/* Content area */
.bento-alt-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  flex: 1;
}

/* Description text */
.bento-alt-description {
  font-family: var(--font-family-inter);
  font-size: var(--bento-alt-description-size);
  font-weight: var(--bento-alt-description-weight);
  line-height: var(--dia-line-height-relaxed);
  color: var(--dia-color-text-primary);
  margin-top: var(--bento-alt-text-margin-top);
  margin-bottom: var(--bento-alt-text-margin-bottom);
  text-align: left;
  letter-spacing: var(--bento-alt-description-letter-spacing);
}

/* Toggle container for right alignment */
.bento-alt-toggle-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

/* Toggle button - matching carousel card style */
.bento-alt-toggle {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: var(--radius-full);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease, background-color 0.2s ease;
  color: var(--dia-color-black);
  flex-shrink: 0;
  box-shadow: 0 1px 2px 0 #0000004d,0 1px 3px 1px #00000026
}

.bento-alt-toggle:hover {
  transform: scale(1.1);
  background-color: white;
  box-shadow: 0 1px 2px 0 #0000004d,0 1px 3px 1px #00000026;
}

.bento-alt-toggle svg {
  width: 16px;
  height: 16px;
  transition: transform var(--transition-base);
}

.bento-alt-toggle.active svg {
  transform: rotate(45deg);
}

/* Overlay */
.bento-alt-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--dia-color-white);
  color: var(--dia-color-text-secondary);
  display: flex;
  flex-direction: column;
  padding: var(--bento-alt-padding-desktop);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-base), visibility var(--transition-base);
  border-radius: 16px;
}

.bento-alt-overlay.active {
  opacity: 1;
  visibility: visible;
}

.bento-alt-overlay-content {
  text-align: left;
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-base);
  font-weight: var(--dia-body-text-weight);
  line-height: var(--dia-body-text-line-height);
  max-width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
}

/* Overlay toggle container */
.bento-alt-overlay-toggle-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

/* Overlay toggle button */
.bento-alt-overlay-toggle {
  background: var(--dia-color-white);
  border: none;
  border-radius: var(--radius-full);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease, background-color 0.2s ease;
  color: var(--dia-color-black);
  flex-shrink: 0;
  box-shadow: 0 1px 2px 0 #0000004d,0 1px 3px 1px #00000026;
}

.bento-alt-overlay-toggle:hover {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.3);
}

.bento-alt-overlay-toggle svg {
  width: 16px;
  height: 16px;
  transition: transform var(--transition-base);
  transform: rotate(45deg);
}

/* Toggle grey background when revealed */
.bento-alt-toggle.active {
  background-color: var(--dia-color-grey);
}

/* Responsive styles */
@media (max-width: 1000px) {
  .bento-alt-card {
    padding: var(--bento-alt-padding-tablet);
  }

  .bento-alt-overlay {
    padding: var(--bento-alt-padding-tablet);
  }
}

@media (max-width: 800px) {
  .bento-alt-card {
    padding: var(--bento-alt-padding-mobile);
    min-height: 250px;
  }

  .bento-alt-overlay {
    padding: var(--bento-alt-padding-mobile);
  }

  /* Keep text at 20px on tablet/mobile */
  .bento-alt-description {
    font-size: var(--bento-alt-description-size); /* 20px */
    letter-spacing: var(--bento-alt-description-letter-spacing);
  }

  /* Keep icon at 100px on tablet/mobile */
  .bento-alt-icon-container {
    width: var(--bento-alt-icon-size); /* 100px */
    height: var(--bento-alt-icon-size); /* 100px */
  }
}

@media (max-width: 480px) {
  .bento-alt-card {
    min-height: 220px;
  }

  /* Keep text at 20px on mobile */
  .bento-alt-description {
    font-size: var(--bento-alt-description-size-mobile); /* 20px */
  }

  /* Keep icon at 100px on mobile */
  .bento-alt-icon-container {
    width: var(--bento-alt-icon-size); /* 100px */
    height: var(--bento-alt-icon-size); /* 100px */
  }
}
