# FunnelVision Light Mode Design System

## Overview
This document outlines the comprehensive light mode design system implemented across the FunnelVision website, following UI best practices for natural lighting simulation and visual hierarchy.

## Core Philosophy
- **Light comes from above**: Lighter elements appear elevated and closer to the user
- **Subtle hierarchy**: Gentle contrast differences rather than harsh transitions  
- **Shadow creates depth**: Realistic lighting with two-shadow technique
- **Consistent elevation**: White cards on off-white backgrounds for perfect hierarchy

## Color Hierarchy

### Background Colors (Darkest to Lightest)
```css
--dia-color-background: #F8F8F8;           /* Site background (97% lightness) */
--dia-color-header-background: #FAFAFA;    /* Header background (98% lightness) */
--dia-color-card-background: #FFFFFF;      /* Card background (100% lightness) */
```

### Text Colors (Optimized for Comfort)
```css
--dia-color-text-primary: hsl(0, 0%, 15%);    /* Dark gray for headings */
--dia-color-text-secondary: hsl(0, 0%, 35%);  /* Medium gray for body text */
--dia-color-text-muted: hsl(0, 0%, 55%);      /* Light gray for secondary content */
--dia-color-text-light: hsl(0, 0%, 65%);      /* Very light gray for subtle text */
```

### Border Colors
```css
--dia-color-card-border: hsl(0, 0%, 88%);         /* Default card border */
--dia-color-card-border-hover: hsl(0, 0%, 85%);   /* Hover state border */
```

## Enhanced Shadow System

### Two-Shadow Technique
All shadows use a combination of dark and light shadows to simulate realistic lighting:

```css
/* Desktop Shadows */
--shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(255, 255, 255, 0.3);
--shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(255, 255, 255, 0.4);
--shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(255, 255, 255, 0.5);

/* Mobile-Optimized Shadows (Performance) */
--shadow-sm-mobile: 0 1px 2px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(255, 255, 255, 0.2);
--shadow-md-mobile: 0 1px 3px rgba(0, 0, 0, 0.06), 0 4px 8px rgba(255, 255, 255, 0.3);
```

## Highlight System

### Overhead Lighting Simulation
```css
--highlight-subtle: rgba(255, 255, 255, 0.6);
--highlight-medium: rgba(255, 255, 255, 0.8);
--highlight-strong: rgba(255, 255, 255, 1.0);
--highlight-top-border: 1px solid var(--highlight-medium);
```

## Card Component Standards

### Universal Card Styling
All cards follow consistent light mode principles:

```css
.card-base {
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background);
  border: var(--card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-box-shadow);
  transition: var(--card-transition);
}

.card-base:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}
```

### Card Types
- **Testimonial Cards**: Pure white with generous padding
- **Bento Cards**: White with subtle gradients and consistent borders
- **Feature Cards**: White backgrounds with elevation effects
- **FAQ Cards**: White with enhanced open states
- **Case Study Cards**: Full-image backgrounds with overlay consistency

## Gradient System

### Light Mode Gradients
All gradients simulate top-to-bottom lighting:

```css
--card-background-gradient: linear-gradient(180deg, #FFFFFF 0%, #FEFEFE 100%);
--card-hover-background-gradient: linear-gradient(180deg, #FFFFFF 0%, #FDFDFD 100%);
--card-elevated-background-gradient: linear-gradient(180deg, #FFFFFF 0%, #FEFEFE 50%, #FDFDFD 100%);
```

## Interactive Elements

### Button Standards
```css
.btn-enhanced {
  box-shadow: var(--shadow-sm);
  transition: var(--card-transition);
}

.btn-enhanced:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
}
```

### Navigation Elements
- Header: Semi-transparent elevated background with subtle shadows
- Nav links: Hover states with background overlays and micro-animations
- Mobile navigation: Consistent with desktop principles

## Responsive Considerations

### Mobile Optimization
- Lighter shadows for better performance on mobile devices
- Consistent visual hierarchy across all breakpoints
- Maintained elevation logic on all screen sizes

### Breakpoint Strategy
- Mobile (< 800px): Optimized shadows and spacing
- Tablet (800px+): Full shadow system activation
- Desktop (1000px+): Enhanced effects and larger shadows

## Implementation Guidelines

### For New Components
1. **Always start with card base styles**
2. **Use consistent shadow tokens**
3. **Implement hover states with elevation changes**
4. **Follow the gradient direction (top-to-bottom)**
5. **Add top highlights for interactive elements**

### Code Standards
```css
/* ✅ Good - Uses design system tokens */
.new-component {
  background: var(--card-background-gradient);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
  transition: var(--card-transition);
}

/* ❌ Bad - Hardcoded values */
.new-component {
  background: white;
  border: 1px solid #ccc;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
```

## Future-Proofing

### Token Structure
All light mode tokens are centralized in `src/styles/funnelvision.css` for easy maintenance and updates.

### Extensibility
The system supports:
- Additional shadow levels (--shadow-xl, --shadow-xxl)
- New highlight variations
- Extended gradient options
- Component-specific overrides

### Maintenance
- Regular audits of color contrast ratios
- Performance monitoring of shadow effects
- Consistency checks across new components
- User feedback integration for comfort improvements

## Testing Checklist

### Visual Verification
- [ ] Proper elevation hierarchy (lighter = higher)
- [ ] Consistent shadow directions
- [ ] Smooth hover transitions
- [ ] Readable text contrast
- [ ] Cohesive color temperature

### Performance Verification  
- [ ] Mobile shadow optimization active
- [ ] Smooth animations on all devices
- [ ] No visual glitches during interactions
- [ ] Consistent rendering across browsers

### Accessibility Verification
- [ ] Sufficient color contrast ratios
- [ ] Clear focus indicators
- [ ] Readable text at all sizes
- [ ] Consistent interactive feedback

---

*This design system ensures a cohesive, comfortable, and visually logical light mode experience that follows natural lighting principles while maintaining excellent performance across all devices.*
