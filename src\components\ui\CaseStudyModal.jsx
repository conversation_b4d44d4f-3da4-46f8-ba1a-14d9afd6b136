import React, { useEffect } from 'react';
import ReactDOM from 'react-dom';
import './CaseStudyModal.css';

/**
 * CaseStudyModal component - Full-screen overlay modal for case studies
 * Features:
 * - Full-screen overlay covering entire viewport
 * - Responsive layout: horizontal split (desktop/tablet) vs vertical stack (mobile)
 * - Left/Top section: Scrollable text content
 * - Right/Bottom section: Case study image + close button
 * - Click overlay background or close button to close
 * - Smooth open/close animations
 * - Uses diabrowser design tokens for consistency
 */
const CaseStudyModal = ({ 
  isOpen, 
  onClose, 
  caseStudyData, 
  image, 
  logo 
}) => {
  // Handle escape key, body scroll lock, and cleanup
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Lock body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // Restore body scroll when modal closes
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click to close
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Handle panel click to prevent event bubbling
  const handlePanelClick = (e) => {
    e.stopPropagation();
  };

  if (!isOpen || !caseStudyData) return null;

  // Render modal using React Portal to document.body
  return ReactDOM.createPortal(
    <div
      className="case-study-modal-backdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
    >
      <div
        className="case-study-modal-panel"
        onClick={handlePanelClick}
      >
        {/* Image Section - Right on desktop/tablet, Top on mobile */}
        <div className="case-study-modal-image-section">
          <div
            className="case-study-modal-image"
            style={{ backgroundImage: `url(${image})` }}
          >
          </div>
        </div>

        {/* Content Section - Left on desktop/tablet, Bottom on mobile */}
        <div className="case-study-modal-content">
          <div className="case-study-modal-content-inner">
            {/* Header with logo and category */}
            <div className="case-study-modal-header">
              {logo && (
                <div className="case-study-modal-logo">
                  <img src={logo} alt="Company logo" />
                </div>
              )}
              {caseStudyData.category && (
                <h4 className="case-study-modal-category">
                  {caseStudyData.category}
                </h4>
              )}
            </div>

            {/* Main content */}
            <div className="case-study-modal-body">
              {caseStudyData.sections && caseStudyData.sections.map((section, index) => (
                <div key={index} className="case-study-modal-section">
                  {section.title && (
                    <h3 className="case-study-modal-section-title">
                      {section.title}
                    </h3>
                  )}
                  {section.content && (
                    <div className="case-study-modal-section-content">
                      {section.content.map((paragraph, pIndex) => (
                        <p key={pIndex} className="case-study-modal-paragraph">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  )}
                  {section.table && (
                    <div className="case-study-modal-table-container">
                      <table className="case-study-modal-table">
                        <thead>
                          <tr>
                            {section.table.headers.map((header, hIndex) => (
                              <th key={hIndex}>{header}</th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {section.table.rows.map((row, rIndex) => (
                            <tr key={rIndex}>
                              {row.map((cell, cIndex) => (
                                <td key={cIndex}>{cell}</td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                  {section.testimonial && (
                    <blockquote className="case-study-modal-testimonial">
                      "{section.testimonial}"
                    </blockquote>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Close button positioned in bottom-right corner of modal */}
        <button
          className="case-study-modal-close"
          onClick={onClose}
          aria-label="Close case study"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18 6L6 18M6 6L18 18"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>,
    document.body
  );
};

export default CaseStudyModal;
