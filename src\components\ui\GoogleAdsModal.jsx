import React, { useEffect } from 'react';
import ReactDOM from 'react-dom';
import './GoogleAdsModal.css';

/**
 * GoogleAdsModal component - Full-screen overlay modal for Google Ads service details
 * Features:
 * - Full-screen overlay covering entire viewport
 * - Responsive layout: horizontal split (desktop/tablet) vs vertical stack (mobile)
 * - Left/Top section: Overview content
 * - Right/Bottom section: Features list
 * - Click overlay background, ESC key, or close button to close
 * - Smooth open/close animations
 * - Uses diabrowser design tokens for consistency
 */
const GoogleAdsModal = ({ 
  isOpen, 
  onClose, 
  serviceName = "Google Ads"
}) => {
  // Handle escape key, body scroll lock, and cleanup
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Lock body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // Restore body scroll when modal closes
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click to close
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const modalContent = (
    <div 
      className="google-ads-modal-backdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="google-ads-modal-title"
    >
      <div className="google-ads-modal-panel">
        {/* Overview Section */}
        <div className="google-ads-modal-overview">
          <div className="google-ads-modal-content">
            <h2 id="google-ads-modal-title" className="google-ads-modal-title">
              Google Ads Management
            </h2>
            <h3 className="google-ads-modal-subtitle">
              Profit-First Campaign Strategy
            </h3>
            <div className="google-ads-modal-description">
              <p>
                We don't just drive traffic—we drive profitable traffic. Our Google Ads management focuses on high-margin customers and strategic campaign optimization to maximize your return on ad spend.
              </p>
              <p>
                Every campaign is built with your profit margins in mind, ensuring sustainable growth and long-term success.
              </p>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="google-ads-modal-features">
          <div className="google-ads-modal-content">
            <h3 className="google-ads-modal-features-title">What's Included</h3>
            <div className="google-ads-modal-features-list">
              <div className="google-ads-modal-feature-item">
                <span className="google-ads-modal-checkmark">✓</span>
                <span>Campaign Strategy & Setup</span>
              </div>
              <div className="google-ads-modal-feature-item">
                <span className="google-ads-modal-checkmark">✓</span>
                <span>Keyword Research & Optimization</span>
              </div>
              <div className="google-ads-modal-feature-item">
                <span className="google-ads-modal-checkmark">✓</span>
                <span>Ad Copy Creation & Testing</span>
              </div>
              <div className="google-ads-modal-feature-item">
                <span className="google-ads-modal-checkmark">✓</span>
                <span>Landing Page Optimization</span>
              </div>
              <div className="google-ads-modal-feature-item">
                <span className="google-ads-modal-checkmark">✓</span>
                <span>Conversion Tracking Setup</span>
              </div>
              <div className="google-ads-modal-feature-item">
                <span className="google-ads-modal-checkmark">✓</span>
                <span>Monthly Performance Reports</span>
              </div>
            </div>
          </div>
        </div>

        {/* Close Button */}
        <button
          className="google-ads-modal-close"
          onClick={onClose}
          aria-label="Close modal"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
  );

  // Render modal to document.body using React Portal
  return ReactDOM.createPortal(modalContent, document.body);
};

export default GoogleAdsModal;
