import React from 'react';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';
import './FadeIn.css';

/**
 * FadeIn component that animates its children into view when they intersect the viewport.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The content to be animated.
 * @param {number} [props.threshold=0.2] - A number from 0 to 1, indicating the percentage of the target's visibility
 *                                         needed to trigger the animation.
 * @param {string} [props.rootMargin='0px'] - Margin around the root. Can have values similar to the CSS margin property.
 * @param {string} [props.className] - Additional CSS class names to apply to the wrapper div.
 */
const FadeIn = ({ children, threshold = 0.2, rootMargin = '0px', className = '' }) => {
  const [ref, isVisible] = useIntersectionObserver({
    threshold,
    rootMargin,
  });

  const classNames = `fade-in ${isVisible ? 'visible' : ''} ${className}`.trim();

  return (
    <div ref={ref} className={classNames}>
      {children}
    </div>
  );
};

export default FadeIn;
