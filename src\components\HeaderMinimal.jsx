import React, { useState, useEffect } from "react";
import "./Header.css";
import funnelVisionLogo from "../assets/funnelvision-logo.svg";

const HeaderMinimal = () => {
  const [scrolled, setScrolled] = useState(false);
  const [visible, setVisible] = useState(true);

  // Effect for scroll detection
  useEffect(() => {
    let lastScrollY = window.scrollY;
    let ticking = false;
    
    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          
          // Determine scroll direction with a threshold to prevent tiny movements
          if (currentScrollY < lastScrollY - 10) {
            // Scrolling UP by at least 10px - show the header
            setVisible(true);
          } else if (currentScrollY > lastScrollY + 10) {
            // Scrolling DOWN by at least 10px - hide the header
            setVisible(false);
          }
          
          // Update the last scroll position
          lastScrollY = currentScrollY;
          ticking = false;
        });
        
        ticking = true;
      }
      
      // Apply scrolled style for background
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle logo click - go to home page
  const handleLogoClick = (e) => {
    e.preventDefault();
    // Navigate to home page
    window.location.href = '/';
  };

  return (
    <header className={`header ${scrolled ? 'scrolled' : ''} ${!visible ? 'hidden' : ''}`}>
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="header-logo">
            <a href="/" onClick={handleLogoClick} className="logo-container">
              <img
                src={funnelVisionLogo}
                alt="FunnelVision Logo"
                className="header-logo-image"
              />
            </a>
          </div>
        </div>
      </div>
    </header>
  );
};

export default HeaderMinimal;
