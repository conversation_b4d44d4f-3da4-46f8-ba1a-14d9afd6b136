/* 
 * FunnelVision Design System - Consolidated Diabrowser CSS
 * Single source of truth for all design tokens, typography, and responsive behavior
 * Based on diabrowser.com's exact design principles and scaling approach
 */

:root {
  /* 
   * DIABROWSER TYPOGRAPHY SCALE
   * Based on diabrowser.com's exact rem values and scaling approach
   */
  
  /* Base Font Sizes - Diabrowser's rem system */
  --dia-font-size-xs: 1.3rem;     /* 13px - caption/small text */
  --dia-font-size-sm: 1.4rem;     /* 14px - p2 text */
  --dia-font-size-base: 1.6rem;   /* 16px - p2 desktop */
  --dia-font-size-lg: 1.8rem;     /* 18px - h4 desktop & body text */
  --dia-font-size-xl: 2.0rem;     /* 20px - p1 desktop & h3/h4 mobile */
  --dia-font-size-2xl: 2.2rem;    /* 22px - h3/h4 desktop  */
  --dia-font-size-3xl: 2.6rem;    /* 26px - h4 desktop */
  --dia-font-size-4xl: 3.2rem;    /* 32px - h3 desktop */
  --dia-font-size-5xl: 4.8rem;    /* 48px - h1 mobile */
  --dia-font-size-6xl: 6.4rem;    /* 64px - h2 desktop */
  --dia-font-size-7xl: 7.2rem;    /* 72px - h1 desktop */
  --dia-font-size-8xl: 8.4rem;    /* 84px - h1 line-height reference */

  /* 
   * DIABROWSER FONT WEIGHTS
   * Much lighter than current system
   */
  --dia-font-weight-light: 350;    /* For headings - diabrowser's approach */
  --dia-font-weight-normal: 400;   /* For body text */
  --dia-font-weight-medium: 500;   /* Rarely used */
  --dia-font-weight-semibold: 600; /* h3/h4 headings */

  /* 
   * DIABROWSER SPACING GRID
   * 0.6rem base instead of 8px base
   */
  --dia-space-1: 0.6rem;      /* 6px */
  --dia-space-2: 0.8rem;      /* 8px */
  --dia-space-3: 1.0rem;      /* 10px */
  --dia-space-4: 1.2rem;      /* 12px */
  --dia-space-5: 1.4rem;      /* 14px */
  --dia-space-6: 1.6rem;      /* 16px */
  --dia-space-7: 1.8rem;      /* 18px */
  --dia-space-8: 2.0rem;      /* 20px */
  --dia-space-9: 2.4rem;      /* 24px */
  --dia-space-10: 3.0rem;     /* 30px */
  --dia-space-11: 4.0rem;     /* 40px */
  --dia-space-12: 5.0rem;     /* 50px */
  --dia-space-13: 6.0rem;     /* 60px */
  --dia-space-14: 8.0rem;     /* 80px */
  --dia-space-15: 10.0rem;    /* 100px */
  --dia-space-16: 16.0rem;    /* 160px */
  --dia-space-18: 18.0rem;    /* 180px */

  /*
   * DIABROWSER COLOR SYSTEM
   * Monochromatic approach with white cards as default
   */
  --dia-color-black: #202124;
  --dia-color-white: #FFFFFF;         /* Pure white for cards */
  --dia-color-grey: #D9D9D9;
  --dia-color-dark-grey: #f2f2f3; 

  /* Text Colors - Optimized for light mode comfort */
  --dia-color-text-primary: #202124;        /* Dark gray instead of harsh black */
  --dia-color-text-secondary: #202124;      /* Medium gray for comfortable reading */
  --dia-color-text-highlight: #96969e;          /* Lighter gray for important content on cards */
  --dia-color-text-muted: hsl(0, 0%, 55%);          /* Lighter gray for less important content */
  --dia-color-text-light: #96969e;          /* Very light gray for subtle text */

  /* Background Colors - Optimized light mode hierarchy */
  --dia-color-background: #FFFFFF;                   /* Site background - clean off-white */
  --dia-color-background-light: #F9F6F2;             /* Light background - subtle variation */
  --dia-color-background-overlay: rgba(0, 0, 0, 0.08); /* bg-black/8 */
  --dia-color-header-background: #FAFAFA;             /* Header - slightly elevated off-white */
  --dia-color-overlay-background: #F5F5F5;           /* Overlay background - subtle variation */
  --dia-color-cta-background-primary: hsl(0, 0%, 15%); /* CTA background - comfortable dark gray */
  --dia-color-cta-background-secondary: #F5F5F5;     /* Secondary CTA background */

  /* Card Colors - Enhanced white-based light mode system */
  --dia-color-card-background: #EFEAE5;             /* Default card background - pure white (perfect) */
  --dia-color-card-background-alt: #D6DBD9;         /* Alternative card background - matches site (perfect) */
  --dia-color-card-border: 0 solid #F5F3EF;         /* Softer border that blends better */
  --dia-color-card-highlight: hsl(0, 0%, 100%);     /* Pure white for top highlights */
  --dia-color-card-shadow-light: 0 solid #f2f2f3; /* Light shadow for depth */

  /* Enhanced Light Mode Card System */
  --dia-color-card-border-hover: 0 solid #f2f2f3;   /* Slightly darker border on hover */
  --dia-color-card-background-elevated: 0 solid #f2f2f3;    /* Slightly elevated card background */
  --dia-color-card-background-pressed: #FDFDFD;     /* Pressed/active card background */

  /* Accent Colors (Minimal Usage) */
  --dia-color-teal-green: #1b3d3c;                           /* Brand accent */

  /*
   * DIABROWSER BREAKPOINTS
   * 17-breakpoint system for progressive container sizing
   */
  --dia-breakpoint-micro: 400px;       /* Micro mobile */
  --dia-breakpoint-small: 600px;       /* Small mobile */
  --dia-breakpoint-large-mobile: 700px; /* Large mobile */
  --dia-breakpoint-tablet: 800px;      /* Typography major jump */
  --dia-breakpoint-tablet-large: 900px; /* Large tablet */
  --dia-breakpoint-desktop: 1000px;    /* Body text refinement */
  --dia-breakpoint-desktop-medium: 1100px; /* Medium desktop */
  --dia-breakpoint-desktop-large: 1200px;  /* Large desktop */
  --dia-breakpoint-xl: 1300px;         /* XL desktop */
  --dia-breakpoint-xxl: 1400px;        /* XXL desktop */
  --dia-breakpoint-ultra: 1500px;      /* Ultra desktop */
  --dia-breakpoint-wide: 1600px;       /* Wide desktop */
  --dia-breakpoint-super: 1700px;      /* Super wide */
  --dia-breakpoint-mega: 1800px;       /* Mega wide */
  --dia-breakpoint-giga: 1900px;       /* Giga wide */
  --dia-breakpoint-max: 2000px;        /* Maximum width */

  /* 
   * DIABROWSER TYPOGRAPHY HIERARCHY
   * Responsive typography tokens matching diabrowser exactly
   */

  /* Hero Title (h1) - Mobile First */
  --dia-hero-title-size: 4.8rem;           /* 48px mobile */
  --dia-hero-title-weight: 350px;
  --dia-hero-title-line-height: 5.2rem;    /* 52px mobile */
  --dia-hero-title-spacing: -0.04em;

  /* Hero Secondary (h2) - Mobile First */
  --dia-hero-secondary-size: 3.6rem;       /* 36px mobile */
  --dia-hero-secondary-weight: 350px;
  --dia-hero-secondary-line-height: 4.1rem; /* 41px mobile */
  --dia-hero-secondary-spacing: -0.04em;

  /* Section Headings (h3) - Mobile First */
  --dia-section-heading-size: 3.6rem;      /* 24px mobile */
  --dia-section-heading-weight: 350px;
  --dia-section-heading-line-height: 4.1rem; /* 30px mobile */
  --dia-section-heading-spacing: -0.04em;

  /* Body Text - Mobile First */
  --dia-body-text-size: 1.4rem;            /* 14px mobile */
  --dia-body-text-weight: 400px;
  --dia-body-text-line-height: 1.7rem;     /* 17px mobile */
  --dia-body-text-spacing: -0.02em;

  /* 
   * LEGACY COMPATIBILITY MAPPINGS
   * These allow gradual migration without breaking existing components
   */
  
  /* Map current section-heading tokens to diabrowser equivalents */
  --dia-font-size-section-heading-mobile: var(--dia-section-heading-size);
  --dia-font-size-section-heading-tablet: var(--dia-section-heading-size);
  --dia-font-size-section-heading-desktop: var(--dia-font-size-4xl); /* 34px */
  
  --dia-font-weight-section-heading: var(--dia-section-heading-weight);
  --dia-line-height-section-heading: var(--dia-section-heading-line-height);

  /* Map current subheading tokens */
  --dia-font-size-subheading-mobile: var(--dia-font-size-xl);    /* 22px */
  --dia-font-size-subheading-tablet: var(--dia-font-size-xl);    /* 22px */
  --dia-font-size-subheading-desktop: var(--dia-font-size-xl);   /* 22px */
  
  --dia-font-weight-subheading: var(--dia-body-text-weight);
  --dia-line-height-subheading: var(--dia-body-text-line-height);

  /* Map current description tokens */
  --dia-font-size-description-mobile: var(--dia-body-text-size);
  --dia-font-size-description-tablet: var(--dia-body-text-size);
  --dia-font-size-description-desktop: var(--dia-font-size-xl);   /* 22px */

  /*
   * SIMPLIFIED FONT SIZE SYSTEM
   * Clean, logical mapping to diabrowser tokens
   * Only includes actually used sizes for better maintainability
   */

  /* Core Text Sizes - Most commonly used */
  --font-size-small: var(--dia-font-size-xs);    /* 13px - Labels, captions */
  --font-size-body: var(--dia-font-size-sm);     /* 14px - Body text, buttons */
  --font-size-base: var(--dia-font-size-base);   /* 16px - Base text, descriptions */
  --font-size-large: var(--dia-font-size-lg);    /* 18px - Large body text */
  --font-size-title: var(--dia-font-size-xl);    /* 22px - Card titles, subtitles */
  --font-size-heading: var(--dia-font-size-2xl); /* 24px - Section headings */

  /* Legacy Compatibility Mappings - Gradually migrate away from these */
  --font-size-12: var(--font-size-small);   /* → --font-size-small */
  --font-size-14: var(--font-size-body);    /* → --font-size-body */
  --font-size-16: var(--font-size-base);    /* → --font-size-base */
  --font-size-18: var(--font-size-large);   /* → --font-size-large */
  --font-size-20: var(--font-size-title);   /* → --font-size-title */
  --font-size-22: var(--font-size-title);   /* → --font-size-title (was duplicate) */
  --font-size-24: var(--font-size-heading); /* → --font-size-heading */
  --font-size-28: var(--font-size-large);   /* → --font-size-large (was duplicate) */
  --font-size-32: var(--font-size-heading); /* → --font-size-heading (was duplicate) */
  --font-size-36: var(--font-size-heading); /* → --font-size-heading (was duplicate) */

  /* Rarely Used Legacy Sizes - Consider removing in future cleanup */
  --font-size-42: var(--dia-font-size-3xl);  /* 26px - Rarely used */
  --font-size-47: var(--dia-font-size-5xl);  /* 48px - Duplicate of 48 */
  --font-size-48: var(--dia-font-size-5xl);  /* 48px - Duplicate of 47 */
  --font-size-64: var(--dia-font-size-4xl);  /* 34px - Misleading name */
  --font-size-80: var(--dia-font-size-5xl);  /* 48px - Misleading name */
  --font-size-123: var(--dia-font-size-7xl); /* 72px - Rarely used */

  /* Legacy laptop font sizes - for backward compatibility */
  --font-size-h1-laptop: var(--dia-hero-secondary-size);  /* 36px mobile, 64px desktop */
  --font-size-h3-laptop: var(--dia-section-heading-size); /* 24px mobile, 34px desktop */

  /* Legacy breakpoint variables - for backward compatibility */
  --breakpoint-laptop: 1000px;
  --breakpoint-tablet: 800px;
  --breakpoint-mobile: 480px;

  /* Typography */
  --font-family-inter:
    "Inter", -apple-system, "Segoe UI", Roboto, Helvetica, Arial,
    sans-serif;
    
  /* Standardized UI Radius - 16px universal standard */
  --radius-sm: 16px;   /* Small elements - standardized to 16px */
  --radius-md: 16px;   /* Medium elements - standardized to 16px */
  --radius-lg: 16px;   /* Large elements - standardized to 16px */
  --radius-xl: 16px;   /* Extra large elements - standardized to 16px */
  --radius-standard: 16px; /* Universal standard radius */
  --radius-full: 9999px; /* Circles and pills only */

  /* Enhanced Light Mode Shadow System - Two-shadow technique */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(255, 255, 255, 0.3);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(255, 255, 255, 0.4);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(255, 255, 255, 0.5);
  --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.15), 0 16px 32px rgba(255, 255, 255, 0.6);

  /* Mobile-optimized shadows for performance */
  --shadow-sm-mobile: 0 1px 2px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(255, 255, 255, 0.2);
  --shadow-md-mobile: 0 1px 3px rgba(0, 0, 0, 0.06), 0 4px 8px rgba(255, 255, 255, 0.3);

  /* Light Mode Highlight System - Overhead lighting simulation */
  --highlight-subtle: rgba(255, 255, 255, 0.6);
  --highlight-medium: rgba(255, 255, 255, 0.8);
  --highlight-strong: rgba(255, 255, 255, 1.0);
  --highlight-top-border: 1px solid var(--highlight-medium);
  
  --transition-base: 0.2s ease;
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Common Color Constants */
  --color-text-primary: var(--dia-color-text-primary);
  --color-text-secondary: var(--dia-color-text-secondary);
  --color-text-muted: var(--dia-color-text-muted);
  --color-text-white: #FFFFFF;
  --color-text-white-secondary: rgba(255, 255, 255, 0.7);
  --color-text-white-muted: rgba(255, 255, 255, 0.5);
  --color-link: var(--dia-color-primary);
  --color-link-hover: var(--dia-color-primary-dark);
  --color-border-light: rgba(0, 0, 0, 0.04); /* Softer, more subtle borders */
  --color-error: #E53935;
  --color-primary: #1b3d3c;
  --color-accent-primary: #1b3d3c;
  --color-accent-secondary-light: rgba(255, 255, 255, 0.85);
  
  /* Header & Footer Tokens */
  --dia-color-header-background: rgba(250, 250, 250, 0.95); /* Semi-transparent elevated off-white */
  --dia-color-header-background-scrolled: rgba(250, 250, 250, 0.98); /* More opaque when scrolled */
  --dia-color-header-border: rgba(0, 0, 0, 0.04); /* Very subtle border */
  --dia-color-footer-background: var(--color-primary);
  --dia-color-footer-text: var(--color-text-white);
  --dia-color-footer-text-secondary: var(--color-text-white-secondary);
  --dia-color-footer-text-muted: var(--color-text-white-muted);
  --dia-color-footer-border: rgba(255, 255, 255, 0.08);
  
  /* Widths */
  --width-1070: 1070px;
  

  
  /* Typography Constants */
  --line-height-none: 1;
  --line-height-tight: 1.1;
  --line-height-snug: 1.25;
  --line-height-base: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  
  /* Font Weight Constants */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /*
   * COMPONENT-SPECIFIC DIABROWSER TOKENS
   * Comprehensive typography system for all UI components
   */

  /* Bento Cards */
  --dia-bento-title-size: var(--dia-font-size-lg);        /* 1.8rem (18px) */
  --dia-bento-title-weight: var(--dia-font-weight-semibold);
  --dia-bento-title-line-height: 1.3;
  --dia-bento-description-size: var(--dia-font-size-base); /* 1.6rem (16px) */
  --dia-bento-description-weight: var(--dia-body-text-weight);
  --dia-bento-description-line-height: var(--dia-body-text-line-height);

  /* Carousel Cards */
  --dia-card-tag-size: var(--dia-font-size-2xl);           /* 1.3rem (13px) */
  --dia-card-tag-weight: 500;
  --dia-card-tag-line-height: 1.2;
  --dia-card-title-size: var(--dia-font-size-sm);         /* 1.8rem (18px) */
  --dia-card-title-weight: 500;
  --dia-card-title-line-height: 1.3;
  --dia-card-content-size: var(--dia-font-size-base);     /* 1.4rem (14px) */
  --dia-card-content-weight: var(--dia-body-text-weight);
  --dia-card-content-line-height: var(--dia-body-text-line-height);
  --dia-card-subtitle-size: var(--dia-font-size-sm);    /* 1.6rem (16px) */
  --dia-card-subtitle-weight: 500;
  --dia-card-subtitle-line-height: 1.4;

  /* Explicit Carousel Card Tokens for component styles */
  --dia-carousel-card-title-size: var(--dia-font-size-2xl);
  --dia-carousel-card-title-weight: var(--dia-font-weight-semibold);
  --dia-carousel-card-title-line-height: var(--dia-card-title-line-height);
  --dia-carousel-card-description-size: var(--dia-card-content-size);
  --dia-carousel-card-description-weight: var(--dia-card-content-weight);
  --dia-carousel-card-description-line-height: var(--dia-card-content-line-height);
  --dia-carousel-card-subtitle-size: var(--dia-card-subtitle-size);
  --dia-carousel-card-subtitle-weight: var(--dia-card-subtitle-weight);
  --dia-carousel-card-subtitle-line-height: var(--dia-card-subtitle-line-height);
  --dia-carousel-card-content-size: var(--dia-card-content-size);
  --dia-carousel-card-content-line-height: var(--dia-card-content-line-height);
  --dia-carousel-card-cta-size: var(--card-cta-text-size);
  --dia-carousel-card-cta-weight: var(--card-cta-text-weight);
  --dia-carousel-card-cta-line-height: var(--card-cta-text-line-height);

  /* Testimonial Cards */
  --dia-testimonial-text-size: var(--dia-body-text-size); /* 1.4rem (14px) */
  --dia-testimonial-text-weight: var(--dia-body-text-weight);
  --dia-testimonial-text-line-height: var(--dia-body-text-line-height);
  --dia-testimonial-name-size: var(--dia-font-size-base); /* 1.6rem (16px) */
  --dia-testimonial-name-weight: 500px;
  --dia-testimonial-name-line-height: 1.3;
  --dia-testimonial-role-size: var(--dia-font-size-sm);   /* 1.4rem (14px) */
  --dia-testimonial-role-weight: 400px;
  --dia-testimonial-role-line-height: 1.4;
  
  /* Additional Testimonial Tokens */
  --dia-testimonial-author-size: var(--dia-font-size-sm); /* 1.4rem (14px) */
  --dia-testimonial-author-weight: var(--dia-font-weight-medium);
  --dia-testimonial-author-line-height: 1.3;

  /* Navigation Elements */
  --dia-nav-link-size: var(--dia-font-size-base);         /* 1.6rem (16px) */
  --dia-secondary-nav-link-size: var(--dia-font-size-sm);         /* 1.4rem (14px) */
  --dia-nav-link-weight: 400px;
  --dia-nav-link-line-height: 1.5;
  --dia-nav-link-active-weight: 600px;
  --dia-nav-brand-size: var(--dia-font-size-lg);          /* 1.8rem (18px) */
  --dia-nav-brand-weight: 500px;
  --dia-nav-brand-line-height: 1.2;

  /* Button Text */
  --dia-btn-primary-size: var(--dia-font-size-base);      /* 1.6rem (16px) */
  --dia-btn-primary-weight: 500px;
  --dia-btn-primary-line-height: 1.2;
  --dia-btn-secondary-size: var(--dia-font-size-base);      /* 1.6rem (16px) */
  --dia-btn-secondary-weight: 500px;
  --dia-btn-secondary-line-height: 1.2;
  --dia-btn-cta-size: var(--dia-font-size-base);          /* 1.6rem (16px) */
  --dia-btn-cta-weight: 500px;
  --dia-btn-cta-line-height: 1.2;

  /* Footer Elements */
  --dia-footer-title-size: var(--dia-font-size-2xl);      /* 2.4rem (24px) */
  --dia-footer-title-weight: 400px;
  --dia-footer-title-line-height: 1.3;
  --dia-footer-link-size: var(--dia-font-size-sm);        /* 1.4rem (14px) */
  --dia-footer-link-weight: 400px;
  --dia-footer-link-line-height: 1.5;
  --dia-footer-copyright-size: var(--dia-font-size-xs);   /* 1.3rem (13px) */
  --dia-footer-copyright-weight: 400px;
  --dia-footer-copyright-line-height: 1.4;

  /* Form Elements */
  --dia-form-label-size: var(--dia-font-size-sm);         /* 1.4rem (14px) */
  --dia-form-label-weight: 500px;
  --dia-form-label-line-height: 1.4;
  --dia-form-input-size: var(--dia-font-size-base);       /* 1.6rem (16px) */
  --dia-form-input-weight: 400px;
  --dia-form-input-line-height: 1.5;
  --dia-form-error-size: var(--dia-font-size-xs);         /* 1.3rem (13px) */
  --dia-form-error-weight: 400px;
  --dia-form-error-line-height: 1.3;

  /* FAQ Elements */
  --dia-faq-question-size: var(--dia-section-heading-size); /* Same as h2 */
  --dia-faq-question-weight: var(--dia-section-heading-weight);
  --dia-faq-question-line-height: var(--dia-section-heading-line-height);
  --dia-faq-answer-size: var(--dia-body-text-size);       /* Same as body text */
  --dia-faq-answer-weight: var(--dia-body-text-weight);
  --dia-faq-answer-line-height: var(--dia-body-text-line-height);
  
  /* Accordion Card Tokens */
  --card-accordion-title-mobile: var(--dia-font-size-base);    /* 1.6rem (16px) */
  --card-accordion-title-tablet: var(--dia-font-size-lg);      /* 1.8rem (18px) */
  --card-accordion-title-desktop: var(--dia-font-size-lg);     /* 2.2rem (22px) */
  --card-accordion-title-weight: var(--dia-font-weight-medium);
  --card-accordion-content-mobile: var(--dia-font-size-sm);    /* 1.4rem (14px) */
  --card-accordion-content-tablet: var(--dia-font-size-base);  /* 1.6rem (16px) */
  --card-accordion-content-desktop: var(--dia-font-size-base); /* 1.6rem (16px) */
  
  /* Comparison Card Tokens */
  --dia-comparison-header-size: var(--dia-font-size-lg);      /* 1.8rem (18px) */
  --dia-comparison-header-weight: var(--dia-font-weight-semibold);
  --dia-comparison-header-line-height: 1.3;
  --card-comparison-header-tablet: var(--dia-font-size-xl);    /* 2.0rem (22px) */
  --card-comparison-header-desktop: var(--dia-font-size-2xl);  /* 2.2rem (22px) */
  --card-comparison-item-mobile: var(--dia-font-size-sm);      /* 1.4rem (14px) */
  --card-comparison-item-tablet: var(--dia-font-size-base);    /* 1.6rem (16px) */
  --card-comparison-item-desktop: var(--dia-font-size-base);     /* 1.8rem (18px) */
  --card-comparison-item-line-height: 1.5;
  
  /* Card Content Tokens */
  --font-size-card-content-mobile: var(--dia-font-size-sm);    /* 1.4rem (14px) */
  --font-size-card-content-tablet: var(--dia-font-size-base);  /* 1.6rem (16px) */
  --font-size-card-content-desktop: var(--dia-font-size-base);   /* 1.8rem (18px) */
  --font-weight-card-content: var(--dia-font-weight-normal);
  
  /* Card Label/Tag Tokens */
  --font-size-card-label-mobile: var(--dia-font-size-xs);      /* 1.3rem (13px) */
  --font-size-card-label-tablet: var(--dia-font-size-sm);      /* 1.4rem (14px) */
  --font-size-card-label-desktop: var(--dia-font-size-sm);     /* 1.4rem (14px) */
  
  /* Card Subtitle Tokens */
  --font-size-card-subtitle-mobile: var(--dia-font-size-sm);   /* 1.4rem (14px) */
  --font-size-card-subtitle-tablet: var(--dia-font-size-sm); /* 1.6rem (16px) */
  --font-size-card-subtitle-desktop: var(--dia-font-size-sm);  /* 1.8rem (18px) */
  --font-weight-card-subtitle: var(--dia-font-weight-medium);
  
  /* Card Title Tokens */
  --font-size-card-title-mobile: var(--dia-font-size-lg);      /* 1.8rem (18px) */
  --font-size-card-title-tablet: var(--dia-font-size-xl);      /* 2.2rem (22px) */
  --font-size-card-title-desktop: var(--dia-font-size-2xl);    /* 2.4rem (24px) */
  --font-weight-card-title: var(--dia-font-weight-medium);
  
  /* CTA Text Tokens */
  --card-cta-text-size: var(--dia-font-size-sm);            /* 1.6rem (16px) */
  --card-cta-text-weight: var(--dia-font-weight-medium);
  --card-cta-text-line-height: 1.2;
  


  /* Interactive States */
  --dia-hover-weight-increase: 500; /* For hover states that need weight change */
  --dia-active-weight: 600;         /* For active/pressed states */
  --dia-disabled-opacity: 0.6;      /* For disabled text elements */
}

/*
 * DIABROWSER RESPONSIVE TYPOGRAPHY
 * Breakpoint-specific overrides matching diabrowser exactly
 */

/* 
 * TESTIMONIAL STYLES
 * Central definitions for testimonial components
 */
.keyword-highlight {
  font-weight: var(--dia-font-weight-medium);
  color: var(--dia-color-text-primary);
}

/* 
 * CAROUSEL CARD STYLES 
 * Only define tokens for component-specific styles
 * Actual implementation in component CSS files
 */

/* Card tokens */
:root {
  /* Enhanced Card Base Styles - Light mode elevation system with 16px radius */
  --card-border-radius: var(--radius-standard); /* Universal 16px radius */
  --card-border: 1px solid var(--dia-color-card-border); /* Universal card border */
  --card-border-hover: 1px solid var(--dia-color-card-border-hover); /* Hover border */
  --card-border-top-highlight: var(--highlight-top-border); /* Top highlight for elevation */
  --card-box-shadow: var(--shadow-sm);
  --card-hover-box-shadow: var(--shadow-md);
  --card-pressed-box-shadow: var(--shadow-lg);
  --card-hover-transform: translateY(-2px);
  --card-pressed-transform: translateY(-1px);
  --card-transition: transform var(--transition-base), box-shadow var(--transition-base), border-color var(--transition-base), background var(--transition-base);

  /*
   * UNIFIED CTA SYSTEM - Based on Hero Section Design
   * Consistent styling for all CTAs across the site
   */

  /* CTA Base Styles */
  --cta-border-radius: 16px;
  --cta-min-width-mobile: 180px;
  --cta-min-width-tablet: 200px;
  --cta-min-width-desktop: 220px;
  --cta-min-width-large: 240px;
  --cta-height: 50px;
  --cta-transition: all 0.3s ease;
  --cta-hover-transform: translateY(-2px);

  /* CTA Spacing */
  --cta-padding-mobile: var(--dia-space-4) var(--dia-space-8);
  --cta-padding-tablet: var(--dia-space-4) var(--dia-space-9);
  --cta-padding-desktop: var(--dia-space-5) var(--dia-space-9);
  --cta-padding-large: var(--dia-space-5) var(--dia-space-10);
  --cta-gap: var(--dia-space-2);

  /* Primary CTA Styles - Dark CTAs go to black on hover */
  --cta-primary-background: var(--dia-color-cta-background-primary);
  --cta-primary-background-hover: #000000; /* Pure black for better contrast */
  --cta-primary-text-color: var(--dia-color-background);
  --cta-primary-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
  --cta-primary-shadow-hover: 0px 6px 16px rgba(0, 0, 0, 0.25);

  /* Secondary CTA Styles */
  --cta-secondary-background: var(--dia-color-cta-background-secondary);
  --cta-secondary-background-hover: var(--dia-color-background);
  --cta-secondary-text-color: var(--dia-color-text-primary);
  --cta-secondary-border: 1px solid var(--dia-color-text-primary);
  --cta-secondary-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  --cta-secondary-shadow-hover: 0px 6px 16px rgba(0, 0, 0, 0.15);

  /* Universal CTA Arrow System */
  --cta-arrow-size: 18px;
  --cta-arrow-transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --cta-arrow-hover-transform: translateX(4px);
  --cta-text-hover-transform: translateX(2px);

  /* Enhanced Light Mode Card Gradients - Overhead lighting simulation */
  --card-background-gradient: #EFEAE5;
  --card-hover-background-gradient: #EFEAE5;
  --card-pressed-background-gradient: #EFEAE5;
  --card-elevated-background-gradient: #EFEAE5;
  
  /* Carousel specific */
  --funnelvision-carousel-card-min-height: 320px;
  --funnelvision-carousel-card-padding: var(--dia-space-6);
  --funnelvision-carousel-title-margin: var(--dia-space-2) 0 var(--dia-space-4);
  
  /* Testimonial specific */
  --testimonial-card-padding: var(--dia-space-8);
  --testimonial-column-gap: var(--dia-space-6);
  --testimonial-column-padding: var(--dia-space-4);
  --testimonial-author-gap: var(--dia-space-4);
  --testimonial-text-margin: 0 0 var(--dia-space-4) 0;
}

/* Card revealed state */
.card-revealed .funnelvision-carousel-card-content {
  opacity: 0;
}

/* 
 * BENTO GRID STYLES
 * Central definitions for bento grid components 
 */

/* Expand card tokens for bento cards */
:root {
  /* Bento grid layout */
  --bento-grid-gap: var(--dia-space-5);
  --bento-grid-columns-mobile: repeat(1, 1fr);
  --bento-grid-columns-tablet: repeat(2, 1fr);
  --bento-grid-columns-desktop: repeat(3, 1fr);
  
  /* Bento card sizing */
  --bento-card-padding: var(--dia-space-8);
  --bento-icon-size: 48px;
  --bento-icon-margin: 0 0 var(--dia-space-6) 0;
  --bento-icon-background: var(--dia-color-primary-light);
  --bento-icon-border-radius: var(--radius-standard); /* Standardized to 16px */
  
  /* Bento content spacing */
  --bento-title-margin: 0 0 var(--dia-space-3) 0;
  --bento-description-margin: 0 0 var(--dia-space-6) 0;
  --bento-footer-margin: auto 0 0 0;
  --bento-cta-gap: var(--dia-space-2);
  --bento-arrow-transform: translateX(4px);

  /* Bento Alt card sizing - Updated for consistent light mode hierarchy */
  --bento-alt-icon-size: 100px;
  --bento-alt-description-size: var(--dia-font-size-4xl);
  --bento-alt-description-size-mobile: var(--dia-font-size-2xl);
  --bento-alt-description-weight: var(--dia-font-weight-semibold);
  --bento-alt-padding-desktop: var(--dia-space-11); /* 48px */
  --bento-alt-padding-tablet: var(--dia-space-9); /* 24px */
  --bento-alt-padding-mobile: var(--dia-space-9); /* 24px */
  --bento-alt-background: var(--dia-color-card-background-alt); /* Consistent with light mode hierarchy */
  --bento-alt-text-margin-top: var(--dia-space-11); /* 40px */
  --bento-alt-text-margin-bottom: var(--dia-space-6); /* 16px */
  --bento-alt-description-letter-spacing: 0;
}

/*
 * UNIVERSAL CTA CLASSES
 * Consistent CTA styling based on hero section design
 */

/* Base CTA styling */
.cta-base {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--cta-gap);
  padding: var(--cta-padding-mobile);
  border-radius: var(--cta-border-radius);
  height: var(--cta-height);
  min-width: var(--cta-min-width-mobile);
  cursor: pointer;
  position: relative;
  text-align: center;
  flex-shrink: 0;
  text-decoration: none;
  border: none;
  font-family: var(--font-family-inter);
  font-size: var(--dia-btn-cta-size);
  font-weight: var(--dia-btn-cta-weight);
  line-height: var(--dia-btn-cta-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  transition: var(--cta-transition);
  white-space: nowrap;
  outline: none;
}

/* Primary CTA */
.cta-primary {
  background: var(--cta-primary-background);
  color: var(--cta-primary-text-color);
  box-shadow: var(--cta-primary-shadow);
}

.cta-primary:hover {
  background: var(--cta-primary-background-hover);
  box-shadow: var(--cta-primary-shadow-hover);
  transform: var(--cta-hover-transform);
}

/* Secondary CTA */
.cta-secondary {
  background: var(--cta-secondary-background);
  color: var(--cta-secondary-text-color);
  border: var(--cta-secondary-border);
  box-shadow: var(--cta-secondary-shadow);
}

.cta-secondary:hover {
  background: var(--cta-secondary-background-hover);
  box-shadow: var(--cta-secondary-shadow-hover);
  transform: var(--cta-hover-transform);
}

/* Universal CTA Arrow Styling */
.cta-arrow {
  width: var(--cta-arrow-size);
  height: var(--cta-arrow-size);
  transition: var(--cta-arrow-transition);
  flex-shrink: 0;
  margin-left: var(--dia-space-1);
}

.cta-text {
  transition: var(--cta-arrow-transition);
}

/* CTA hover effects with arrow animation */
.cta-base:hover .cta-arrow {
  transform: var(--cta-arrow-hover-transform);
}

.cta-base:hover .cta-text {
  transform: var(--cta-text-hover-transform);
}

/* CTA Responsive Sizing */
@media (min-width: 600px) {
  .cta-base {
    min-width: var(--cta-min-width-tablet);
    padding: var(--cta-padding-tablet);
  }
}

@media (min-width: 800px) {
  .cta-base {
    min-width: var(--cta-min-width-desktop);
    padding: var(--cta-padding-desktop);
  }
}

@media (min-width: 1000px) {
  .cta-base {
    min-width: var(--cta-min-width-large);
    padding: var(--cta-padding-large);
  }
}

/*
 * COMPARISON CARD STYLES
 * Central definitions for comparison card components
 * Note: Container styling removed - only individual card backgrounds should be visible
 */

/* 
 * COMPARISON CARD STYLES 
 * Only define tokens for component-specific styles
 */
:root {
  /* Comparison layout */
  --comparison-column-padding: var(--dia-space-11) var(--dia-space-10);
  --comparison-title-margin: 0 0 var(--dia-space-6) 0;
  --comparison-title-alignment: left;
}

/* 
 * FAQ ACCORDION STYLES
 * Only define tokens for component-specific styles
 */
:root {
  /* FAQ layout */
  --faq-section-padding: var(--dia-space-14) 0;
  --faq-header-margin: 0 0 var(--dia-space-12) 0;
  --faq-header-alignment: center;
  --faq-accordion-max-width: 800px;
  
  /* FAQ item styling */
  --faq-item-padding: var(--dia-space-3) var(--dia-space-2);
  --faq-item-margin: 0 0 var(--dia-space-2) 0;
  --faq-item-border: 1px solid var(--color-border-light);
  --faq-item-border-radius: var(--radius-standard); /* Standardized to 16px */
  
  /* FAQ question styling */
  --faq-question-padding-right: var(--dia-space-8);
}

/* Mobile-first responsive shadow optimization */
@media (max-width: 800px) {
  :root {
    /* Use lighter shadows on mobile for better performance */
    --card-box-shadow: var(--shadow-sm-mobile);
    --card-hover-box-shadow: var(--shadow-md-mobile);
  }
}

/* Tablet (800px) - Diabrowser's typography breakpoint */
@media (min-width: 800px) {
  :root {
    /* Restore full shadows on larger screens */
    --card-box-shadow: var(--shadow-sm);
    --card-hover-box-shadow: var(--shadow-md);

    /* Hero Title scales up slightly */
    --dia-hero-title-size: 4.8rem;           /* Same as mobile */
    --dia-hero-title-line-height: 5.2rem;    /* Same as mobile */

    /* Hero Secondary scales up slightly */
    --dia-hero-secondary-size: 3.6rem;       /* Same as mobile */
    --dia-hero-secondary-line-height: 4.1rem; /* Same as mobile */

    /* Section Headings stay same as mobile */
    --dia-section-heading-size: 3.6rem;      /* 36px */
    --dia-section-heading-line-height: 4.1rem; /* 41px */

    /* Body Text stays smaller on tablet */
    --dia-body-text-size: 1.4rem;            /* 14px */
    --dia-body-text-line-height: 1.7rem;     /* 17px */

    /* Component tokens scale slightly at 800px */
    --dia-bento-title-size: var(--dia-font-size-xl);     /* 2.2rem (22px) */
    --dia-card-title-size: var(--dia-font-size-xl);      /* 2.2rem (22px) */
    --dia-testimonial-name-size: var(--dia-font-size-lg); /* 1.8rem (18px) */
    --dia-nav-brand-size: var(--dia-font-size-xl);       /* 2.2rem (22px) */
    --dia-footer-title-size: var(--dia-font-size-3xl);   /* 2.6rem (26px) */
  }
}

/* Desktop (1000px) - Diabrowser's body text scaling point */
@media (min-width: 1000px) {
  :root {
    /* Hero Title scales to full desktop size */
    --dia-hero-title-size: 7.2rem;           /* 72px desktop */
    --dia-hero-title-line-height: 8.4rem;    /* 84px desktop */

    /* Hero Secondary scales to full desktop size */
    --dia-hero-secondary-size: 6.4rem;       /* 64px desktop */
    --dia-hero-secondary-line-height: 7rem;  /* 70px desktop */

    /* Section Headings scale to desktop size */
    --dia-section-heading-size: 6.4rem;      /* 64px desktop */
    --dia-section-heading-line-height: 7rem; /* 70px desktop */

    /* Body Text scales to desktop size */
    --dia-body-text-size: 2.0rem;            /* 20px desktop */
    --dia-card-text-size: 1.6rem;            /* 16px desktop */
    --dia-body-text-line-height: 2.6rem;     /* 26px desktop */

    /* Component tokens scale to full desktop size at 1000px */
    --dia-bento-title-size: var(--dia-font-size-xl);    /* 2.4rem (24px) */
    --dia-bento-description-size: var(--dia-card-text-size); /* 1.4rem (14px) */
    --dia-card-title-size: var(--dia-font-size-base);     /* 2.4rem (24px) */
    --dia-card-content-size: var(--dia-font-size-sm);  /* 2.2rem (22px) */
    --dia-card-subtitle-size: var(--dia-font-size-sm);   /* 1.8rem (18px) */
    --dia-testimonial-text-size: var(--dia-font-size-sm); /* 2.2rem (22px) */
    --dia-testimonial-name-size: var(--dia-font-size-xl); /* 2.2rem (22px) */
    --dia-testimonial-role-size: var(--dia-font-size-sm); /* 1.6rem (16px) */
    --dia-nav-link-size: var(--dia-font-size-base);        /* 1.4rem (14px) */
    --dia-secondary-nav-link-size: var(--dia-font-size-sm);        /* 1.4rem (14px) */
    --dia-nav-brand-size: var(--dia-font-size-2xl);      /* 2.4rem (24px) */
    --dia-btn-primary-size: var(--dia-font-size-base);     /* 1.6rem (16px) */
    --dia-btn-cta-size: var(--dia-font-size-base);         /* 1.8rem (18px) */
    --dia-footer-title-size: var(--dia-font-size-4xl);   /* 3.4rem (34px) */
    --dia-footer-link-size: var(--dia-font-size-base);   /* 1.6rem (16px) */
    --dia-form-input-size: var(--dia-font-size-lg);      /* 1.8rem (18px) */

    /* Update compatibility mappings for desktop */
    --dia-font-size-description-desktop: var(--dia-body-text-size);
  }
}

/* ==========================================
   CONTENT HIERARCHY CLASSES
   Standardized styles for section typography
   ========================================== */

/* Section Headings (H2) - Used for main section titles */
.section-heading {
  font-family: var(--font-family-inter);
  font-size: var(--dia-section-heading-size); /* 36px on mobile */
  font-weight: var(--dia-section-heading-weight); /* 350 */
  line-height: var(--dia-section-heading-line-height); /* 4.1rem */
  letter-spacing: var(--dia-section-heading-spacing);
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-8) 0;
}

/* Section Descriptions - Used for main section descriptions */
.section-description {
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size); /* 14px on mobile */
  font-weight: var(--dia-body-text-weight); /* 400 */
  line-height: var(--dia-body-text-line-height); /* 1.7rem */
  color: var(--dia-color-text-secondary);
  margin: 0 0 var(--dia-space-10) 0;
}

/* Subheadings (H2) - Used for sub-section titles */
.section-subheading {
  font-family: var(--font-family-inter);
  font-size: var(--dia-section-heading-size); /* Same as section-heading */
  font-weight: var(--dia-section-heading-weight); /* 350 */
  line-height: var(--dia-section-heading-line-height);
  letter-spacing: var(--dia-section-heading-spacing);
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-6) 0;
}

/* Subheading Descriptions - Used for sub-section descriptions */
.section-subheading-description {
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size); /* diabrowser body text */
  font-weight: var(--dia-body-text-weight); /* 400 */
  line-height: var(--dia-body-text-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  color: var(--dia-color-text-secondary);
  margin: 0 0 var(--dia-space-6) 0;
}

/* Centered text variant */
.section-heading.text-center,
.section-description.text-center,
.section-subheading.text-center,
.section-subheading-description.text-center {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}

/* Max widths matching diabrowser.com patterns */
.section-heading.max-width {
  max-width: 800px; /* Matches diabrowser.com heading widths */
}

.section-description.max-width {
  max-width: 600px; /* Matches diabrowser.com description widths */
}

/* Additional content width classes for diabrowser.com compliance */
.content-width-narrow {
  max-width: 480px;
  margin-left: auto;
  margin-right: auto;
}

.content-width-medium {
  max-width: 640px;
  margin-left: auto;
  margin-right: auto;
}

.content-width-wide {
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* ==========================================
   DIABROWSER CONTAINER STANDARDS
   Based on diabrowser.com analysis
   ========================================== */

/* Content container classes - for text content */
.dia-container-text {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
}

.dia-container-narrow {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
}

.dia-container-form {
  max-width: 480px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
}

/* Component container classes - for cards, grids, carousels */
.dia-container-cards {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
}

.dia-container-wide {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
}

/* Full-width container */
.dia-container-full {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
}

/* ==========================================
   DIABROWSER RESPONSIVE CONTAINER SYSTEM
   17-breakpoint progressive container sizing
   ========================================== */

/* Base container - mobile first */
.container {
  width: 100%;
  max-width: 100vw; /* Never exceed viewport width */
  margin: 0 auto;
  padding: 0 var(--dia-space-6); /* 16px horizontal padding */
  box-sizing: border-box;
}

/* Micro Mobile (400px+) */
@media (min-width: 400px) {
  .container {
    max-width: min(400px, 100vw); /* Never exceed viewport */
    /* Keep unified padding - don't override */
  }
}

/* Small Mobile (600px+) */
@media (min-width: 600px) {
  .container {
    max-width: min(600px, 100vw); /* Never exceed viewport */
    /* Keep unified padding - don't override */
  }
}

/* Large Mobile (700px+) */
@media (min-width: 700px) {
  .container {
    max-width: min(700px, 100vw); /* Never exceed viewport */
    /* Keep unified padding - don't override */
  }
}

/* Tablet Portrait (800px+) - MAJOR TYPOGRAPHY JUMP */
@media (min-width: 800px) {
  .container {
    max-width: min(800px, 100vw); /* Never exceed viewport */
    padding: 0 var(--dia-space-8); /* 20px - OK to increase on larger screens */
  }
}

/* Large Tablet (900px+) */
@media (min-width: 900px) {
  .container {
    max-width: min(900px, 100vw); /* Never exceed viewport */
    padding: 0 var(--dia-space-9); /* 24px - OK to increase on larger screens */
  }
}

/* Small Desktop (1000px+) - BODY TEXT REFINEMENT */
@media (min-width: 1000px) {
  .container {
    max-width: min(1000px, 100vw); /* Never exceed viewport */
    padding: 0 var(--dia-space-10); /* 30px - OK to increase on larger screens */
  }
}

/* Medium Desktop (1100px+) */
@media (min-width: 1100px) {
  .container {
    max-width: min(1100px, 100vw); /* Never exceed viewport */
  }
}

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .container {
    max-width: min(1200px, 100vw); /* Never exceed viewport */
  }
}

/* XL Desktop (1300px+) */
@media (min-width: 1300px) {
  .container {
    max-width: min(1300px, 100vw); /* Never exceed viewport */
  }
}

/* XXL Desktop (1400px+) */
@media (min-width: 1400px) {
  .container {
    max-width: min(1400px, 100vw); /* Never exceed viewport */
  }
}

/* Ultra Desktop (1500px+) */
@media (min-width: 1500px) {
  .container {
    max-width: min(1500px, 100vw); /* Never exceed viewport */
  }
}

/* Wide Desktop (1600px+) */
@media (min-width: 1600px) {
  .container {
    max-width: min(1600px, 100vw); /* Never exceed viewport */
  }
}

/* Super Wide (1700px+) */
@media (min-width: 1700px) {
  .container {
    max-width: min(1700px, 100vw); /* Never exceed viewport */
  }
}

/* Mega Wide (1800px+) */
@media (min-width: 1800px) {
  .container {
    max-width: min(1800px, 100vw); /* Never exceed viewport */
  }
}

/* Giga Wide (1900px+) */
@media (min-width: 1900px) {
  .container {
    max-width: min(1900px, 100vw); /* Never exceed viewport */
  }
}

/* Maximum Width (2000px+) */
@media (min-width: 2000px) {
  .container {
    max-width: min(2000px, 100vw); /* Never exceed viewport */
  }
}

/* ==========================================
   DIABROWSER CONTAINER RESPONSIVE BEHAVIOR
   IMPORTANT: These rules must come LAST to override progressive container padding
   ========================================== */

/* Unified responsive padding for all diabrowser containers - OVERRIDE ALL PREVIOUS PADDING */
@media (max-width: 800px) {
  .container,
  .dia-container-text,
  .dia-container-narrow,
  .dia-container-form,
  .dia-container-cards,
  .dia-container-wide,
  .dia-container-full {
    padding: 0 var(--dia-space-6) !important; /* Force unified 16px padding */
  }
}

@media (max-width: 480px) {
  .container,
  .dia-container-text,
  .dia-container-narrow,
  .dia-container-form,
  .dia-container-cards,
  .dia-container-wide,
  .dia-container-full {
    padding: 0 var(--dia-space-4) !important; /* Force unified 12px padding */
  }
}


