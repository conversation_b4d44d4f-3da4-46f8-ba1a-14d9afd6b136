/* Case Study Card Styles */

.case-study-card {
  width: 100%;
  height: 500px; /* Increased height to accommodate content better */
  border-radius: var(--card-border-radius);
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: var(--card-transition);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
}

.case-study-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  border-color: var(--dia-color-card-border-hover);
}

/* Full container background image */
.case-study-card-background {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  transition: transform 0.3s ease;
}

.case-study-card:hover .case-study-card-background {
  transform: scale(1.02);
}

/* Content overlay */
.case-study-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: var(--dia-space-8);
}

/* Content container */
.case-study-card-content {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-8); /* 32px spacing between elements */
  width: 100%;
  z-index: 2;
  align-items: flex-start; /* Ensure all children align to the left */
}

/* Logo styling */
.case-study-card-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%; /* Take full width for consistent alignment */
}

.case-study-card-logo img {
  max-height: 40px;
  max-width: 120px;
  object-fit: contain;
  filter: brightness(0) invert(1); /* Make logo white */
}

/* Bunkie Life logo - Use original colors (opposite filter) */
.case-study-card-logo img[src="/images/logos/14.png"] {
  filter: brightness(1) invert(0) !important; /* Keep original colors */
}

/* Title styling */
.case-study-card-title {
  color: var(--dia-color-white);
  font-family: var(--font-family-inter);
  font-size: var(--dia-carousel-card-title-size);
  font-weight: var(--dia-carousel-card-title-weight);
  line-height: var(--dia-carousel-card-title-line-height);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  width: 100%; /* Take full width for consistent alignment */
  text-align: left; /* Ensure left alignment */
}

/*
 * CASE STUDY CTA - Using Unified CTA System
 * Special white variant for overlay context
 */

.case-study-card-cta {
  /* Use CTA base with special white styling for overlay */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--cta-gap);
  padding: var(--cta-padding-mobile);
  border-radius: var(--cta-border-radius);
  height: var(--cta-height);
  min-width: var(--cta-min-width-mobile);
  cursor: pointer;
  position: relative;
  text-align: center;
  flex-shrink: 0;
  text-decoration: none;
  font-family: var(--font-family-inter);
  font-size: var(--dia-btn-cta-size);
  font-weight: var(--dia-btn-cta-weight);
  line-height: var(--dia-btn-cta-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  transition: var(--cta-transition);
  white-space: nowrap;
  outline: none;
  align-self: flex-start; /* Align to left */

  /* Special white styling for overlay context */
  background: var(--dia-color-white);
  color: var(--dia-color-text-primary);
  border: 1px solid var(--dia-color-white);
  box-shadow: var(--cta-primary-shadow);
}

.case-study-card-cta:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--cta-primary-shadow-hover);
  transform: var(--cta-hover-transform);
}

.case-study-card-cta .button-text-secondary {
  color: var(--dia-color-text-primary);
}

/* Responsive adjustments */
@media (max-width: 800px) {
  .case-study-card {
    height: 450px; /* Increased for better content spacing */
  }

  .case-study-card-overlay {
    padding: var(--dia-space-6);
  }

  .case-study-card-content {
    gap: var(--dia-space-6); /* 24px spacing on mobile */
    align-items: flex-start; /* Ensure all children align to the left */
    justify-content: flex-start;
  }

  .case-study-card-logo img {
    max-height: 32px;
    max-width: 100px;
  }

  .case-study-card-title {
    font-size: var(--font-size-20);
    line-height: var(--line-height-28);
  }
}

@media (max-width: 480px) {
  .case-study-card {
    height: 400px; /* Increased for better content spacing */
  }

  .case-study-card-overlay {
    padding: var(--dia-space-5);
  }

  .case-study-card-content {
    gap: var(--dia-space-5); /* 20px spacing on small mobile */
  }

  .case-study-card-logo img {
    max-height: 28px;
    max-width: 80px;
  }

  .case-study-card-title {
    font-size: var(--font-size-18);
    line-height: var(--line-height-24);
  }
}
