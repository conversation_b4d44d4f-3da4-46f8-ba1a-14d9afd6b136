import React, { useState } from "react";
import FadeIn from "./animations/FadeIn";

interface BentoCardData {
  icon: () => React.ReactElement;
  description: string;
  revealText: string;
}

interface BentoAltCardProps {
  icon: () => React.ReactElement;
  description: string;
  revealText: string;
}

const BentoAltCard: React.FC<BentoAltCardProps> = ({ icon: Icon, description, revealText }) => {
  const [isRevealed, setIsRevealed] = useState(false);

  const toggleReveal = () => {
    setIsRevealed(!isRevealed);
  };

  // Function to render text with bold formatting
  const renderTextWithBold = (text: string) => {
    const parts = text.split(/\*\*(.*?)\*\*/g);
    return parts.map((part, index) => {
      if (index % 2 === 1) {
        return <strong key={index}>{part}</strong>;
      }
      return part;
    });
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't toggle if clicking on the toggle button itself
    if ((e.target as Element).closest('.bento-alt-toggle') || (e.target as Element).closest('.bento-alt-overlay-toggle')) {
      return;
    }
    toggleReveal();
  };

  return (
    <div
      className="bento-alt-card relative cursor-pointer overflow-hidden flex flex-col"
      style={{
        background: 'var(--dia-color-card-background)',
        borderRadius: '16px', // Material Design standard radius
        border: '1px solid rgba(0, 0, 0, 0.04)',
        padding: '3rem', // 48dp - Material Design card padding
        transition: 'all 0.2s ease',
        boxShadow: 'var(--dia-shadow-sm)',
        minHeight: '18.75rem' // 300px in rem - Material Design card height
      }}
      onClick={handleCardClick}
    >
      {/* Icon */}
      <div
        className="flex items-center justify-center flex-shrink-0"
        style={{
          width: '6rem', // 96dp - Material Design large icon container
          height: '6rem',
          color: 'var(--dia-color-black)',
          backgroundColor: '#F9F6F2',
          borderRadius: '9999px'
        }}
      >
        <Icon />
      </div>

      {/* Content */}
      <div className="flex flex-col w-full flex-1" style={{ marginTop: '1.5rem' }}> {/* 24dp - Material Design content spacing */}
        <div
          className="text-text-primary"
          style={{
            fontSize: 'var(--dia-font-size-xl)',
            fontWeight: 'var(--dia-font-weight-medium)',
            lineHeight: '1.3',
            letterSpacing: '-0.02em',
            marginBottom: '1.5rem' // 24dp - Material Design text spacing
          }}
        >
          {renderTextWithBold(description)}
        </div>

        {/* Toggle button container for right alignment */}
        <div className="flex justify-end w-full">
          <button
            className={`flex items-center justify-center flex-shrink-0 border-none cursor-pointer transition-all duration-200 ${
              isRevealed ? 'bg-gray-300' : 'bg-white bg-opacity-90'
            }`}
            style={{
              borderRadius: '9999px',
              width: '36px',
              height: '36px',
              color: 'var(--dia-color-black)',
              boxShadow: '0 1px 2px 0 #0000004d,0 1px 3px 1px #00000026'
            }}
            onClick={toggleReveal}
            aria-label={isRevealed ? 'Hide details' : 'Show details'}
          >
            <svg
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={`w-4 h-4 transition-transform duration-200 ${isRevealed ? 'rotate-45' : ''}`}
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
        </div>
      </div>

      {/* Overlay */}
      <div
        className={`absolute inset-0 flex flex-col transition-all duration-300 ${
          isRevealed ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
        style={{
          background: 'var(--dia-color-white)',
          borderRadius: '16px', // Material Design standard radius
          padding: '3rem' // 48dp - Material Design overlay padding
        }}
        onClick={handleCardClick}
      >
        <div
          className="text-text-primary flex-1"
          style={{
            fontSize: 'var(--dia-font-size-base)',
            fontWeight: 'var(--dia-font-weight-normal)',
            lineHeight: '1.5',
            letterSpacing: '-0.01em'
          }}
        >
          {renderTextWithBold(revealText)}
        </div>

        {/* Overlay toggle button container for right alignment */}
        <div className="flex justify-end w-full" style={{ marginTop: '1.5rem' }}> {/* 24dp - Material Design button spacing */}
          <button
            className="flex items-center justify-center flex-shrink-0 bg-white border-none cursor-pointer transition-all duration-200"
            style={{
              borderRadius: '9999px',
              width: '36px',
              height: '36px',
              color: 'var(--dia-color-black)',
              boxShadow: '0 1px 2px 0 #0000004d,0 1px 3px 1px #00000026'
            }}
            onClick={toggleReveal}
            aria-label="Hide details"
          >
            <svg
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-4 h-4 rotate-45"
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

const ProblemSection: React.FC = () => {
  // Define bento-alt cards data
  const bentoAltCardsData: BentoCardData[] = [
    {
      icon: () => (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
          <path d="M9 12l2 2 4-4"/>
          <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
        </svg>
      ),
      description: "**Only 16% of brands** know true profit per SKU.",
      revealText: "Most teams still see revenue, not real margin. When shipping, payment fees and returns hide inside 'overhead,' bids are blindfolded. We factor those costs to every order so you finally see contribution at the SKU level, and scale what actually makes money."
    },
    {
      icon: () => (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      ),
      description: "**56%** of product searches **start on Google.**",
      revealText: "Shoppers may start on Meta, but the sale still comes home to Google. We sync your profit-structured data to those discovery channels, then retarget the click back through high-intent Google campaigns."
    },
    {
      icon: () => (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
          <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
        </svg>
      ),
      description: "CAC has climbed **60% in five years.**",
      revealText: "Ad inflation turns a 'good' 3× ROAS into shrinking cash flow. PAX fights back with margin-based bidding, SKU pruning and payback-window targets that keep acquisition costs under control, even when clicks get pricier."
    }
  ];

  return (
    <section
      className="bg-background w-full max-w-full overflow-x-hidden"
      style={{
        padding: '8rem 0', // 128dp - Material Design large section padding
        color: 'var(--dia-color-text-secondary)'
      }}
    >
      <div className="w-full max-w-screen-2xl mx-auto px-4 md:px-6 lg:px-8">
        {/* Section Header - Centered with logical width constraints */}
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="text-center max-w-4xl mx-auto">
            <h2
              className="section-heading text-text-primary"
              style={{ marginBottom: '1.5rem' }} // 24dp - Material Design heading spacing
            >
              Why Most DTC Brands Waste 30% of Their Ad Spend
            </h2>
            <p className="body-text text-text-secondary">
              Following vanity metrics can hide serious profit leaks.
            </p>
          </div>
        </FadeIn>

        {/* Cards Grid - Proper responsive layout */}
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto"
          style={{ marginTop: '4rem' }} // 64dp - Material Design content spacing
        >
          {bentoAltCardsData.map((card, index) => (
            <FadeIn key={index} threshold={0.1} rootMargin="0px" className={`fade-up delay-${(index + 1) * 100}`}>
              <BentoAltCard
                icon={card.icon}
                description={card.description}
                revealText={card.revealText}
              />
            </FadeIn>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProblemSection;
