import React from "react";
import FadeIn from "./animations/FadeIn";
import ScaleIn from "./animations/ScaleIn";
import { Carousel, Card } from "./ui/FunnelVisionCardsCarousel";

// Content component for carousel cards
const CardContent: React.FC<{ title: string; description: string }> = ({ title, description }) => {
  return (
    <div className="space-y-4">
      <p className="text-lg font-semibold text-primary">
        {title}
      </p>
      <p className="body-text text-text-secondary">
        {description}
      </p>
    </div>
  );
};

const SearchMarketingSuiteSection: React.FC = () => {
  // Define cards data for the search marketing features
  const searchFeatureCardsData = [
    {
      category: "Google Ads",
      title: "Reactive visual descriptions",
      src: "https://images.unsplash.com/photo-1609250291996-fdebe6020a8f?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3",
      content: <CardContent 
        title="Dynamic visual awareness" 
        description="It reacts to changes in the visual world, like describing what it sees as the camera view moves. The system continuously analyzes the environment and provides contextual information in real-time." 
      />
    },
    {
      category: "Meta Integration",
      title: "Cross-platform optimization",
      src: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?q=80&w=2039&auto=format&fit=crop&ixlib=rb-4.0.3",
      content: <CardContent 
        title="Seamless platform integration" 
        description="Connect your Meta discovery campaigns with Google search intent. Our system syncs profit-structured data across platforms for maximum conversion efficiency." 
      />
    },
    {
      category: "AI Discovery",
      title: "Designed for accessibility",
      src: "https://images.unsplash.com/photo-1617791160505-6f00504e3519?q=80&w=2069&auto=format&fit=crop&ixlib=rb-4.0.3",
      content: <CardContent 
        title="User-centric design" 
        description="Developed with input from the DTC community, the interface provides clear, actionable insights that are relevant to your business needs and profit goals." 
      />
    }
  ];
  
  const searchFeatureCards = searchFeatureCardsData.map((card, index) => (
    <Card key={card.title} card={card} index={index} />
  ));

  return (
    <section className="section-base">
      <div className="container-base">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="text-center mb-16">
            <h2 className="section-heading text-text-primary mb-6">
              Your Complete Profit-First Search Marketing Suite
            </h2>
            <div className="max-w-3xl mx-auto">
              <p className="body-text text-text-secondary">
                FunnelVision transforms how DTC brands approach digital advertising. Instead of isolated channel strategies, we build integrated search ecosystems where every platform works together to maximize profit.
              </p>
            </div>
          </div>
        </FadeIn>

        <div className="mb-16">
          <ScaleIn className="delay-200">
            <div className="w-full">
              <Carousel items={searchFeatureCards} />
            </div>
          </ScaleIn>
        </div>

        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-400">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-semibold text-text-primary mb-4">
              Your campaigns benefit from our partnerships:
            </h3>
          </div>
        </FadeIn>

        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-500">
          <div className="text-center">
            <div className="max-w-2xl mx-auto mb-8">
              <p className="body-text text-text-secondary">
                We've partnered with Formen Norden Ecom Labs—the premier e-commerce research lab—to provide our clients with the latest and most impactful ROI-focused research, best practices, industry developments, and actionable strategies.
              </p>
            </div>
            <a 
              href="#waitlist" 
              className="inline-flex items-center gap-2 bg-primary text-white px-8 py-4 rounded-standard font-medium hover:bg-primary-hover transition-colors"
            >
              Secure your spot
              <svg
                width="18"
                height="18"
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-4 h-4"
              >
                <path
                  d="M9.5 3.5L15.5 9.5L9.5 15.5M15.5 9.5H3.5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </a>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default SearchMarketingSuiteSection;
