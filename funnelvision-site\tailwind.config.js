/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: '1.6rem',
      },

      // Material Design Grid System
      container: {
        center: true,
        padding: {
          DEFAULT: '1rem',    // 16dp for xs/sm
          'sm': '1rem',       // 16dp for sm
          'md': '1.5rem',     // 24dp for md
          'lg': '1.5rem',     // 24dp for lg
          'xl': '1.5rem',     // 24dp for xl
          '2xl': '1.5rem',    // 24dp+ for xxl (auto margins)
        },
        screens: {
          'sm': '360px',      // sm: 360-599px
          'md': '600px',      // md: 600-904px
          'lg': '905px',      // lg: 905-1239px
          'xl': '1240px',     // xl: 1240-1439px
          '2xl': '1440px',    // xxl: ≥1440px
          'desktop': '1025px', // Header desktop breakpoint (original uses max-width: 1024px)
        },
      },
    },
    extend: {
      // FunnelVision Design System - Diabrowser Integration
      colors: {
        // Brand Colors
        primary: {
          DEFAULT: '#1d3c2a',
          hover: '#264d37',
          light: 'rgba(29, 60, 42, 0.05)',
          dark: '#1b3d3c',
        },

        // Text Colors - Optimized for light mode comfort
        text: {
          primary: '#202124',
          secondary: '#202124',
          highlight: '#96969e',
          muted: 'hsl(0, 0%, 55%)',
          light: '#96969e',
          white: '#FFFFFF',
          'white-secondary': 'rgba(255, 255, 255, 0.7)',
          'white-muted': 'rgba(255, 255, 255, 0.5)',
        },

        // Background Colors - Light mode hierarchy
        background: {
          // Match design tokens --dia-color-background (#FFFFFF)
          DEFAULT: '#FFFFFF',
          white: '#FFFFFF',
          light: '#F9F6F2',
          elevated: '#FAFAFA',
          overlay: 'rgba(0, 0, 0, 0.08)',
          'cta-primary': 'hsl(0, 0%, 15%)',
          'cta-secondary': '#F5F5F5',
        },

        // Card Colors - Enhanced white-based system (aligned with diabrowser tokens)
        card: {
          background: '#EFEAE5',           // --dia-color-card-background
          'background-alt': '#D6DBD9',     // --dia-color-card-background-alt
          'background-elevated': '#FDFDFD', // --dia-color-card-background-elevated
          'background-pressed': '#FDFDFD',  // --dia-color-card-background-pressed
          border: 'rgba(0, 0, 0, 0.04)',   // --dia-card-border
          'border-hover': 'rgba(0, 0, 0, 0.08)', // --dia-card-border-hover
          highlight: '#FFFFFF',             // --dia-color-card-highlight
          shadow: '#f2f2f3',               // --dia-color-card-shadow-light
        },

        // UI State Colors
        error: '#E53935',
        success: '#4CAF50',
        warning: '#FFC107',

        // Accent Colors
        teal: {
          green: '#1b3d3c',
        },

        // Header & Footer
        header: {
          background: 'rgba(250, 250, 250, 0.95)',
          'background-scrolled': 'rgba(250, 250, 250, 0.98)',
          border: 'rgba(0, 0, 0, 0.04)',
        },
        footer: {
          background: '#1b3d3c',
          text: '#FFFFFF',
          'text-secondary': 'rgba(255, 255, 255, 0.7)',
          'text-muted': 'rgba(255, 255, 255, 0.5)',
          border: 'rgba(255, 255, 255, 0.08)',
        },
      },

      // Diabrowser spacing grid - CORRECTED to exact pixel match
      spacing: {
        // Design token utilities - exact pixel match with original
        'dia-1': 'var(--dia-space-1)',   // 6px
        'dia-2': 'var(--dia-space-2)',   // 8px
        'dia-3': 'var(--dia-space-3)',   // 10px
        'dia-4': 'var(--dia-space-4)',   // 12px
        'dia-5': 'var(--dia-space-5)',   // 14px
        'dia-6': 'var(--dia-space-6)',   // 16px
        'dia-7': 'var(--dia-space-7)',   // 18px
        'dia-8': 'var(--dia-space-8)',   // 20px
        'dia-9': 'var(--dia-space-9)',   // 24px
        'dia-10': 'var(--dia-space-10)', // 30px
        'dia-11': 'var(--dia-space-11)', // 40px
        'dia-12': 'var(--dia-space-12)', // 50px
        'dia-13': 'var(--dia-space-13)', // 60px
        'dia-14': 'var(--dia-space-14)', // 80px
        'dia-15': 'var(--dia-space-15)', // 100px
        'dia-16': 'var(--dia-space-16)', // 160px
        'dia-18': 'var(--dia-space-18)', // 180px

        // Standard Tailwind spacing (for compatibility)
        '0.5': '0.125rem', // 2px
        '1': '0.25rem',    // 4px
        '1.5': '0.375rem', // 6px
        '2': '0.5rem',     // 8px
        '2.5': '0.625rem', // 10px
        '3': '0.75rem',    // 12px
        '4': '1rem',       // 16px
        '5': '1.25rem',    // 20px
        '6': '1.5rem',     // 24px
        '7': '1.75rem',    // 28px
        '8': '2rem',       // 32px
        '9': '2.25rem',    // 36px
        '10': '2.5rem',    // 40px
        '11': '2.75rem',   // 44px
        '12': '3rem',      // 48px
        '14': '3.5rem',    // 56px
        '16': '4rem',      // 64px
        '20': '5rem',      // 80px
        '24': '6rem',      // 96px
        '32': '8rem',      // 128px
      },

      // Diabrowser typography scale - CORRECTED to exact pixel match
      fontSize: {
        // Design token utilities - exact pixel match with original
        'dia-xs': 'var(--dia-font-size-xs)',     // 13px
        'dia-sm': 'var(--dia-font-size-sm)',     // 14px
        'dia-base': 'var(--dia-font-size-base)', // 16px
        'dia-lg': 'var(--dia-font-size-lg)',     // 18px
        'dia-xl': 'var(--dia-font-size-xl)',     // 20px
        'dia-2xl': 'var(--dia-font-size-2xl)',   // 22px
        'dia-3xl': 'var(--dia-font-size-3xl)',   // 26px
        'dia-4xl': 'var(--dia-font-size-4xl)',   // 32px
        'dia-5xl': 'var(--dia-font-size-5xl)',   // 48px
        'dia-6xl': 'var(--dia-font-size-6xl)',   // 64px
        'dia-7xl': 'var(--dia-font-size-7xl)',   // 72px
        'dia-8xl': 'var(--dia-font-size-8xl)',   // 84px

        // Standard Tailwind typography (corrected to exact pixel match)
        'xs': ['0.8125rem', { lineHeight: '1.4' }],  // 13px - EXACT match
        'sm': ['0.875rem', { lineHeight: '1.4' }],   // 14px - EXACT match
        'base': ['1rem', { lineHeight: '1.5' }],     // 16px - EXACT match
        'lg': ['1.125rem', { lineHeight: '1.5' }],   // 18px - EXACT match
        'xl': ['1.25rem', { lineHeight: '1.4' }],    // 20px - EXACT match
        '2xl': ['1.375rem', { lineHeight: '1.4' }],  // 22px - EXACT match
        '3xl': ['1.625rem', { lineHeight: '1.3' }],  // 26px - EXACT match
        '4xl': ['2rem', { lineHeight: '1.2' }],      // 32px - EXACT match
        '5xl': ['3rem', { lineHeight: '1.1' }],      // 48px - EXACT match
        '6xl': ['4rem', { lineHeight: '1.0' }],      // 64px - EXACT match
        '7xl': ['4.5rem', { lineHeight: '1.0' }],    // 72px - EXACT match
        '8xl': ['5.25rem', { lineHeight: '1.0' }],   // 84px - EXACT match

        // Component-specific sizes (corrected)
        'hero-title': ['3rem', { lineHeight: '3.25rem', letterSpacing: '-0.04em' }],      // 48px mobile
        'hero-secondary': ['2.25rem', { lineHeight: '2.5625rem', letterSpacing: '-0.04em' }], // 36px mobile
        'section-heading': ['2.25rem', { lineHeight: '2.5625rem', letterSpacing: '-0.04em' }], // 36px mobile
        'body-text': ['0.875rem', { lineHeight: '1.0625rem', letterSpacing: '-0.02em' }], // 14px mobile
      },

      // Diabrowser font weights - lighter approach
      fontWeight: {
        light: '350',    // For headings - diabrowser approach
        normal: '400',   // For body text
        medium: '500',   // Rarely used
        semibold: '600', // h3/h4 headings
      },

      // Universal 16px radius system
      borderRadius: {
        'standard': '16px', // Universal standard radius
        'sm': '16px',       // Standardized to 16px
        'md': '16px',       // Standardized to 16px
        'lg': '16px',       // Standardized to 16px
        'xl': '16px',       // Standardized to 16px
        'full': '9999px',   // Circles and pills only
      },

      // Material Design Grid breakpoints
      screens: {
        '2xs': { 'max': '359px' },  // xs: 0-359px (4 columns, 16dp gutter/margin)
      },

      // Enhanced light mode shadow system - two-shadow technique (aligned with diabrowser tokens)
      boxShadow: {
        'sm': '0 1px 2px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(255, 255, 255, 0.3)',     // --dia-shadow-sm
        'md': '0 2px 4px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(255, 255, 255, 0.4)',    // --dia-shadow-md
        'lg': '0 4px 8px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(255, 255, 255, 0.5)',   // --dia-shadow-lg
        'xl': '0 8px 16px rgba(0, 0, 0, 0.15), 0 16px 32px rgba(255, 255, 255, 0.6)',  // --dia-shadow-xl

        // Mobile-optimized shadows for performance
        'sm-mobile': '0 1px 2px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(255, 255, 255, 0.2)', // --dia-shadow-sm-mobile
        'md-mobile': '0 1px 3px rgba(0, 0, 0, 0.06), 0 4px 8px rgba(255, 255, 255, 0.3)', // --dia-shadow-md-mobile
      },

      // Animation and transitions
      transitionTimingFunction: {
        'smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },

      transitionDuration: {
        'base': '200ms',
        'smooth': '300ms',
      },
    },
  },
  plugins: [],
}
