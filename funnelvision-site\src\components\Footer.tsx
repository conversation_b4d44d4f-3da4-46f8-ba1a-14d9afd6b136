import React from "react";
import FadeIn from "./animations/FadeIn";

const Footer: React.FC = () => {
  const handlePrivacyClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    window.history.pushState({}, '', '/privacy');
    window.location.reload();
  };

  return (
    <footer
      className="simple-footer w-full max-w-full box-border overflow-x-hidden"
      style={{
        backgroundColor: 'var(--dia-color-text-primary)',
        color: 'var(--dia-color-background)',
        padding: 'var(--dia-space-12) 0 var(--dia-space-6) 0',
        marginTop: 'var(--dia-space-14)'
      }}
    >
      <div className="dia-container-cards">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div
            className="footer-main flex justify-between items-start w-full max-w-full box-border tablet:flex-row flex-col"
            style={{
              marginBottom: 'var(--dia-space-8)',
              gap: 'var(--dia-space-8)'
            }}
          >
            {/* Logo and Tagline */}
            <div className="footer-brand flex-1 max-w-[480px] min-w-0 box-border">
              <h3
                className="footer-logo m-0"
                style={{
                  fontFamily: 'var(--font-family-inter)',
                  fontSize: 'var(--dia-font-size-xl)',
                  fontWeight: 'var(--dia-font-weight-semibold)',
                  lineHeight: 'var(--dia-line-height-tight)',
                  color: 'var(--dia-color-background)',
                  margin: '0 0 var(--dia-space-2) 0'
                }}
              >
                FunnelVision
              </h3>
              <p
                className="footer-tagline m-0"
                style={{
                  fontFamily: 'var(--font-family-inter)',
                  fontSize: 'var(--dia-font-size-base)',
                  fontWeight: 'var(--dia-font-weight-normal)',
                  lineHeight: 'var(--dia-line-height-relaxed)',
                  color: 'rgba(255, 255, 255, 0.8)',
                  margin: '0'
                }}
              >
                Profit-First Search Marketing for DTC Brands
              </p>
            </div>

            {/* Essential Links */}
            <nav
              className="footer-nav flex items-start flex-wrap min-w-0 box-border"
              style={{ gap: 'var(--dia-space-6)' }}
            >
              <a
                href="#about"
                className="footer-link no-underline transition-colors duration-200"
                style={{
                  fontFamily: 'var(--font-family-inter)',
                  fontSize: 'var(--dia-font-size-sm)',
                  fontWeight: 'var(--dia-font-weight-normal)',
                  lineHeight: 'var(--dia-line-height-normal)',
                  color: 'rgba(255, 255, 255, 0.8)'
                }}
              >
                About
              </a>
              <a
                href="#services"
                className="footer-link no-underline transition-colors duration-200"
                style={{
                  fontFamily: 'var(--font-family-inter)',
                  fontSize: 'var(--dia-font-size-sm)',
                  fontWeight: 'var(--dia-font-weight-normal)',
                  lineHeight: 'var(--dia-line-height-normal)',
                  color: 'rgba(255, 255, 255, 0.8)'
                }}
              >
                Services
              </a>
              <a
                href="#contact"
                className="footer-link no-underline transition-colors duration-200"
                style={{
                  fontFamily: 'var(--font-family-inter)',
                  fontSize: 'var(--dia-font-size-sm)',
                  fontWeight: 'var(--dia-font-weight-normal)',
                  lineHeight: 'var(--dia-line-height-normal)',
                  color: 'rgba(255, 255, 255, 0.8)'
                }}
              >
                Contact
              </a>
              <a
                href="/privacy"
                onClick={handlePrivacyClick}
                className="footer-link no-underline transition-colors duration-200"
                style={{
                  fontFamily: 'var(--font-family-inter)',
                  fontSize: 'var(--dia-font-size-sm)',
                  fontWeight: 'var(--dia-font-weight-normal)',
                  lineHeight: 'var(--dia-line-height-normal)',
                  color: 'rgba(255, 255, 255, 0.8)'
                }}
              >
                Privacy
              </a>
            </nav>
          </div>

          {/* Bottom Section */}
          <div
            className="footer-bottom flex justify-between items-center w-full max-w-full box-border tablet:flex-row flex-col"
            style={{
              paddingTop: 'var(--dia-space-6)',
              borderTop: '1px solid rgba(255, 255, 255, 0.15)'
            }}
          >
            {/* Social Links */}
            <div
              className="footer-social flex items-center mb-4 tablet:mb-0"
              style={{ gap: 'var(--dia-space-4)' }}
            >
              <a
                href="#"
                className="social-link flex items-center justify-center transition-colors duration-200"
                style={{
                  width: '32px',
                  height: '32px',
                  color: 'rgba(255, 255, 255, 0.8)'
                }}
                aria-label="Follow us on X"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 29 21"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                >
                  <path
                    d="M21.6866 -0.677734H25.6209L16.9827 9.15785L27.0749 22.5001H19.1551L12.9544 14.3921L5.85561 22.5001H1.92136L11.0728 11.9802L1.4082 -0.677734H9.52471L15.1267 6.72891L21.6866 -0.677734ZM20.3097 20.1909H22.4906L8.37865 1.54597H6.03521L20.3097 20.1909Z"
                    fill="currentColor"
                  />
                </svg>
              </a>
              <a
                href="#"
                className="social-link flex items-center justify-center transition-colors duration-200"
                style={{
                  width: '32px',
                  height: '32px',
                  color: 'rgba(255, 255, 255, 0.8)'
                }}
                aria-label="Follow us on LinkedIn"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 29 21"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M22.7865 19.2048H19.2711V13.7902C19.2711 12.4991 19.2461 10.838 17.4427 10.838C15.6115 10.838 15.3321 12.2438 15.3321 13.6969V19.2048H11.8181V8.07032H15.1904V9.5915H15.2392C15.7083 8.71592 16.8569 7.79292 18.5681 7.79292C22.1281 7.79292 22.7865 10.0984 22.7865 13.0973V19.2048ZM7.85147 6.54718C6.72077 6.54718 5.81014 5.64819 5.81014 4.54051C5.81014 3.43411 6.72077 2.53448 7.85147 2.53448C8.9769 2.53448 9.89081 3.43411 9.89081 4.54051C9.89081 5.64819 8.9769 6.54718 7.85147 6.54718ZM6.08953 8.07032H9.61142V19.2048H6.08953V8.07032ZM24.179 -0.677734H4.29501C3.34549 -0.677734 2.57422 0.0754189 2.57422 1.00356V20.9718C2.57422 21.9011 3.34549 22.6556 4.29501 22.6556H24.179C25.1304 22.6556 25.9076 21.9011 25.9076 20.9718V1.00356C25.9076 0.0754189 25.1304 -0.677734 24.179 -0.677734Z"
                    fill="currentColor"
                  />
                </svg>
              </a>
            </div>

            {/* Copyright */}
            <div
              className="footer-copyright"
              style={{
                fontFamily: 'var(--font-family-inter)',
                fontSize: 'var(--dia-font-size-sm)',
                fontWeight: 'var(--dia-font-weight-normal)',
                color: 'rgba(255, 255, 255, 0.7)'
              }}
            >
              <span>&copy; FunnelVision 2025</span>
            </div>
          </div>
        </FadeIn>
      </div>
    </footer>
  );
};

export default Footer;
