/* Main animations file for FunnelVision*/

/* Fade-in animation */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Slide-up animation */
.slide-up {
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation for child elements */
.stagger {
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.stagger.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Micro-interaction animation classes for hover, active, and focus states */

/* Button interactions */
.button-hover {
  transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.button-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.button-active:active {
  transform: scale(0.98); /* Subtle scale reduction during active state */
}

/* Card interactions */
.card-hover {
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  outline: none !important;
  border: none !important;
}

.card-hover:hover,
.card-hover:focus,
.card-hover:active {
  box-shadow: 0 8px 24px rgba(59, 107, 255, 0.15);
  transform: scale(1.01); /* Subtle scale transform on hover */
  outline: none !important;
  border: none !important;
}

.card-active:active {
  transform: scale(0.98); /* Slight depression on active */
}

/* Link interactions */
.link-hover {
  transition: color 0.2s ease, text-decoration 0.2s ease;
}

.link-hover:hover {
  text-decoration: underline;
}

/* Header navigation animations */
.nav-link {
  position: relative;
  transition: color 0.2s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transition: width 0.2s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Focus states */
.focus-visible:focus-visible {
  outline: 2px solid rgba(26, 115, 232, 0.7);
  outline-offset: 2px;
}

/* Keyframes for specific animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .slide-up,
  .stagger,
  .button-hover,
  .card-hover,
  .link-hover,
  .nav-link {
    transition: none;
    transform: none;
    animation: none;
  }
  
  .nav-link::after {
    transition: none;
  }
}
