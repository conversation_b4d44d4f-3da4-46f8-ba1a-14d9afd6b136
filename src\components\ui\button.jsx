import React from "react";
import { cn } from "../../lib/utils";

// Button variants
const buttonVariants = {
  default: "bg-neutral-900 text-white hover:bg-neutral-800",
  ghost: "bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800",
  link: "bg-transparent underline-offset-4 hover:underline text-neutral-900",
};

// Button sizes
const buttonSizes = {
  default: "h-10 px-4 py-2",
  sm: "h-9 rounded-[16px] px-3",
  lg: "h-11 rounded-[16px] px-8",
};

const Button = ({
  className,
  variant = "default",
  size = "default",
  asChild = false,
  children,
  ...props
}) => {
  const Comp = asChild ? "span" : props.href ? "a" : "button";
  
  return (
    <Comp
      className={cn(
        "inline-flex items-center justify-center rounded-[16px] text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neutral-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
        buttonVariants[variant],
        buttonSizes[size],
        className
      )}
      {...props}
    >
      {children}
    </Comp>
  );
};

export { Button, buttonVariants };
