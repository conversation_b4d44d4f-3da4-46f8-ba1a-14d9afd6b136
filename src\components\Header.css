.header {
  width: 100%;
  max-width: 100vw; /* Never exceed viewport width */
  height: var(--dia-space-13);
  min-height: var(--dia-space-13);
  max-height: var(--dia-space-13);
  background-color: var(--dia-color-header-background);
  backdrop-filter: blur(8px);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--dia-color-header-border);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
  transform: translateY(0);
  will-change: transform;
  overflow: hidden; /* Prevent any overflow */
  box-sizing: border-box;
}

/* Allow menu to overflow when mobile menu is open */
.header.mobile-menu-open {
  overflow: visible;
}

.header.hidden {
  transform: translateY(-100%);
}

.header.scrolled {
  background-color: var(--dia-color-header-background-scrolled);
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-md);
  border-bottom: 1px solid var(--dia-color-card-border-hover);
}

/* Head<PERSON> uses standard .container class from diabrowser system */
.header .container {
  padding-top: var(--dia-space-5);
  padding-bottom: var(--dia-space-2);
  height: 100%;
  /* Horizontal padding handled by standard container system */
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  position: relative;
}

/* Logo section */
.header-logo {
  display: flex;
  align-items: center;
  gap: var(--dia-space-2);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--dia-space-2);
  text-decoration: none;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.logo-container:hover {
  transform: scale(1.02);
}

.header-logo-image {
  height: var(--dia-space-9);
  width: auto;
  display: block;
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(1142%) hue-rotate(121deg) brightness(0%) contrast(91%); /* Convert to solid #1d3c2a */
}

.deepmind-text {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-nav-brand-size);
  font-weight: var(--dia-nav-brand-weight);
  line-height: var(--dia-nav-brand-line-height);
  letter-spacing: var(--dia-section-heading-spacing);
}

.site-switcher {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 36px;
  background: transparent;
  border: 1px solid var(--color-white-15);
  border-radius: 1000px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.site-switcher:hover {
  background-color: var(--color-grey-958);
}

/* Navigation */
.header-nav {
  display: flex;
  align-items: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  gap: 0;
}

/* Mega Menu Container */
.mega-menu-container {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--dia-space-1) var(--dia-space-2);
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-nav-link-size);
  font-weight: var(--dia-nav-link-weight);
  line-height: var(--dia-nav-link-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  position: relative;
  margin-right: var(--dia-space-3);
  transition: color 0.2s ease;
  text-decoration: none;
  gap: var(--dia-space-1);
}

.dropdown-icon {
  margin-left: 2px;
  transition: transform 0.2s ease;
}

.nav-link.menu-open {
  color: var(--dia-color-text-secondary);
  background-color: var(--dia-color-text-primary);
}

.nav-link.menu-open .dropdown-icon {
  transform: rotate(180deg);
}

/* Mega Menu */
.mega-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 800px;
  background-color: var(--dia-color-header-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  margin-top: var(--dia-space-2);
  overflow: hidden;
  z-index: 1001;
  opacity: 1;
  animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate(-50%, -10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.mega-menu-content {
  display: flex;
  padding: var(--dia-space-4);
}

.mega-menu-section {
  flex: 1;
  padding: 0 var(--dia-space-3);
  border-right: 1px solid var(--color-border-light);
}

.mega-menu-section:last-child {
  border-right: none;
}

.mega-menu-section h3 {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-nav-link-size);
  font-weight: var(--dia-nav-link-active-weight);
  margin: 0 0 var(--dia-space-2) 0;
  letter-spacing: var(--letter-spacing-normal);
}

.mega-menu-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mega-menu-section ul li {
  margin-bottom: var(--dia-space-2);
}

.mega-menu-section ul li a {
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-nav-link-size);
  letter-spacing: var(--letter-spacing-normal);
  text-decoration: none;
  transition: color 0.2s ease;
  display: inline-block;
}

.mega-menu-section ul li a:hover {
  color: var(--dia-color-text-primary);
}

.mega-menu-footer {
  background-color: var(--dia-color-header-background);
  padding: var(--dia-space-2) var(--dia-space-4);
  text-align: right;
  border-top: 1px solid var(--color-border-light);
}

.see-all-link {
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-sm);
  font-weight: var(--dia-font-weight-medium);
  letter-spacing: var(--letter-spacing-normal);
  text-decoration: none;
  display: flex;
  align-items: center;
  margin-top: var(--dia-space-2);
  transition: color 0.2s ease;
}

.see-all-link:hover {
  color: var(--dia-color-text-primary);
}

.see-all-link svg {
  transition: transform 0.2s ease;
}

.see-all-link:hover svg {
  transform: translateX(4px);
}

.nav-link:hover {
  color: var(--dia-color-text-primary);
  border-radius: var(--radius-sm);
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--dia-color-text-primary);
  font-weight: var(--dia-nav-link-active-weight);
}

/* Action buttons */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--dia-space-2);
}

/*
 * HEADER CTA BUTTON - Using Unified CTA System
 * Consistent with hero section and site-wide standards
 */

/* Header CTA extends universal CTA system with compact sizing */
.header-actions .btn-primary {
  /* Balanced padding for header context with arrow */
  padding: var(--dia-space-3) var(--dia-space-6);
  font-size: var(--dia-btn-primary-size);
  line-height: var(--dia-btn-primary-line-height);
  letter-spacing: var(--letter-spacing-normal);
  font-weight: var(--dia-btn-primary-weight);
  border-radius: var(--cta-border-radius);
  min-width: auto; /* Allow compact sizing in header */
  height: auto; /* Allow flexible height in header */

  /* Maintain CTA visual consistency */
  background: var(--cta-primary-background);
  color: var(--cta-primary-text-color);
  box-shadow: var(--cta-primary-shadow);
  transition: var(--cta-transition);
  border: none;
}

.header-actions .btn-primary:hover {
  background: var(--cta-primary-background-hover);
  box-shadow: var(--cta-primary-shadow-hover);
  transform: var(--cta-hover-transform);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--dia-space-1);
  padding: var(--dia-space-2) var(--dia-space-4);
  background: var(--dia-color-text-primary);
  border-radius: var(--radius-full);
  color: var(--dia-color-white);
  font-family: var(--font-family-inter);
  font-size: var(--dia-btn-cta-size);
  font-weight: var(--dia-btn-cta-weight);
  line-height: var(--dia-btn-cta-line-height);
  letter-spacing: var(--letter-spacing-normal);
  text-decoration: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  border: none;
  box-shadow: var(--shadow-lg);
}

/* Pulse animation for the button */
@keyframes pulse {
  0% {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2); /* Start with base shadow */
  }
  70% {
    box-shadow: 0px 4px 20px 8px rgba(0, 0, 0, 0.3); /* Expand shadow */
  }
  100% {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2); /* Return to base shadow */
  }
}

.action-btn:hover {
  transform: translateY(-1px);
  animation: pulse 1.5s infinite;
  /* Base box-shadow from .action-btn will apply, pulse animation will modify it */
}

.action-btn:active {
  transform: translateY(0);
}

/* Header CTA uses universal arrow system */
.header-actions .btn:hover .cta-arrow {
  transform: var(--cta-arrow-hover-transform);
}

.header-actions .btn:hover .cta-text {
  transform: var(--cta-text-hover-transform);
}

/* Duplicate pulse animation removed - using the one above */

/* Duplicate hover effects removed - using the ones above */

.se.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--dia-space-2) var(--dia-space-4);
  height: 36px;
  background: #1d3c2a; /* New dark teal background */
  border-radius: 1000px;
  color: #ffffff; /* Keep text white for contrast */
  font-family: var(--font-family-inter);
  font-size: var(--font-size-14);
  font-weight: 500;
  letter-spacing: -0.01em; /* Adjusted for Inter font */
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(2px);
  border-radius: 1000px;
  cursor: pointer;
  border: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.search-btn:hover {
  background-color: rgba(255, 255, 255, 0.16);
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.search-btn:active {
  transform: translateY(0);
}

.search-btn .search-icon {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-btn:hover .search-icon {
  transform: scale(1.1);
}

/* Hamburger Menu */
.hamburger-menu {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 24px;
  height: 18px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 10000;
}

.hamburger-line {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--color-text-primary);
  transition: all 0.3s ease-in-out;
}

.hamburger-line.open:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
  transform: translateX(-20px);
}

.hamburger-line.open:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--dia-color-background);
  /* Temporary debug background */
  background: rgba(255, 255, 255, 0.98) !important;
  z-index: 9998 !important;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  width: 100vw;
  max-width: 100vw;
  padding-top: calc(var(--dia-space-8) + var(--dia-space-4));
  padding-bottom: var(--dia-space-6);
  padding-left: 0;
  padding-right: 0;
  pointer-events: none;
  visibility: hidden;
  border-top: 1px solid var(--color-border-light);
  box-sizing: border-box;
}

.mobile-nav.open {
  transform: translateX(0) !important;
  opacity: 1 !important;
  pointer-events: auto !important; /* Enable interactions when open */
  visibility: visible !important; /* Show when open */
  display: block !important;
  z-index: 9999 !important; /* Ensure it's above everything */
}

.mobile-nav-links {
  display: flex !important;
  flex-direction: column !important;
  padding: var(--dia-space-4) var(--dia-space-6) !important;
  min-height: calc(100vh - calc(var(--dia-space-8) + var(--dia-space-4)) - var(--dia-space-6)) !important;
  justify-content: flex-start !important;
  /* Temporary debug background */
  background: rgba(255, 255, 255, 0) !important;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: var(--dia-space-4) var(--dia-space-2);
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-nav-link-size);
  font-weight: var(--dia-nav-link-weight);
  border-bottom: 1px solid var(--color-border-light);
  text-decoration: none;
  transition: color 0.2s ease;
  min-height: 48px;
  width: 100%;
  box-sizing: border-box;
}

.mobile-nav-link:active,
.mobile-nav-link.active {
  color: var(--dia-color-text-primary);
  font-weight: var(--dia-nav-link-active-weight);
}

/* Mobile Mega Menu */
.mobile-mega-menu-container {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.mobile-mega-menu {
  background-color: #f5f5f7; /* Light gray background */
  border-radius: 8px;
  margin: var(--dia-space-2) var(--dia-space-3) var(--dia-space-4) var(--dia-space-3);
  border: 1px solid #00000029; /* Subtle border */
  overflow: visible; /* Allow content to be fully visible */
  position: relative;
  z-index: 2100; /* Higher z-index than mobile-nav */
  display: block;
  height: auto;
  max-height: none; /* No height restriction */
  width: calc(100% - 2 * var(--dia-space-3));
  box-sizing: border-box;
  animation: fadeIn 0.3s ease-out;
  will-change: opacity;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.mobile-mega-menu-section {
  padding: var(--dia-space-4);
  border-bottom: 1px solid #00000029; /* Updated border color for light theme */
}

.mobile-mega-menu-section h3 {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-nav-link-size);
  font-weight: var(--dia-nav-link-active-weight);
  margin: 0 0 var(--dia-space-4) 0;
  letter-spacing: var(--dia-body-text-spacing);
}

.mobile-mega-menu-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-mega-menu-section ul li {
  margin-bottom: var(--dia-space-3);
}

.mobile-mega-menu-section ul li:last-child {
  margin-bottom: 0;
}

.mobile-mega-menu-section ul li a {
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-nav-link-size);
  letter-spacing: var(--dia-body-text-spacing);
  text-decoration: none;
  display: block;
  padding: var(--dia-space-2) 0;
  transition: color 0.2s ease;
}

.mobile-mega-menu-section ul li a:hover {
  color: #1d3c2a; /* Dark teal on hover to match brand */
}

.mobile-mega-menu-footer {
  background-color: #f5f5f7; /* Light gray background */
  padding: var(--dia-space-4);
  text-align: right;
  border-bottom: 1px solid #00000029; /* Consistent border color */
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

/* Mobile CTA Button */
.mobile-cta-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--dia-space-9) var(--dia-space-6);
  padding: var(--dia-space-3) var(--dia-space-7);
  gap: var(--dia-space-2);
  border-radius: 1000px;
  text-decoration: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-size: var(--dia-btn-primary-size);
  font-weight: var(--dia-btn-primary-weight);
  line-height: var(--dia-btn-primary-line-height);
}

.mobile-cta-button:hover {
  transform: translateY(-1px);
  background-color: rgba(29, 60, 42, 0.05);
}

.mobile-cta-button:active {
  transform: translateY(0);
}

/* Mobile CTA uses universal arrow system */
.mobile-cta-button:hover .cta-arrow {
  transform: var(--cta-arrow-hover-transform);
}

.mobile-cta-button:hover .cta-text {
  transform: var(--cta-text-hover-transform);
}

/* Responsive design */
@media (max-width: 1024px) {
  .header-nav {
    display: none;
  }

  .header-actions {
    display: none;
  }
  
  .hamburger-menu {
    display: flex;
  }
  
  .mobile-nav {
    display: block !important;
  }
  
  /* Ensure mobile CTA button has appropriate size */
  .mobile-nav-actions .btn-secondary {
  padding: var(--dia-space-2) var(--dia-space-4);
  font-size: var(--dia-font-size-sm);
  line-height: var(--dia-line-height-base);
  width: 100%;
  justify-content: center;
  margin: var(--dia-space-4) 0;
}
}

/* Tablet specific fixes */
@media (max-width: 1024px) and (min-width: 769px) {
  .mobile-nav {
    padding-top: calc(var(--dia-space-8) + var(--dia-space-2));
  }

  .mobile-nav-links {
    padding: var(--dia-space-6) var(--dia-space-8);
  }

  .mobile-nav-link {
    font-size: var(--dia-font-size-lg);
    padding: var(--dia-space-5) var(--dia-space-3);
    min-height: 56px;
  }
}

@media (max-width: 768px) {
  .header .container {
    padding-top: var(--dia-space-5);
    padding-bottom: var(--dia-space-2);
    /* Horizontal padding handled by standard container system */
  }

  .mobile-nav {
    padding-top: calc(var(--dia-space-8) + var(--dia-space-3));
  }

  .mobile-nav-links {
    padding: var(--dia-space-3) var(--dia-space-4);
  }

  .mobile-nav-link {
    font-size: var(--dia-font-size-md);
    padding: var(--dia-space-3) var(--dia-space-2);
    min-height: 44px;
  }

  .header-logo-image {
    height: var(--dia-space-9);
  }

  .mobile-nav-links {
    padding: 0 var(--dia-space-6);
  }

  /* Adjust button size for tablet */
  .header-actions .btn-primary {
    font-size: var(--dia-btn-primary-size);
    padding: var(--dia-space-1) var(--dia-space-5);
  }

  .mobile-cta-button.btn-primary {
    margin: var(--dia-space-7) var(--dia-space-6);
    padding: var(--dia-space-2) var(--dia-space-6);
  }
}

@media (max-width: 480px) {
  .header .container {
    padding-top: var(--dia-space-5);
    padding-bottom: var(--dia-space-2);
    /* Horizontal padding handled by standard container system */
  }

  .header-logo-image {
    height: var(--dia-space-8);
  }

  /* Adjust button size for small mobile */
  .header-actions .btn-primary {
    font-size: var(--dia-btn-primary-size);
    padding: var(--dia-space-1) var(--dia-space-4);
  }

  .mobile-cta-button.btn-primary {
    margin: var(--dia-space-6) var(--dia-space-4);
    padding: var(--dia-space-2) var(--dia-space-5);
    font-size: var(--dia-btn-primary-size);
  }

  .mobile-nav {
    padding-top: calc(var(--dia-space-7) + var(--dia-space-2));
  }

  .mobile-nav-links {
    padding: var(--dia-space-2) var(--dia-space-3);
  }

  .mobile-nav-link {
    font-size: var(--dia-font-size-sm);
    padding: var(--dia-space-2) var(--dia-space-1);
    min-height: 40px;
  }

  .mobile-mega-menu {
    margin: var(--dia-space-1) var(--dia-space-2) var(--dia-space-3) var(--dia-space-2);
    width: calc(100% - 2 * var(--dia-space-2));
  }

  .mobile-mega-menu-section {
    padding: var(--dia-space-3);
  }

  .mobile-cta-button.btn-primary {
    margin: var(--dia-space-4) var(--dia-space-3);
    width: calc(100% - 2 * var(--dia-space-3));
    font-size: var(--dia-font-size-sm);
  }
}
