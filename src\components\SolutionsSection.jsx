import React from "react";
import "./SolutionsSection.css";
import FadeIn from "./animations/FadeIn";
import GoogleAdsCard from "./ui/GoogleAdsCard";
import YouTubeAdsCard from "./ui/YouTubeAdsCard";
import AIDiscoveryCard from "./ui/AIDiscoveryCard";
import MicrosoftAdsCard from "./ui/MicrosoftAdsCard";


const SolutionsSection = () => {

  return (
    <div className="blind-vision-section">
      <div className="dia-container-cards">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="blind-vision-header">
            <h2 className="section-subheading text-center">
              Every Campaign<br />
              Optimized for Profit.
            </h2>
            <div className="section-description text-center">
              <p>
                We're not a generalist agency. We run paid search as your CRO-led growth partner.
              </p>
            </div>
          </div>
        </FadeIn>

        {/* Google Ads Carousel */}
        <div className="solutions-carousel-section">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-200">
            <div className="solutions-carousel-header">
              <h2 className="section-heading text-center">
                Google Ads
              </h2>
              <div className="section-description text-center">
                <p>
                  High-Precision, High-Margin Campaigns
                </p>
              </div>
            </div>
            <GoogleAdsCard
              serviceName="Google Ads"
            />
          </FadeIn>
        </div>

        {/* YouTube Ads Carousel */}
        <div className="solutions-carousel-section">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-300">
            <div className="solutions-carousel-header">
              <h2 className="section-heading text-center">
                YouTube Ads
              </h2>
              <div className="section-description text-center">
                <p>
                  Build and scale YouTube without guesswork.
                </p>
              </div>
            </div>
            <YouTubeAdsCard
              serviceName="YouTube Ads"
            />
          </FadeIn>
        </div>

        {/* AI Discovery Carousel */}
        <div className="solutions-carousel-section">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-400">
            <div className="solutions-carousel-header">
              <h2 className="section-heading text-center">
                AI Discovery
              </h2>
              <div className="section-description text-center">
                <p>
                  Get ahead of the next shift in consumer search.
                </p>
              </div>
            </div>
            <AIDiscoveryCard
              serviceName="AI Discovery"
            />
          </FadeIn>
        </div>

        {/* Microsoft Ads Card */}
        <div className="solutions-carousel-section">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-500">
            <div className="solutions-carousel-header">
              <h2 className="section-heading text-center">
                Microsoft Ads
              </h2>
              <div className="section-description text-center">
                <p>
                  Quiet profit channel most brands ignore
                </p>
              </div>
            </div>
            <MicrosoftAdsCard
              serviceName="Microsoft Ads"
            />
          </FadeIn>
        </div>

      </div>
    </div>
  );
};

export default SolutionsSection;
