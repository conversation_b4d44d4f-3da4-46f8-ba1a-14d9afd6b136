/* Import FunnelVision design tokens */
@import './styles/design-tokens.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/*
 * FunnelVision Design System - TypeScript + Tailwind Implementation
 * Consolidated design tokens integrated with Tailwind CSS
 * Based on diabrowser.com's design principles
 */

:root {
  /* Font Family */
  --font-family-inter: "Inter", -apple-system, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;

  /* Base styling for light mode */
  font-family: var(--font-family-inter);
  line-height: 1.5;
  font-weight: 400;
  color: #202124;
  background-color: var(--dia-color-background);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--dia-color-background);
}

/* Typography base styles using Tailwind classes */
h1, h2, h3, h4, h5, h6 {
  font-weight: 350; /* Diabrowser light weight for headings */
  color: #202124;
  margin: 0;
}

p {
  font-weight: 400;
  color: #202124;
  margin: 0;
}

/* Link styles */
a {
  color: #1d3c2a;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #264d37;
}

/* Button base styles - will be enhanced with Tailwind */
button {
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Card base styles */
.card {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.04);
  border-radius: 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  transition: all 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

/* Container utilities */
.container {
  width: 100%;
  max-width: 1296px;
  margin: 0 auto;
  padding: 0 1.6rem;
}

/* Section spacing */
.section {
  padding: 8.0rem 0;
}

@media (max-width: 800px) {
  .section {
    padding: 4.8rem 0;
  }
}

/*
 * ANIMATION STYLES
 * Core animation classes for FadeIn, ScaleIn, etc.
 */

/* Fade-in animation styles */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Scale-in animation styles */
.scale-in {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* Animation delay utilities */
.delay-100 { transition-delay: 100ms; }
.delay-200 { transition-delay: 200ms; }
.delay-300 { transition-delay: 300ms; }
.delay-400 { transition-delay: 400ms; }
.delay-500 { transition-delay: 500ms; }

/* Fade up animation */
.fade-up {
  transform: translateY(30px);
}

.fade-up.visible {
  transform: translateY(0);
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .scale-in,
  .fade-up {
    transition: none;
    transform: none;
    opacity: 1;
  }
}
