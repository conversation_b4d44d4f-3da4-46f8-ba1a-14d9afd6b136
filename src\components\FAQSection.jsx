import React, { useState } from "react";
import "./FAQSection.css";
import FadeIn from "./animations/FadeIn";

const FAQItem = ({ question, answer, isOpen, onClick, index }) => {
  return (
    <div className={`faq-item ${isOpen ? 'open' : ''}`} onClick={onClick}>
      <div className="faq-question">
        <h3>{question}</h3>
        <div className="faq-icon button-hover">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="btn-icon"
          >
            <path
              d={isOpen ? "M19 13H5v-2h14v2z" : "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}
              fill="currentColor"
            />
          </svg>
        </div>
      </div>
      <div className={`faq-answer ${isOpen ? 'visible' : ''}`}>
        <p>{answer}</p>
      </div>
    </div>
  );
};

const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const faqData = [
    {
      question: "What is PAX™ and how is it different from ROAS?",
      answer: "PAX™ (Profit After eXpenditure) is our proprietary methodology that factors in your true costs—shipping, payment fees, returns, and COGS—to show real profit per campaign. While ROAS only shows revenue, PAX™ reveals which campaigns actually make you money. Most brands discover they're scaling unprofitable SKUs when they switch to PAX™."
    },
    {
      question: "Why do most DTC brands waste 30% of their ad spend?",
      answer: "Most brands optimize for vanity metrics like ROAS without understanding true profit margins per SKU. They scale campaigns that look good on paper but bleed margin in reality. Our profit-first approach eliminates this waste by building campaigns around your actual unit economics from day one."
    },
    {
      question: "How quickly can you improve our profit margins?",
      answer: "Most clients see immediate cost reductions within 30 days as we eliminate wasteful spend on low-margin SKUs. Significant profit improvements typically emerge within 60-90 days as our margin-based bidding strategies and cross-platform synergies take effect. The average client sees 31% higher profit margins within 6 months."
    },
    {
      question: "Do you only work with large ecommerce brands?",
      answer: "We partner with established DTC brands doing $500K+ annually who are serious about profitable growth. Our methods work best when there's sufficient data to analyze true unit economics. If you're scaling but struggling with margins, or if your 'good' ROAS isn't translating to cash flow, we can help."
    },
    {
      question: "What platforms do you manage beyond Google Ads?",
      answer: "We create integrated profit-first campaigns across Google Search, Shopping, YouTube, Microsoft Advertising (Bing), and emerging AI-search platforms. Unlike agencies that treat each platform separately, we sync your profit data across all channels to maximize cross-platform synergies and eliminate budget waste."
    },
    {
      question: "How do you ensure campaigns stay profitable as we scale?",
      answer: "Our PAX Protocol™ includes margin-segmented campaigns, daily hands-on management, and profit-based bidding that automatically adjusts as costs change. We don't 'set and forget'—we're in your accounts daily, protecting margins while scaling the SKUs that actually drive profit growth."
    }
  ];

  return (
    <div className="faq-content">
      <div className="dia-container-text">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="faq-header">
            <h2 className="section-heading text-center">Questions? Answers.</h2>
            <div className="section-description text-center">
              <p>
                Everything you need to know about our profit-first approach to search marketing.
              </p>
            </div>
          </div>
        </FadeIn>

        <div className="faq-accordion">
          {faqData.map((faq, index) => (
            <FadeIn
              key={index}
              threshold={0.1}
              rootMargin="0px"
              className={`fade-up delay-${(index + 1) * 100}`}
            >
              <FAQItem
                question={faq.question}
                answer={faq.answer}
                isOpen={openIndex === index}
                onClick={() => toggleFAQ(index)}
              />
            </FadeIn>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FAQSection;
