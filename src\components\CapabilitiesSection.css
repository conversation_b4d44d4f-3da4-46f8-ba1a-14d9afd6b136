.capabilities-section {
  padding: var(--dia-space-15) 0;
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-secondary);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.capabilities-header {
  text-align: center;
  margin-bottom: var(--dia-space-12); /* Standardized spacing */
}

/*
 * Main Title styles - using standardized section heading
 * Note: .capabilities-main-title class is kept for legacy compatibility
 * but the JSX now uses section-heading class directly
 */
.capabilities-main-title {
  /* These styles will be gradually phased out */
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  max-width: var(--width-843);
  margin: 0 auto;
}

/* Video section */
.capabilities-video {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--dia-space-14);
  padding: 0 clamp(var(--dia-space-6), 5%, var(--dia-space-15)); /* Responsive padding */
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--dia-space-11);
  margin-top: var(--dia-space-12);
}

/* FunnelVision Carousel Integration */
.natural-interaction-carousel {
  margin-top: var(--dia-space-12);
  margin-bottom: var(--dia-space-13);
  width: 100%;
  max-width: 100vw; /* Never exceed viewport width */
  margin-left: auto;
  margin-right: auto;
  padding: 0; /* Remove padding that causes overflow */
  overflow: hidden; /* Prevent overflow */
  position: relative;
  box-sizing: border-box;
}

/* Generic video container is removed - we're using scoped containers instead */
/* See capabilities-video .video-container below */

.capabilities-video .video-container {
  width: 100%;
  max-width: 1070px;
  height: 601px;
  border-radius: 26px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.04);
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.capabilities-video .video-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.capabilities-video .video-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    ellipse at center,
    rgba(66, 133, 244, 0.05) 0%,
    rgba(248, 249, 250, 0.4) 70%
  );
}

.capabilities-video .play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-primary);
  cursor: pointer;
  transition: transform 0.2s;
}

.capabilities-video .play-button:hover {
  transform: scale(1.1);
}

/* Content sections */
.capabilities-content {
  margin-bottom: var(--dia-space-15);
}

.capabilities-section-header {
  text-align: center;
  margin-bottom: var(--dia-space-14);
  max-width: var(--width-843);
  margin-left: auto;
  margin-right: auto;
}

.capabilities-section-title {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-section-heading-size);
  font-weight: var(--dia-section-heading-weight);
  line-height: var(--dia-section-heading-line-height);
  letter-spacing: var(--dia-section-heading-spacing);
  margin: 0 0 var(--dia-space-11) 0;
}

.capabilities-section-description {
  max-width: var(--width-616);
  margin: 0 auto;
}

.capabilities-section-description p {
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size);
  font-weight: var(--dia-body-text-weight);
  line-height: var(--dia-body-text-line-height);
  margin: 0;
  text-align: center;
}

/* Grid layouts */
.capabilities-grid {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: var(--item-spacing-64);
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 2px;
}

.capabilities-grid--two-column {
  max-width: 1300px;
}

/* Enhanced capability cards with consistent light mode styling */
.capability-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%; /* Use full width within container */
  max-width: 400px; /* Consistent with diabrowser card standards */
  margin-top: 0;
  outline: none;
  -webkit-tap-highlight-color: transparent;
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background);
  border-radius: var(--card-border-radius);
  padding: var(--dia-space-5);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
  transition: var(--card-transition);
}

.capability-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

.capability-card--offset {
  margin-top: 120px; /* Using margin instead of padding for offset effect */
}

.capability-image {
  margin-bottom: var(--item-spacing-36);
  width: 100%; /* Use full width within card */
  max-width: 400px; /* Consistent with card max-width */
}

/* Specific adjustment for context aware dialogue card */
.context-dialogue-card {
  margin-top: 0;
}

.capability-image-placeholder {
  width: 100%;
  height: 393px;
  border-radius: 26px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80px;
  position: relative;
}

.capability-image-placeholder::before {
  content: "";
  position: absolute;
  inset: 39px;
  border-radius: 24px;
  background: radial-gradient(
    ellipse at center,
    rgba(59, 107, 255, 0.1) 0%,
    rgba(6, 6, 6, 0.6) 70%
  );
}

.capability-content {
  text-align: left;
  width: 100%;
}

.capability-title {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-subheading-mobile);
  font-weight: var(--dia-font-weight-subheading);
  line-height: var(--dia-line-height-subheading);
  margin: 0 0 var(--dia-space-2) 0;
}

.capability-description {
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size);
  font-weight: var(--dia-body-text-weight);
  line-height: var(--dia-body-text-line-height);
  margin: 0;
}

/* Two column grid adjustments */
.capabilities-grid--two-column .capability-card {
  width: 616px;
}

.capabilities-grid--two-column .capability-image {
  width: 620px;
}

.capabilities-grid--two-column .capability-image-placeholder {
  width: 100%;
  height: 620px;
}

.capabilities-grid--two-column .capability-image-placeholder::before {
  inset: 62px;
}

/* Responsive design */
@media (max-width: 1024px) {
  .capabilities-video {
    padding: 0 var(--item-spacing-48);
  }

  .capabilities-grid {
    flex-direction: column;
    align-items: center;
    gap: var(--item-spacing-48);
  }

  .capability-card {
    width: 100%;
    max-width: 500px;
    padding-top: 0;
  }

  .capability-card--offset {
    padding-top: 0;
  }

  .capability-image {
    width: 100%;
  }

  .capability-image-placeholder {
    height: 300px;
  }

  .capabilities-grid--two-column .capability-card {
    width: 100%;
    max-width: 600px;
  }

  .capabilities-grid--two-column .capability-image {
    width: 100%;
  }

  .capabilities-grid--two-column .capability-image-placeholder {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .capabilities-section {
    padding: var(--dia-space-13) 0;
  }

  .capabilities-main-title {
    font-size: var(--dia-hero-title-size);
    line-height: var(--dia-hero-title-line-height);
  }

  .capabilities-section-title {
    font-size: var(--dia-section-heading-size);
    line-height: var(--dia-section-heading-line-height);
  }

  .capabilities-section-description p {
    font-size: var(--dia-body-text-size);
    line-height: var(--dia-body-text-line-height);
  }

  .capabilities-video .video-container {
    height: 300px;
  }

  .capability-title {
    font-size: var(--font-size-24);
    line-height: var(--line-height-32);
  }

  .capability-description {
    font-size: var(--font-size-16);
    line-height: var(--line-height-24);
  }
}

@media (max-width: 480px) {
  .capabilities-main-title {
    font-size: var(--dia-hero-secondary-size);
    line-height: var(--dia-hero-secondary-line-height);
  }

  .capabilities-section-title {
    font-size: var(--dia-section-heading-size);
    line-height: var(--dia-section-heading-line-height);
  }

  .capabilities-section-description p {
    font-size: var(--dia-body-text-size);
    line-height: var(--dia-body-text-line-height);
  }

  .video-container {
    height: 250px;
  }

  .capability-image-placeholder {
    height: 250px;
    font-size: 60px;
  }

  .capabilities-grid--two-column .capability-image-placeholder {
    height: 280px;
  }
}
