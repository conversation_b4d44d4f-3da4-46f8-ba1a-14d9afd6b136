# FunnelVision

Simple landing page project built with Re<PERSON> and Vite.

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```
2. Start the development server:
   ```bash
   npm run dev
   ```
3. Build for production:
   ```bash
   npm run build
   ```

Requires Node.js and npm installed on your machine.

## Tailwind, shadcn/ui and TypeScript

This project doesn't ship with Tailwind CSS or TypeScript configured out of the box. To enable shadcn UI components and Tailwind utilities, run:

```bash
npx shadcn-ui@latest init
npx tailwindcss init -p
```

For TypeScript support you can add a `tsconfig.json` via:

```bash
npx tsc --init
```

Then rename files to `.tsx` where needed. Vite will compile them automatically.

## Project Structure

All React components live inside `src/components`. Reusable UI pieces are placed in `src/components/ui` following the shadcn UI convention. If the `ui` folder is missing, create it so your custom components can share styles and be easily imported across the app.

## Additional Dependencies

Some UI components use icons and animations provided by external packages. Install them with:

```bash
npm install lucide-react framer-motion @radix-ui/react-slot class-variance-authority
```