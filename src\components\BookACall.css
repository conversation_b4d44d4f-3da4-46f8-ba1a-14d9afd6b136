/* Book A Call Page Styles */

.book-a-call-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--dia-background);
}

.book-a-call-main {
  flex: 1;
  padding-top: calc(var(--dia-space-13) + var(--dia-space-15)); /* Header height + additional space */
  padding-bottom: var(--dia-space-15);
  padding-left: 0;
  padding-right: 0;
}

/* Header Section */
.book-a-call-header {
  text-align: center;
  margin-bottom: var(--dia-space-15); /* 100px */
}

.book-a-call-title {
  /* Use section heading tokens for consistency */
  font-family: var(--font-family-inter);
  font-size: var(--dia-section-heading-size);
  font-weight: var(--dia-section-heading-weight);
  line-height: var(--dia-section-heading-line-height);
  letter-spacing: var(--dia-section-heading-spacing);
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-9) 0; /* 24px bottom margin */
}

.book-a-call-description {
  /* Use section description tokens for consistency */
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size);
  font-weight: var(--dia-body-text-weight);
  line-height: var(--dia-body-text-line-height);
  color: var(--dia-color-text-secondary);
  margin: 0;
}

/* Card Container and Card */
.book-a-call-card-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.book-a-call-card {
  background: var(--dia-white);
  border: 1px solid var(--dia-border-light);
  border-radius: var(--dia-radius-standard);
  padding: var(--dia-space-11); /* 40px */
  box-shadow: var(--dia-shadow-sm);
  width: 100%;
  max-width: 900px;
  overflow: hidden;
}

/* Calendly Widget Responsive */
.calendly-inline-widget {
  width: 100% !important;
  border-radius: var(--dia-radius-sm);
  overflow: hidden;
  /* Desktop: Horizontal layout with adequate height */
  height: 800px !important;
}

/* Footer Styles - Match main footer */
.book-a-call-footer {
  background-color: var(--dia-color-text-primary); /* Dia black background like main footer */
  color: var(--dia-color-background); /* White text like main footer */
  padding: var(--dia-space-9) 0; /* 24px top/bottom */
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--dia-space-6); /* 16px */
}

.footer-left {
  display: flex;
  align-items: center;
}

.footer-copyright {
  font-size: var(--dia-font-size-sm); /* 14px */
  color: var(--dia-color-background); /* White text like main footer */
  font-family: var(--font-family-inter);
  font-weight: var(--dia-font-weight-normal);
}

.footer-right {
  display: flex;
  gap: var(--dia-space-9); /* 24px */
  align-items: center;
}

.footer-link {
  font-size: var(--dia-font-size-sm); /* 14px */
  color: rgba(255, 255, 255, 0.8); /* Slightly transparent white like main footer */
  text-decoration: none;
  font-family: var(--font-family-inter);
  font-weight: var(--dia-font-weight-normal);
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--dia-color-background); /* Full white on hover */
  text-decoration: underline;
}

/* Responsive Design */

/* Tablet breakpoint - Vertical layout */
@media (max-width: 1024px) {
  .book-a-call-main {
    padding-top: calc(var(--dia-space-13) + var(--dia-space-13)); /* Header height + tablet space */
    padding-bottom: var(--dia-space-13);
  }

  .calendly-inline-widget {
    height: 700px !important; /* Taller for vertical layout on tablet */
  }
}

/* Mobile breakpoint - Vertical layout */
@media (max-width: 768px) {
  .book-a-call-main {
    padding-top: calc(var(--dia-space-13) + var(--dia-space-11)); /* Header height + mobile space */
    padding-bottom: var(--dia-space-11);
    padding-left: 0;
    padding-right: 0;
  }

  .book-a-call-title {
    font-size: var(--dia-font-size-3xl); /* 26px on mobile */
  }

  .book-a-call-description {
    font-size: var(--dia-font-size-base); /* 16px on mobile */
  }

  .book-a-call-card {
    padding: var(--dia-space-9); /* 24px on mobile */
    margin: 0 var(--dia-space-4); /* 12px side margins */
  }

  .calendly-inline-widget {
    height: 650px !important; /* Vertical layout height for mobile */
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: var(--dia-space-4); /* 12px */
  }

  .footer-right {
    gap: var(--dia-space-6); /* 16px on mobile */
  }
}

/* Small mobile breakpoint */
@media (max-width: 480px) {
  .book-a-call-card {
    margin: 0 var(--dia-space-2); /* 8px side margins on small mobile */
    padding: var(--dia-space-6); /* 16px padding on small mobile */
  }

  .calendly-inline-widget {
    height: 600px !important; /* Compact vertical layout for small screens */
  }
}
