# Tailwind Visual Parity Tasks

This checklist breaks down the work required to achieve pixel-perfect parity between the original JavaScript build and the new **TypeScript + Tailwind** build in `funnelvision-site/`.

## 1. Visual Snapshot Testing
- [ ] Capture baseline screenshots from `src/` at all six breakpoints (xs, sm, md, lg, xl, xxl).
- [ ] Capture matching screenshots from `funnelvision-site/src/`.
- [ ] Use a diff tool to highlight layout or style differences.

## 2. Token Audit
- [ ] Compare `funnelvision-site/src/styles/design-tokens.css` with `src/styles/funnelvision.css`.
- [ ] Add any missing typography, color, or spacing tokens to the Tailwind config.
- [ ] Ensure Tailwind utilities reference these tokens via CSS variables.

## 3. Component Review
For each component listed in the Visual Diff Table, verify parity and note discrepancies.
- [ ] Header – sticky behavior, mobile menu transitions, hover styles.
- [ ] Hero – typography sizes, CTA layout, rating display.
- [ ] LargeTextSection – text reveal animation and spacing.
- [ ] ProblemSection – card grid columns and gutters.
- [ ] ROASvsPAXSection – chart sizing and captions.
- [ ] SolutionsSection – icon sizing and padding.
- [ ] ProfitApproachSection – alignment of text and images.
- [ ] Footer – social icons and copyright notice.

## 4. Responsive Verification
- [ ] Validate container widths and grid alignment at xs, sm, md, lg, xl and xxl breakpoints.
- [ ] Check that section padding follows `--section-padding-*` tokens.
- [ ] Confirm typography jumps at 800px and 1000px.

## 5. Performance Check
- [ ] Remove unused Tailwind classes and custom CSS.
- [ ] Verify bundle size after tree shaking.
- [ ] Ensure animations remain smooth.

## 6. Sign-off Matrix
Mark each component and breakpoint once it matches the original build.

| Component | xs | sm | md | lg | xl | xxl |
|-----------|----|----|----|----|----|----|
| Header | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |
| Hero | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |
| LargeTextSection | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |
| ProblemSection | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |
| ROASvsPAXSection | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |
| SolutionsSection | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |
| ProfitApproachSection | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |
| Footer | ☐ | ☐ | ☐ | ☐ | ☐ | ☐ |

Update this table as issues are resolved.
