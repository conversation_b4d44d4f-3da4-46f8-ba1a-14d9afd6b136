import React, {
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";
import { AnimatePresence, motion } from "framer-motion";
import type { Logo } from "./logo-icons";

// Shuffles array in-place using <PERSON><PERSON><PERSON> algorithm
const shuffleArray = <T,>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Distributes logos across columns
const distributeLogos = (allLogos: Logo[], columnCount: number): Logo[][] => {
  const shuffled = shuffleArray(allLogos);
  const columns: Logo[][] = Array.from({ length: columnCount }, () => []);

  shuffled.forEach((logo, index) => {
    columns[index % columnCount].push(logo);
  });

  const maxLength = Math.max(...columns.map((col) => col.length));
  columns.forEach((col) => {
    while (col.length < maxLength) {
      col.push(shuffled[Math.floor(Math.random() * shuffled.length)]);
    }
  });

  return columns;
};

interface LogoColumnProps {
  logos: Logo[];
  index: number;
  currentTime: number;
}

const LogoColumn = React.memo<LogoColumnProps>(
  ({ logos, index, currentTime }) => {
    const cycleInterval = 2000;
    const columnDelay = index * 200;
    const adjustedTime = (currentTime + columnDelay) % (cycleInterval * logos.length);
    const currentIndex = Math.floor(adjustedTime / cycleInterval);
    const CurrentLogo = useMemo(() => logos[currentIndex].img, [logos, currentIndex]);

    return (
      <motion.div
        className="flex flex-col items-center justify-center h-16 w-16"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full flex items-center justify-center"
          >
            <CurrentLogo 
              className="max-w-full max-h-full object-contain filter grayscale opacity-60 hover:opacity-100 hover:grayscale-0 transition-all duration-300"
            />
          </motion.div>
        </AnimatePresence>
      </motion.div>
    );
  }
);

LogoColumn.displayName = "LogoColumn";

interface LogoCarouselProps {
  columnCount: number;
  logos: Logo[];
  className?: string;
}

export const LogoCarousel: React.FC<LogoCarouselProps> = ({
  columnCount,
  logos,
  className = ""
}) => {
  const [currentTime, setCurrentTime] = useState(0);

  const columns = useMemo(
    () => distributeLogos(logos, columnCount),
    [logos, columnCount]
  );

  const updateTime = useCallback(() => {
    setCurrentTime((prevTime) => prevTime + 100);
  }, []);

  useEffect(() => {
    const interval = setInterval(updateTime, 100);
    return () => clearInterval(interval);
  }, [updateTime]);

  return (
    <div className={`w-full ${className}`}>
      <div 
        className="grid gap-8 justify-items-center items-center mx-auto max-w-4xl"
        style={{
          gridTemplateColumns: `repeat(${columnCount}, 1fr)`,
        }}
      >
        {columns.map((columnLogos, index) => (
          <LogoColumn
            key={index}
            logos={columnLogos}
            index={index}
            currentTime={currentTime}
          />
        ))}
      </div>
    </div>
  );
};
