import React, { useRef, useState, useContext, createContext, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useOutsideClick } from "../../hooks/useOutsideClick";
import { cn } from "../../lib/utils";

// Types
interface CardData {
  category: string;
  title: string;
  src: string;
  content?: React.ReactNode;
}

interface CarouselContextType {
  activeCardIndex: number;
  setActiveCardIndex: (index: number) => void;
  scrollToCard: (index: number) => void;
}

interface CardProps {
  card: CardData;
  index: number;
}

interface CarouselProps {
  items: React.ReactNode[];
  className?: string;
}

// Context for carousel state management
const CarouselContext = createContext<CarouselContextType>({
  activeCardIndex: 0,
  setActiveCardIndex: () => {},
  scrollToCard: () => {},
});

// Hook to use carousel context
const useCarousel = (): CarouselContextType => {
  const context = useContext(CarouselContext);
  if (!context) {
    throw new Error("useCarousel must be used within a CarouselContextProvider");
  }
  return context;
};

/**
 * Card component for the FunnelVision Cards Carousel
 */
export const Card = React.memo<CardProps>(({ card, index }) => {
  const { category, title, src, content } = card;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRevealed, setIsRevealed] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const { activeCardIndex, setActiveCardIndex, scrollToCard } = useCarousel();
  
  // Close modal when clicked outside
  useOutsideClick(modalRef, () => {
    if (isModalOpen) setIsModalOpen(false);
  });

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (isModalOpen && event.key === "Escape") {
        setIsModalOpen(false);
      }
    };

    if (isModalOpen) {
      document.addEventListener("keydown", handleEscapeKey);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
      document.body.style.overflow = "unset";
    };
  }, [isModalOpen]);

  const handleCardClick = useCallback(() => {
    if (activeCardIndex !== index) {
      setActiveCardIndex(index);
      scrollToCard(index);
    } else {
      setIsModalOpen(true);
    }
  }, [activeCardIndex, index, setActiveCardIndex, scrollToCard]);

  const handleToggleReveal = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsRevealed(!isRevealed);
  }, [isRevealed]);

  const isActive = activeCardIndex === index;

  return (
    <>
      {/* Card */}
      <motion.div
        ref={cardRef}
        className={cn(
          "card-base relative cursor-pointer overflow-hidden",
          "w-80 h-96 flex-shrink-0",
          isActive ? "ring-2 ring-primary" : ""
        )}
        onClick={handleCardClick}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        layout
      >
        {/* Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{ backgroundImage: `url(${src})` }}
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-40" />
        
        {/* Content */}
        <div className="relative z-10 p-6 h-full flex flex-col justify-between text-white">
          {/* Category */}
          <div className="text-sm font-medium opacity-80">
            {category}
          </div>
          
          {/* Title */}
          <div className="text-xl font-semibold">
            {title}
          </div>
          
          {/* Toggle Button */}
          <button
            onClick={handleToggleReveal}
            className="self-end w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-colors"
          >
            <motion.div
              animate={{ rotate: isRevealed ? 45 : 0 }}
              transition={{ duration: 0.2 }}
            >
              +
            </motion.div>
          </button>
        </div>
        
        {/* Reveal Content */}
        <AnimatePresence>
          {isRevealed && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="absolute inset-0 bg-white p-6 flex flex-col justify-center z-20"
            >
              <button
                onClick={handleToggleReveal}
                className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                ×
              </button>
              <div className="text-text-primary">
                {content}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Modal */}
      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              ref={modalRef}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-standard max-w-2xl w-full max-h-[80vh] overflow-auto"
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <div className="text-sm text-text-muted mb-1">{category}</div>
                    <h3 className="text-2xl font-semibold text-text-primary">{title}</h3>
                  </div>
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
                  >
                    ×
                  </button>
                </div>
                <div className="mb-4">
                  <img src={src} alt={title} className="w-full h-48 object-cover rounded-standard" />
                </div>
                <div className="text-text-secondary">
                  {content}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
});

Card.displayName = "Card";

/**
 * Carousel component that manages card state and navigation
 */
export const Carousel: React.FC<CarouselProps> = ({ items, className }) => {
  const [activeCardIndex, setActiveCardIndex] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollToCard = useCallback((index: number) => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const cardWidth = 320; // w-80 = 320px
      const gap = 32; // gap-8 = 32px
      const scrollPosition = index * (cardWidth + gap);
      
      container.scrollTo({
        left: scrollPosition,
        behavior: 'smooth'
      });
    }
  }, []);

  const contextValue: CarouselContextType = {
    activeCardIndex,
    setActiveCardIndex,
    scrollToCard,
  };

  return (
    <CarouselContext.Provider value={contextValue}>
      <div className={cn("w-full", className)}>
        {/* Cards Container */}
        <div
          ref={scrollContainerRef}
          className="flex gap-8 overflow-x-auto scrollbar-hide pb-4"
          style={{ scrollSnapType: 'x mandatory' }}
        >
          {items}
        </div>
        
        {/* Navigation Dots */}
        <div className="flex justify-center mt-6 gap-2">
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                setActiveCardIndex(index);
                scrollToCard(index);
              }}
              className={cn(
                "w-2 h-2 rounded-full transition-colors",
                activeCardIndex === index ? "bg-primary" : "bg-gray-300"
              )}
            />
          ))}
        </div>
      </div>
    </CarouselContext.Provider>
  );
};
