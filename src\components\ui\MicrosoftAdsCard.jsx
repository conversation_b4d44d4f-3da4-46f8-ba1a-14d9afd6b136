import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import MicrosoftAdsModal from './MicrosoftAdsModal';
import './MicrosoftAdsCard.css';

/**
 * MicrosoftAdsCard component - Hero image card that opens modal overlay
 * Features:
 * - Single hero image display
 * - Click-to-open modal functionality
 * - Hover states and animations
 * - Responsive behavior matching carousel dimensions
 * - Uses diabrowser design tokens for consistency
 */
const MicrosoftAdsCard = ({
  serviceName = "Microsoft Ads"
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const imageSrc = '/images/services/microsoft-bing-ads.webp';

  // Preload image
  useEffect(() => {
    const img = new Image();
    img.onload = () => setImageLoaded(true);
    img.onerror = () => setImageLoaded(true); // Still show card even if image fails
    img.src = imageSrc;
  }, [imageSrc]);

  const handleCardClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // Handle keyboard interaction
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleCardClick();
    }
  };

  if (!imageLoaded) {
    return (
      <div className="microsoft-ads-card-container">
        <div className="microsoft-ads-card-loading">
          <div className="microsoft-ads-card-skeleton" />
        </div>
      </div>
    );
  }

  return (
    <>
      <motion.div
        className="microsoft-ads-card-container"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.1 }}
      >
        <div
          className="microsoft-ads-card"
          onClick={handleCardClick}
          onKeyDown={handleKeyDown}
          tabIndex={0}
          role="button"
          aria-label={`Open ${serviceName} details`}
        >
          <div className="microsoft-ads-card-image-container">
            <img
              src={imageSrc}
              alt={`${serviceName} showcase`}
              className="microsoft-ads-card-image"
              loading="lazy"
            />
            <div className="microsoft-ads-card-cta">
              <span className="microsoft-ads-card-cta-text">View Details</span>
              <svg 
                className="microsoft-ads-card-cta-icon" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2"
              >
                <path d="M7 17L17 7" />
                <path d="M7 7h10v10" />
              </svg>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Modal */}
      <MicrosoftAdsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        serviceName={serviceName}
      />
    </>
  );
};

export default MicrosoftAdsCard;
