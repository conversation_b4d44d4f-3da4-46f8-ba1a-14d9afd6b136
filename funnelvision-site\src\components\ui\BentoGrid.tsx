import React from "react";

// Utility function for conditional class names
function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(" ");
}

interface BentoGridProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * BentoGrid component - Responsive grid layout for bento cards
 */
export const BentoGrid: React.FC<BentoGridProps> = ({
  children,
  className,
}) => {
  return (
    <div
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr",
        className,
      )}
    >
      {children}
    </div>
  );
};

interface BentoCardProps {
  name: string;
  className?: string;
  Icon: React.ComponentType;
  description: string;
  href?: string;
  cta?: string;
}

/**
 * BentoCard component - Card with background, icon, and hover effect
 */
export const BentoCard: React.FC<BentoCardProps> = ({
  name,
  className,
  Icon,
  description,
  href,
  cta = "Learn more",
}) => (
  <div
    key={name}
    className={cn(
      "group relative bg-card-background border border-card-border rounded-standard p-8 transition-all duration-200 hover:transform hover:scale-[1.02] hover:shadow-card-hover",
      className,
    )}
  >
    <div className="flex flex-col h-full">
      <div className="flex-1">
        <div className="w-12 h-12 mb-6 text-primary">
          <Icon />
        </div>
        <h3 className="text-xl font-semibold text-text-primary mb-4 leading-tight">
          {name}
        </h3>
        <p className="body-text text-text-secondary leading-relaxed">
          {description}
        </p>
      </div>
      {href && (
        <div className="mt-6 pt-4">
          <a 
            href={href} 
            className="inline-flex items-center gap-2 text-primary hover:text-primary-hover transition-colors font-medium"
          >
            {cta}
            <svg
              viewBox="0 0 16 16"
              fill="currentColor"
              className="w-4 h-4"
            >
              <path d="M8 2L7.3 2.7L11.6 7H2.5V8H11.6L7.3 12.3L8 13L13.5 7.5L8 2Z" />
            </svg>
          </a>
        </div>
      )}
    </div>
  </div>
);

interface NumberedBentoCardProps {
  title: string;
  className?: string;
  number: number;
  description: string;
}

/**
 * NumberedBentoCard component - Card with number instead of icon
 */
export const NumberedBentoCard: React.FC<NumberedBentoCardProps> = ({
  title,
  className,
  number,
  description,
}) => (
  <div
    key={title}
    className={cn(
      "group relative bg-card-background border border-card-border rounded-standard p-8 transition-all duration-200 hover:transform hover:scale-[1.02] hover:shadow-card-hover",
      className,
    )}
  >
    <div className="flex flex-col h-full">
      <div className="flex-1">
        <div className="w-12 h-12 mb-6 bg-primary text-white rounded-standard flex items-center justify-center font-bold text-xl">
          {number}
        </div>
        <h3 className="text-xl font-semibold text-text-primary mb-4 leading-tight">
          {title}
        </h3>
        <p className="body-text text-text-secondary leading-relaxed">
          {description}
        </p>
      </div>
    </div>
  </div>
);
