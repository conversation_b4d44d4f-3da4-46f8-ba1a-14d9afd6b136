/* Import the consolidated FunnelVision design system */
@import './styles/funnelvision.css';

:root {
  /*
   * APPLICATION-SPECIFIC VARIABLES
   * Variables specific to this application that are NOT part of the diabrowser design system
   */

  /* Brand Colors - FunnelVision specific */
  --color-primary: #1d3c2a; /* Primary brand color - dark green */
  --color-primary-hover: #264d37; /* Hover state for primary */
  --color-primary-light: rgba(29, 60, 42, 0.05); /* Light version for backgrounds/hovers */

  /* UI State Colors */
  --color-error: #FF5252; /* Error/negative state */
  --color-success: #4CAF50; /* Success state */
  --color-warning: #FFC107; /* Warning state */

  /* Legacy Color Aliases - for backward compatibility */
  --color-grey-2: var(--dia-color-text-primary);
  --color-grey-7: var(--dia-color-text-primary);
  --color-grey-25: var(--dia-color-text-secondary);
  --color-grey-63: var(--dia-color-text-muted);
  --color-grey-85: var(--dia-color-grey);
  --color-grey-95: var(--dia-color-background);
  --color-white-solid: var(--dia-color-white);
  --color-background-primary: var(--dia-color-card-background); /* Cards now use pure white */
  --color-background-secondary: var(--dia-color-card-background); /* Cards now use pure white */

  /* Application-specific colors */
  --color-white-15: rgba(255, 255, 255, 0.15);
  --color-grey-958: rgba(241, 243, 244, 0.08);
  --color-border-light: rgba(0, 0, 0, 0.04);

  /* Brand Gradients */
  --gradient-primary: linear-gradient(135deg, #1d3c2a 0%, #264d37 100%);

  /* Enhanced Application Shadows - Two-shadow technique for light mode */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(255, 255, 255, 0.3);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(255, 255, 255, 0.4);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(255, 255, 255, 0.5);

  /* Standardized Application Border Radius - 16px universal */
  --radius-sm: 16px;   /* Standardized to 16px */
  --radius-md: 16px;   /* Standardized to 16px */
  --radius-lg: 16px;   /* Standardized to 16px */
  --radius-standard: 16px; /* Universal standard radius */
  --radius-full: 1000px; /* For pills and circles only */

  /* Legacy Spacing Aliases - for backward compatibility */
  --item-spacing-4: var(--dia-space-1);
  --item-spacing-7: var(--dia-space-1);
  --item-spacing-8: var(--dia-space-1);
  --item-spacing-10: var(--dia-space-3);
  --item-spacing-12: var(--dia-space-4);
  --item-spacing-14: var(--dia-space-5);
  --item-spacing-16: var(--dia-space-2);
  --item-spacing-18: var(--dia-space-7);
  --item-spacing-20: var(--dia-space-8);
  --item-spacing-24: var(--dia-space-9);
  --item-spacing-32: var(--dia-space-4);
  --item-spacing-36: var(--dia-space-11);
  --item-spacing-40: var(--dia-space-5);
  --item-spacing-48: var(--dia-space-6);
  --item-spacing-64: var(--dia-space-8);
  --item-spacing-80: var(--dia-space-10);
  --item-spacing-96: var(--dia-space-14);
  --item-spacing-120: var(--dia-space-15);

  /* Legacy Line Heights - for backward compatibility */
  --line-height-16: 16px;
  --line-height-20: 20px;
  --line-height-24: 24px;
  --line-height-28: 28px;
  --line-height-32: 32px;
  --line-height-36: 36px;
  --line-height-48: 48px;
  --line-height-56: 56px;
  --line-height-72: 72px;
  --line-height-96: 96px;
  --line-height-128: 128px;

  /* Legacy Letter Spacing - for backward compatibility */
  --letter-spacing-tight: -0.02em;
  --letter-spacing-normal: -0.01em;
  --letter-spacing-wide: 0.01em;

  /* Application-specific Widths */
  --width-616: 616px;
  --width-843: 843px;
  --width-1070: 1070px;
  --width-1296: 1296px;
  --width-1920: 1920px;

  /* Application Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;



  /* Legacy Font Family - for backward compatibility */
  --font-family-sf-pro: SF Pro Display, SF Pro Icons, Helvetica Neue, Helvetica, Arial, sans-serif;
}

/* ==========================================
   APPLICATION BASE STYLES
   ========================================== */

* {
  box-sizing: border-box;
}

html {
  /* Set base font size for rem calculations - diabrowser expects 10px base */
  font-size: 62.5%; /* 62.5% of 16px = 10px, so 1rem = 10px */
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: var(--font-family-inter);
  font-size: 1.6rem; /* 16px - reset to normal reading size */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-primary);
}

*, *::before, *::after {
  box-sizing: inherit;
  letter-spacing: -0.08px; /* Standard letter-spacing */
}

#root {
  min-height: 100vh;
  max-width: 100vw; /* Ensure root never exceeds viewport */
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Typography normalization across elements */
p, span, strong, em, b, i, div, a {
  font-weight: inherit;
  font-size: inherit;
  line-height: inherit;
  font-family: inherit;
  letter-spacing: inherit;
  color: inherit;
}

/* Preserve strong tag behavior with explicit class */
strong:not([class]), b:not([class]) {
  font-weight: 600;
}

/* Reset unwanted browser defaults */
em:not([class]), i:not([class]) {
  font-style: normal;
}

/* Base HTML element styles - using diabrowser tokens */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--dia-section-heading-weight);
  letter-spacing: var(--dia-section-heading-spacing);
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
}

h1 {
  font-size: var(--dia-hero-title-size);
  line-height: var(--dia-hero-title-line-height);
  font-weight: var(--dia-hero-title-weight);
  letter-spacing: var(--dia-hero-title-spacing);
}

h2 {
  font-size: var(--dia-section-heading-size);
  line-height: var(--dia-section-heading-line-height);
}

h3 {
  font-size: var(--dia-font-size-subheading-mobile);
  line-height: var(--dia-line-height-subheading);
}

h4 {
  font-size: var(--dia-font-size-xl);
  line-height: var(--dia-body-text-line-height);
}

h5 {
  font-size: var(--dia-body-text-size);
  line-height: var(--dia-body-text-line-height);
}

h6 {
  font-size: var(--dia-font-size-lg);
  line-height: var(--dia-body-text-line-height);
}

p {
  margin: 0;
  letter-spacing: var(--dia-body-text-spacing);
  font-weight: var(--dia-body-text-weight);
  font-size: var(--dia-body-text-size);
  line-height: var(--dia-body-text-line-height);
  color: var(--dia-color-text-secondary);
}

button {
  border: none;
  cursor: pointer;
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

a {
  color: inherit;
  text-decoration: none;
}

/* ==========================================
   APPLICATION-SPECIFIC UTILITY CLASSES
   ========================================== */



/* Section styles - standardized to diabrowser tokens */
.section {
  padding: var(--dia-space-14) 0;
  width: 100%;
  overflow: hidden;
}

/* Responsive section padding */
@media (max-width: 800px) {
  .section {
    padding: var(--dia-space-12) 0;
  }
}

@media (max-width: 480px) {
  .section {
    padding: var(--dia-space-10) 0;
  }
}

.section-light {
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-secondary);
}

.section-dark {
  background-color: var(--dia-color-text-primary);
  color: var(--dia-color-background);
}

.section-gray {
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-secondary);
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/*
 * UPDATED BUTTON STYLES - Using Unified CTA System
 * Consistent with hero section design
 */

/* Base button class - extends CTA system */
.btn {
  /* Inherit from CTA base */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--cta-gap);
  padding: var(--cta-padding-mobile);
  border-radius: var(--cta-border-radius);
  height: var(--cta-height);
  min-width: var(--cta-min-width-mobile);
  cursor: pointer;
  position: relative;
  text-align: center;
  flex-shrink: 0;
  text-decoration: none;
  border: none;
  font-family: var(--font-family-inter);
  font-size: var(--dia-btn-cta-size);
  font-weight: var(--dia-btn-cta-weight);
  line-height: var(--dia-btn-cta-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  transition: var(--cta-transition);
  white-space: nowrap;
  outline: none;
}

/* Primary button - matches hero primary CTA */
.btn-primary {
  background: var(--cta-primary-background);
  color: var(--cta-primary-text-color);
  box-shadow: var(--cta-primary-shadow);
}

.btn-primary:hover {
  background: var(--cta-primary-background-hover);
  box-shadow: var(--cta-primary-shadow-hover);
  transform: var(--cta-hover-transform);
}

.btn-primary:active {
  transform: translateY(1px);
}

/* Secondary button - matches hero secondary CTA */
.btn-secondary {
  background: var(--cta-secondary-background);
  color: var(--cta-secondary-text-color);
  border: var(--cta-secondary-border);
  box-shadow: var(--cta-secondary-shadow);
}

.btn-secondary .btn-icon {
  color: var(--cta-secondary-text-color);
}

.btn-secondary:hover {
  background: var(--cta-secondary-background-hover);
  box-shadow: var(--cta-secondary-shadow-hover);
  transform: var(--cta-hover-transform);
}

/* Universal CTA arrow animations for all buttons */
.btn:hover .cta-arrow {
  transform: var(--cta-arrow-hover-transform);
}

.btn:hover .cta-text {
  transform: var(--cta-text-hover-transform);
}

/* Button responsive sizing - matches CTA system */
@media (min-width: 600px) {
  .btn {
    min-width: var(--cta-min-width-tablet);
    padding: var(--cta-padding-tablet);
  }
}

@media (min-width: 800px) {
  .btn {
    min-width: var(--cta-min-width-desktop);
    padding: var(--cta-padding-desktop);
  }
}

@media (min-width: 1000px) {
  .btn {
    min-width: var(--cta-min-width-large);
    padding: var(--cta-padding-large);
  }
}

.btn-sm {
  padding: var(--dia-space-2) var(--dia-space-6);
  font-size: var(--dia-btn-secondary-size);
  line-height: var(--dia-btn-secondary-line-height);
}

.btn-lg {
  padding: var(--dia-space-5) var(--dia-space-9);
  font-size: var(--dia-btn-primary-size); /* Use primary size for consistency */
  line-height: var(--dia-btn-primary-line-height);
}

/* Layout utilities */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

.gap-8 {
  gap: var(--item-spacing-8);
}
.gap-16 {
  gap: var(--item-spacing-16);
}
.gap-24 {
  gap: var(--item-spacing-24);
}
.gap-36 {
  gap: var(--item-spacing-36);
}
.gap-48 {
  gap: var(--item-spacing-48);
}
.gap-64 {
  gap: var(--item-spacing-64);
}
.gap-80 {
  gap: var(--item-spacing-80);
}
.gap-120 {
  gap: var(--item-spacing-120);
}


