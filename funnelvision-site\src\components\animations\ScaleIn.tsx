import React, { useRef, useEffect, useState } from 'react';

interface ScaleInProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * ScaleIn component that animates its children with a progressive growing scale effect 
 * as they enter and move through the viewport.
 */
const ScaleIn: React.FC<ScaleInProps> = ({ 
  children, 
  className = '' 
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(0);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // Mark as in view once any part of the element is visible
        setIsInView(entry.isIntersecting);
      },
      { threshold: 0 }
    );

    const currentElement = cardRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (!cardRef.current || !isInView) return;

      const rect = cardRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      
      // Calculate how far element is through viewport (0 at top entry, 1 at middle)
      const visibleAmount = Math.min(
        (viewportHeight - rect.top) / (viewportHeight / 2),
        1
      );
      
      // Scale from 0.85 (start) to 1 (fully visible)
      const newScale = 0.85 + (visibleAmount * 0.15);
      setScale(newScale);
    };

    // Initial calculation
    if (isInView) {
      handleScroll();
    }

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isInView]);

  const classNames = `scale-in-progressive ${className}`.trim();
  
  // Only apply transform when element is in view
  const style: React.CSSProperties = isInView ? {
    transform: `scale(${scale})`,
    opacity: Math.min(scale * 2 - 1.2, 1) // Fade in slightly after scaling starts
  } : {
    transform: 'scale(0.85)',
    opacity: 0
  };

  return (
    <div ref={cardRef} className={classNames} style={style}>
      {children}
    </div>
  );
};

export default ScaleIn;
