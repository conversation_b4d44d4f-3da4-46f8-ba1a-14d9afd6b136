/* Simple Footer - Clean, No-Scroll Implementation */
.simple-footer {
  background-color: var(--dia-color-text-primary); /* Dia black background */
  color: var(--dia-color-background); /* White text on black background */
  padding: var(--dia-space-12) 0 var(--dia-space-6) 0;
  margin-top: var(--dia-space-14);
  width: 100%;
  max-width: 100vw; /* Prevent exceeding viewport width */
  box-sizing: border-box;
  overflow-x: hidden; /* Prevent horizontal overflow */
}

/* Footer uses dia-container-cards for consistent spacing */

/* Main footer content */
.footer-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--dia-space-8);
  gap: var(--dia-space-8);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Brand section */
.footer-brand {
  flex: 1;
  max-width: 480px;
  min-width: 0; /* Allow shrinking */
  box-sizing: border-box;
}

.footer-logo {
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-xl);
  font-weight: var(--dia-font-weight-semibold);
  line-height: var(--dia-line-height-tight);
  color: var(--dia-color-background);
  margin: 0 0 var(--dia-space-2) 0;
}

.footer-tagline {
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-base);
  font-weight: var(--dia-font-weight-normal);
  line-height: var(--dia-line-height-relaxed);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Navigation links */
.footer-nav {
  display: flex;
  align-items: flex-start;
  gap: var(--dia-space-6);
  flex-wrap: wrap;
  min-width: 0; /* Allow shrinking */
  box-sizing: border-box;
}

.footer-link {
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-sm);
  font-weight: var(--dia-font-weight-normal);
  line-height: var(--dia-line-height-normal);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color var(--transition-base);
}

.footer-link:hover {
  color: var(--dia-color-background);
}

/* Bottom section */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--dia-space-6);
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Social links */
.footer-social {
  display: flex;
  align-items: center;
  gap: var(--dia-space-4);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: rgba(255, 255, 255, 0.8);
  transition: color var(--transition-base), transform var(--transition-base);
}

.social-link:hover {
  color: var(--dia-color-background);
  transform: translateY(-1px);
}

/* Copyright */
.footer-copyright {
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-sm);
  font-weight: var(--dia-font-weight-normal);
  color: rgba(255, 255, 255, 0.7);
}

/* Responsive design - Single breakpoint approach */
@media (max-width: 768px) {
  .footer-main {
    flex-direction: column;
    gap: var(--dia-space-6);
    text-align: center;
  }

  .footer-brand {
    max-width: 100%;
  }

  .footer-nav {
    justify-content: center;
    gap: var(--dia-space-4);
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--dia-space-4);
    text-align: center;
  }

  .footer-social {
    order: -1; /* Show social links first on mobile */
  }
}
