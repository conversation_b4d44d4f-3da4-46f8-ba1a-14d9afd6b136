.app {
  min-height: 100vh;
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-primary);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
}

.main-content {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Hide the enhancing-gemini section */
section#enhancing-gemini {
  display: none;
}

.large-text-section-wrapper {
  position: relative;
  margin-top: calc(var(--dia-space-14) * 1.75); /* Increased by 75% */
  margin-bottom: calc(var(--dia-space-14) * 1.8); /* Increased by 80% */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Section wrapper styles */
#devices,
#capabilities {
  background-color: var(--dia-color-background) !important;
  width: 100%;
}

/* Responsibility section styles */
.responsibility-section {
  text-align: center;
  padding: var(--dia-space-14) 0;
}

.responsibility-title {
  /* Using section-heading class styles */
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-section-heading-size);
  font-weight: var(--dia-section-heading-weight);
  line-height: var(--dia-section-heading-line-height);
  margin: 0 0 var(--dia-space-8) 0;
}

.responsibility-description {
  /* Using section-description class styles */
  color: var(--color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-section-description-mobile);
  font-weight: var(--font-weight-section-description);
  line-height: var(--line-height-section-description);
  margin: 0;
  text-align: center;
  max-width: var(--width-616);
  margin-left: auto;
  margin-right: auto;
  letter-spacing: var(--letter-spacing-normal);
}

/* Contributors section */
.contributors-section {
  padding: var(--dia-space-14) 0;
  border-top: 1px solid var(--color-border-light);
  background-color: var(--dia-color-card-background); /* Pure white for contributors section */
}

.contributors-content {
  max-width: var(--width-616);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-2);
}

.contributors-intro {
  color: var(--color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-loose);
  letter-spacing: var(--letter-spacing-normal);
  margin: 0;
}

.contributors-list {
  color: var(--color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-loose);
  letter-spacing: var(--letter-spacing-normal);
  margin: 0;
  text-align: justify;
}

.contributors-acknowledgment {
  color: var(--color-text-muted);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-loose);
  letter-spacing: var(--letter-spacing-normal);
  margin: 0;
}

/* Global responsive rules for legacy section titles that haven't been migrated yet */
@media (max-width: 768px) {
  .capabilities-main-title,
  .our-work-title,
  .testimonials-title,
  .next-steps-title,
  .solutions-main-title,
  .section-header-title,
  .dorsey-title,
  .nextsteps-title {
    font-size: var(--font-size-section-heading-tablet) !important;
    line-height: var(--line-height-section-heading) !important;
  }
  .capabilities-section-title,
  .capability-title,
  .feature-title,
  .subsection-title {
    font-size: var(--font-size-subheading-tablet) !important;
    line-height: var(--line-height-subheading) !important;
  }
}

@media (max-width: 480px) {
  .capabilities-main-title,
  .our-work-title,
  .testimonials-title,
  .next-steps-title,
  .solutions-main-title,
  .section-header-title,
  .dorsey-title,
  .nextsteps-title {
    font-size: var(--font-size-section-heading-mobile) !important;
    line-height: var(--line-height-section-heading) !important;
  }
  .capabilities-section-title,
  .capability-title,
  .feature-title,
  .subsection-title {
    font-size: var(--font-size-subheading-mobile) !important;
    line-height: var(--line-height-subheading) !important;
  }
}

/* Responsive design */
@media (max-width: var(--breakpoint-tablet)) {
  .responsibility-title {
    font-size: var(--font-size-section-heading-tablet);
    line-height: var(--line-height-section-heading);
  }

  .responsibility-description {
    font-size: var(--font-size-section-description-tablet);
    line-height: var(--line-height-section-description);
  }

  .contributors-intro,
  .contributors-list {
    font-size: var(--font-size-base);
    line-height: var(--line-height-loose);
  }

  .contributors-content {
    padding: 0 var(--dia-space-2);
  }
}

@media (max-width: var(--breakpoint-mobile)) {
  .responsibility-title {
    font-size: var(--font-size-section-heading-mobile);
    line-height: var(--line-height-section-heading);
  }

  .responsibility-description {
    font-size: var(--font-size-section-description-mobile);
    line-height: var(--line-height-section-description);
  }

  .contributors-intro,
  .contributors-list {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-loose);
  }
}
