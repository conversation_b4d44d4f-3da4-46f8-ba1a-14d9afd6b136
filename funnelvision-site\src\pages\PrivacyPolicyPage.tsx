import React from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import FadeIn from '../components/animations/FadeIn';

const PrivacyPolicyPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Header />

      {/* Privacy Policy Content */}
      <div className="pt-24 pb-16">
        {/* Header Section */}
        <div className="bg-background-elevated py-16">
          <div className="container-base">
            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
              <div className="text-center">
                <h1 className="section-heading text-text-primary mb-4">Privacy Policy</h1>
                <div className="max-w-2xl mx-auto">
                  <p className="body-text text-text-secondary mb-6">
                    Your privacy is important to us. This policy explains how we collect, 
                    use, and protect your information when you use our services.
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-text-muted mb-4">Last updated: January 1, 2025</p>
                  <Link
                    to="/"
                    className="inline-flex items-center gap-2 text-primary hover:text-primary-hover transition-colors"
                  >
                    ← Back to Home
                  </Link>
                </div>
              </div>
            </FadeIn>
          </div>
        </div>

        {/* Content Section */}
        <div className="py-16">
          <div className="container-base">
            <div className="max-w-4xl mx-auto">
              
              <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
                <section className="mb-12">
                  <h2 className="text-2xl font-semibold text-text-primary mb-4">Information We Collect</h2>
                  <div className="prose prose-gray max-w-none">
                    <p className="body-text text-text-secondary mb-4">
                      We collect information you provide directly to us, such as when you create an account, 
                      subscribe to our newsletter, or contact us for support. This may include:
                    </p>
                    <ul className="list-disc list-inside space-y-2 text-text-secondary">
                      <li>Name and contact information</li>
                      <li>Company information</li>
                      <li>Marketing preferences</li>
                      <li>Communication history</li>
                    </ul>
                  </div>
                </section>
              </FadeIn>

              <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-200">
                <section className="mb-12">
                  <h2 className="text-2xl font-semibold text-text-primary mb-4">How We Use Your Information</h2>
                  <div className="prose prose-gray max-w-none">
                    <p className="body-text text-text-secondary mb-4">
                      We use the information we collect to:
                    </p>
                    <ul className="list-disc list-inside space-y-2 text-text-secondary">
                      <li>Provide and improve our services</li>
                      <li>Communicate with you about our services</li>
                      <li>Send you marketing communications (with your consent)</li>
                      <li>Analyze usage patterns and improve user experience</li>
                    </ul>
                  </div>
                </section>
              </FadeIn>

              <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-300">
                <section className="mb-12">
                  <h2 className="text-2xl font-semibold text-text-primary mb-4">Information Sharing</h2>
                  <div className="prose prose-gray max-w-none">
                    <p className="body-text text-text-secondary mb-4">
                      We do not sell, trade, or otherwise transfer your personal information to third parties 
                      without your consent, except as described in this policy. We may share information:
                    </p>
                    <ul className="list-disc list-inside space-y-2 text-text-secondary">
                      <li>With service providers who assist in our operations</li>
                      <li>When required by law or to protect our rights</li>
                      <li>In connection with a business transaction</li>
                    </ul>
                  </div>
                </section>
              </FadeIn>

              <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-400">
                <section className="mb-12">
                  <h2 className="text-2xl font-semibold text-text-primary mb-4">Data Security</h2>
                  <div className="prose prose-gray max-w-none">
                    <p className="body-text text-text-secondary">
                      We implement appropriate security measures to protect your personal information 
                      against unauthorized access, alteration, disclosure, or destruction. However, 
                      no method of transmission over the internet is 100% secure.
                    </p>
                  </div>
                </section>
              </FadeIn>

              <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-500">
                <section className="mb-12">
                  <h2 className="text-2xl font-semibold text-text-primary mb-4">Your Rights</h2>
                  <div className="prose prose-gray max-w-none">
                    <p className="body-text text-text-secondary mb-4">
                      You have the right to:
                    </p>
                    <ul className="list-disc list-inside space-y-2 text-text-secondary">
                      <li>Access and update your personal information</li>
                      <li>Request deletion of your personal information</li>
                      <li>Opt out of marketing communications</li>
                      <li>Request a copy of your data</li>
                    </ul>
                  </div>
                </section>
              </FadeIn>

              <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-600">
                <section className="mb-12">
                  <h2 className="text-2xl font-semibold text-text-primary mb-4">Contact Us</h2>
                  <div className="prose prose-gray max-w-none">
                    <p className="body-text text-text-secondary">
                      If you have any questions about this Privacy Policy, please contact us at:
                    </p>
                    <div className="mt-4 p-6 bg-background-elevated rounded-standard">
                      <p className="body-text text-text-primary font-medium">FunnelVision</p>
                      <p className="body-text text-text-secondary">Email: <EMAIL></p>
                    </div>
                  </div>
                </section>
              </FadeIn>

            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default PrivacyPolicyPage;
