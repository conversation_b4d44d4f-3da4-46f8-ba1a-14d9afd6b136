import React from "react";

interface LargeTextSectionProps {
  children: React.ReactNode;
  className?: string;
}

const LargeTextSection: React.FC<LargeTextSectionProps> = ({
  children,
  className = ""
}) => {
  return (
    <section
      className={`w-full bg-background ${className}`}
      style={{
        padding: '6rem 0' // 96dp - Material Design section padding
      }}
    >
      <div className="w-full max-w-screen-2xl mx-auto px-4 md:px-6 lg:px-8">
        <div
          className="mx-auto text-center"
          style={{
            maxWidth: '50rem' // 800px - Material Design content width constraint
          }}
        >
          <div className="large-text-content">
            {children}
          </div>
        </div>
      </div>
    </section>
  );
};

export default LargeTextSection;
