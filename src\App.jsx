import { useState, useEffect, Suspense, lazy } from "react";
import Header from "./components/Header";
import { <PERSON> } from "./components/ui/animated-hero";
import LargeTextSection from "./components/LargeTextSection";
import { RevealText } from "./components/TextRevealByWord";
import EnhancingGeminiSection from "./components/EnhancingGeminiSection";
import ProblemSection from "./components/ProblemSection";
import ROASvsPAXSection from "./components/ROASvsPAXSection";
import SolutionsSection from "./components/SolutionsSection";
import ProfitApproachSection from "./components/ProfitApproachSection";
import OurWorkSection from "./components/OurWorkSection";
import TestimonialsSection from "./components/TestimonialsSection";
import NextStepsSection from "./components/NextStepsSection";
import FAQSection from "./components/FAQSection";
import PartnershipsSection from "./components/PartnershipsSection";
import SecondaryScrollMenu from "./components/SecondaryScrollMenu";
import Footer from "./components/Footer";

// Lazy load PrivacyPolicy to avoid blocking issues
const PrivacyPolicy = lazy(() => import("./components/PrivacyPolicy"));
const BookACall = lazy(() => import("./components/BookACall"));
// Typography test temporarily removed
import "./App.css";

function App() {
  // Simple routing state
  const [currentPage, setCurrentPage] = useState('home');

  // Handle routing based on URL path
  useEffect(() => {
    const path = window.location.pathname;
    if (path === '/privacy') {
      setCurrentPage('privacy');
    } else if (path === '/book-a-call') {
      setCurrentPage('book-a-call');
    } else {
      setCurrentPage('home');
    }

    // Handle browser back/forward buttons
    const handlePopState = () => {
      const path = window.location.pathname;
      if (path === '/privacy') {
        setCurrentPage('privacy');
      } else if (path === '/book-a-call') {
        setCurrentPage('book-a-call');
      } else {
        setCurrentPage('home');
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Render Privacy Policy page
  if (currentPage === 'privacy') {
    return (
      <div className="app">
        <Header />
        <main className="main-content">
          <Suspense fallback={<div style={{padding: '2rem', textAlign: 'center'}}>Loading...</div>}>
            <PrivacyPolicy />
          </Suspense>
        </main>
        <Footer />
      </div>
    );
  }

  // Render Book A Call page
  if (currentPage === 'book-a-call') {
    return (
      <div className="app">
        <Suspense fallback={<div style={{padding: '2rem', textAlign: 'center'}}>Loading...</div>}>
          <BookACall />
        </Suspense>
      </div>
    );
  }

  // Render Home page
  return (
    <div className="app">
      <Header />
      <SecondaryScrollMenu />

      <main className="main-content">
        <section className="section section-light">
          <Hero />
        </section>

        <section className="section section-light">
          <div className="container">
            <LargeTextSection>
              <h2>
                <RevealText content="**ROAS is a blunt instrument.** It hides margin bleed, ignores SKU economics, and often misleads your team. **PAX™ (Profit After eXpenditure)** is our north star. **It's how we know what's really working.**" />
              </h2>
            </LargeTextSection>
          </div>
        </section>

        <section id="problem" className="section section-gray">
          <ProblemSection />
        </section>

        <section id="roas-vs-pax" className="section section-light">
          <ROASvsPAXSection />
        </section>

        <section id="solutions" className="section section-light">
          <SolutionsSection />
        </section>

        <section id="profit-approach" className="section section-gray">
          <ProfitApproachSection />
        </section>

        <section id="our-work" className="section section-gray">
          <OurWorkSection />
        </section>

        <section id="testimonials" className="section section-light">
          <TestimonialsSection />
        </section>

        <section id="next-steps" className="section section-light">
          <NextStepsSection />
        </section>

        <section id="faq" className="section section-gray">
          <FAQSection />
        </section>

        <section id="partnerships" className="section section-light">
          <PartnershipsSection />
        </section>

        <section id="enhancing-gemini" className="section section-gray">
          <EnhancingGeminiSection />
        </section>
        

      </main>

      <Footer />
    </div>
  );
}

export default App;
