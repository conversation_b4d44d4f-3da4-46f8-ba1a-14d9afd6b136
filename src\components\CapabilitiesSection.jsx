import React from "react";
import "./CapabilitiesSection.css";
import "./ui/bento-grid.css";
import FadeIn from "./animations/FadeIn";
import ScaleIn from "./animations/ScaleIn";
import { BentoGrid, BentoCard } from "./ui/bento-grid";
import ROASvsPAXComparison from "./ROASvsPAXComparison";
import { Button } from "./ui/button";
import { Carousel, Card } from "./ui/FunnelVisionCardsCarousel";

const CapabilityCard = ({
  title,
  description,
  imagePlaceholder,
  offset = false,
  delay = 0,
  className = ''
}) => (
  <FadeIn threshold={0.1} rootMargin="0px" className={`fade-up delay-${delay}`}>
    <div className={`capability-card card-hover ${offset ? "capability-card--offset" : ""} ${className}`}>
      <div className="capability-image">
        <div className="capability-image-placeholder">{imagePlaceholder}</div>
      </div>
      <div className="capability-content">
        <h3 className="section-subheading">{title}</h3>
        <p className="section-subheading-description">{description}</p>
      </div>
    </div>
  </FadeIn>
);

// Content component for carousel cards
const CardContent = ({ title, description }) => {
  return (
    <div className="content-block">
      <p className="content-text">
        <span className="content-highlight">{title}</span>{" "}
        {description}
      </p>
    </div>
  );
};

const CapabilitiesSection = () => {
  // Define cards data for the Natural interaction carousel
  const naturalInteractionCardsData = [
    {
      category: "68%",
      title: "of DTC brands can't accurately measure true profit from their ad campaigns.",
      src: "https://images.unsplash.com/photo-1478737270239-2f02b77fc618?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
      content: <CardContent 
        title="Advanced voice recognition technology" 
        description="Detects different accents, languages and emotions, and gives fluid responses in 24 languages. Our AI system can accurately understand natural speech patterns even in noisy environments." 
      />
    },
    {
      category: "42%",
      title: "of potential customers now start product searches on platforms beyond Google",
      src: "https://images.unsplash.com/photo-1543269865-cbf427effbad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
      content: <CardContent 
        title="Real-time conversation flow" 
        description="Project Astra can intuitively start conversations and respond in the moment — without interrupting or time lag. The system maintains context throughout interactions for more natural dialogue." 
      />
    },
    {
      category: "31%",
      title: "higher profit margins on average for brands focusing on PAX instead of ROAS",
      src: "https://images.unsplash.com/photo-1551845728-6820a30c64e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80",
      content: <CardContent 
        title="Smart filtering technology" 
        description="Ignores distractions, like background conversation and irrelevant speech. The platform can distinguish between direct queries and ambient noise, maintaining focus on the primary conversation." 
      />
    },
    {
      category: "25%",
      title: "of DTC brands are not aware of the profit impact of their ad spend",
      src: "https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
      content: <CardContent 
        title="Continuously improving interactions" 
        description="The system adapts to your communication style and preferences over time, creating increasingly personalized experiences. It remembers past conversations and builds on established context." 
      />
    }
  ];
  
  const naturalInteractionCards = naturalInteractionCardsData.map((card, index) => (
    <Card key={card.title} card={card} index={index} />
  ));
  
  return (
    <section id="capabilities" style={{ backgroundColor: '#f5f5f7' }} className="capabilities-section">
      <div className="dia-container-cards">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="capabilities-header">
            <h2 className="section-heading text-center">
              Why Most DTC Brands Waste 30% of Their Ad Spend
            </h2>
          </div>
        </FadeIn>

        {/* Natural interaction carousel */}
        <div className="capabilities-content">
          <div className="natural-interaction-carousel">
            <Carousel items={naturalInteractionCards} />
          </div>
        </div>
        
        {/* Natural interaction heading and description with video */}
        <div className="capabilities-content">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
            <div className="capabilities-section-header">
              <h2 className="section-heading">
                ROAS vs PAX
              </h2>
              <div className="section-description">
                <p>
                In today's fragmented search landscape, relying solely on Google Ads means missing valuable traffic and leaving profit on the table. Without a unified, <span className="profit-text">profit-first approach</span> across all search platforms, scaling becomes unpredictable and increasingly expensive.
                </p>
              </div>
            </div>
          </FadeIn>
          
          <ScaleIn className="delay-100">
            <div className="capabilities-video">
              <ROASvsPAXComparison />
            </div>
          </ScaleIn>
        </div>

        {/* Action intelligence */}
        <div className="capabilities-content">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
            <div className="capabilities-section-header">
              <h2 className="capabilities-section-title">The Profit-First Search Marketing Ecosystem</h2>
              <div className="capabilities-section-description">
                <p>
                Our proprietary PAX (Profit After eXpenditue) methodology ensures every campaign is structured around your true margin, not just revenue. The result? Self-funding growth loops that compound profit while expanding your market reach.
                </p>
              </div>
            </div>
          </FadeIn>

          <BentoGrid className="bento-grid">
            <BentoCard
              name="Profit-Structured Campaigns"
              description="Every bid aligns with your true margin."
              href="/#next-steps" cta="Get started"
              Icon={() => (
                <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="2" width="6" height="20" rx="1" />
                  <rect x="16" y="2" width="6" height="20" rx="1" />
                  <rect x="9" y="2" width="6" height="20" rx="1" />
                </svg>
              )}
            />
            
            <BentoCard
              name="Data-Driven Decisions"
              description="Weekly insights show exactly which keywords, products, and audiences compound profit."
              href="/#next-steps" cta="Get started"
              Icon={() => (
                <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" />
                  <polyline points="14 2 14 8 20 8" />
                  <line x1="16" y1="13" x2="8" y2="13" />
                  <line x1="16" y1="17" x2="8" y2="17" />
                </svg>
              )}
            />
            
            <BentoCard
              name="Predictable Scaling"
              description="Clear roadmap for increasing spend without sacrificing margins."
              href="/#next-steps" cta="Get started"
              Icon={() => (
                <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <line x1="2" y1="12" x2="22" y2="12" />
                  <path d="M12 2a15.3 15.3 0 014 10 15.3 15.3 0 01-4 10 15.3 15.3 0 01-4-10 15.3 15.3 0 014-10z" />
                </svg>
              )}
            />
            
            <BentoCard
              name="Cross-Platform Synergy"
              description="Unified strategy across Google, YouTube, Bing, and organic search."
              href="/#next-steps" cta="Get started"
              Icon={() => (
                <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                  <line x1="16" y1="2" x2="16" y2="6" />
                  <line x1="8" y1="2" x2="8" y2="6" />
                  <line x1="3" y1="10" x2="21" y2="10" />
                </svg>
              )}
            />

            <BentoCard
              name="No Lock-In Contracts"
              description="We earn your business every month through results."
              href="/#next-steps" cta="Get started"
              Icon={() => (
                <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9" />
                  <path d="M13.73 21a2 2 0 01-3.46 0" />
                </svg>
              )}
            />
          </BentoGrid>
        </div>
      </div>
    </section>
  );
};

export default CapabilitiesSection;
