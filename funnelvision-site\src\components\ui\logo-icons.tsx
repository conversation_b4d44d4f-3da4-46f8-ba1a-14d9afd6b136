import React from "react";

/**
 * Logo data for use in the logo carousel
 * Each logo has a unique ID, name, and image component
 */

// Create SVG wrapper components for each logo image
const createSVGComponent = (id: number, name: string) => {
  // This creates a functional component that renders the SVG from the public directory
  return (props: React.ImgHTMLAttributes<HTMLImageElement>) => (
    <img 
      src={`/images/logos/${id}.svg`} 
      alt={name} 
      {...props} 
    />
  );
};

// Logo interface
export interface Logo {
  id: number;
  name: string;
  img: React.ComponentType<React.ImgHTMLAttributes<HTMLImageElement>>;
}

// Define logo objects with component references
export const logos: Logo[] = [
  { id: 1, name: "<PERSON>", img: createSVGComponent(1, "Apple") },
  { id: 2, name: "Supabase", img: createSVGComponent(2, "Supabase") },
  { id: 3, name: "Vercel", img: createSVGComponent(3, "Vercel") },
  { id: 4, name: "<PERSON><PERSON>", img: createSVGComponent(4, "<PERSON><PERSON>") },
  { id: 5, name: "<PERSON>", img: createSVGComponent(5, "<PERSON>") },
  { id: 6, name: "Pierre", img: createSVGComponent(6, "Pierre") },
  { id: 7, name: "BMW", img: createSVGComponent(7, "BMW") },
  { id: 8, name: "Claude", img: createSVGComponent(8, "Claude") },
  { id: 9, name: "Nextjs", img: createSVGComponent(9, "Nextjs") },
  { id: 10, name: "Tailwind", img: createSVGComponent(10, "Tailwind") },
  { id: 11, name: "Logo11", img: createSVGComponent(11, "Logo11") },
  { id: 12, name: "Logo12", img: createSVGComponent(12, "Logo12") },
];

// Export for backward compatibility
export const sampleLogos = logos;
