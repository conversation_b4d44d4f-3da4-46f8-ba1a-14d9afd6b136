import React from "react";
import "./OurWorkSection.css";
import FadeIn from "./animations/FadeIn";
import ScaleIn from "./animations/ScaleIn";
import CaseStudyCard from "./ui/CaseStudyCard";
import { getCaseStudyById } from "../data/caseStudies";

const OurWorkSection = () => {
  // Define cards data for case studies with modal data
  const caseStudyCardsData = [
    {
      image: "/images/case-studies/case-1.webp",
      logo: "/images/logos/10.svg",
      title: "Cut wasted ad spend by 52.6%",
      ctaText: "Read case",
      ctaLink: "#case-study-boulies",
      caseStudyData: getCaseStudyById('boulies')
    },
    {
      image: "/images/case-studies/case-2.webp",
      logo: "/images/logos/14.png",
      title: "Increased monthly revenue 122%",
      ctaText: "Read case",
      ctaLink: "#case-study-bunkie-life",
      caseStudyData: getCaseStudyById('bunkie-life')
    },
    {
      image: "/images/case-studies/case-3.webp",
      logo: "/images/logos/13.svg",
      title: "Increased revenue 8.4X in 12 months",
      ctaText: "Read case",
      ctaLink: "#case-study-kodiak",
      caseStudyData: getCaseStudyById('kodiak')
    },
  ];

  const caseStudyCards = caseStudyCardsData.map((card) => (
    <CaseStudyCard
      key={card.title}
      image={card.image}
      logo={card.logo}
      title={card.title}
      ctaText={card.ctaText}
      ctaLink={card.ctaLink}
      caseStudyData={card.caseStudyData}
    />
  ));

  return (
    <div className="devices-section">
      <div className="dia-container-cards">
        <div className="ourwork-container">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
            <h2 className="section-heading text-center">Our Work</h2>
            <div className="section-description text-center">
              <p>
                What Happens When You Focus on Profit
              </p>
            </div>
          </FadeIn>

          <div className="case-studies-grid">
            <ScaleIn className="delay-200">
              <div className="case-studies-grid-wrapper">
                {caseStudyCards}
              </div>
            </ScaleIn>
          </div>
        </div>


      </div>
    </div>
  );
};

export default OurWorkSection;
