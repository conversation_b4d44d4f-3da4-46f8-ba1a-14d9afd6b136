import React from 'react';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';

interface FadeInProps {
  children: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  className?: string;
}

/**
 * FadeIn component that animates its children into view when they intersect the viewport.
 */
const FadeIn: React.FC<FadeInProps> = ({ 
  children, 
  threshold = 0.2, 
  rootMargin = '0px', 
  className = '' 
}) => {
  const [ref, isVisible] = useIntersectionObserver({
    threshold,
    rootMargin,
  });

  const classNames = `fade-in ${isVisible ? 'visible' : ''} ${className}`.trim();

  return (
    <div ref={ref} className={classNames}>
      {children}
    </div>
  );
};

export default FadeIn;
