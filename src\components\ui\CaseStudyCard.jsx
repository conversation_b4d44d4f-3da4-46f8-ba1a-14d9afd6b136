import React, { useState } from 'react';
import './CaseStudyCard.css';
import CaseStudyModal from './CaseStudyModal';

/**
 * CaseStudyCard component - New card type for case studies
 * Features:
 * - Full-container image background
 * - Logo, title, and CTA button overlay
 * - 32px vertical spacing between elements
 * - Secondary CTA button styling
 * - Click to open full-screen modal
 */
const CaseStudyCard = ({
  image,
  logo,
  title,
  ctaText = "Read case",
  ctaLink = "#",
  className = "",
  caseStudyData = null
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleCardClick = (e) => {
    e.preventDefault();
    if (caseStudyData) {
      setIsModalOpen(true);
    } else {
      // Fallback to original link behavior if no case study data
      window.location.href = ctaLink;
    }
  };

  return (
    <>
      <div
        className={`case-study-card ${className}`}
        onClick={handleCardClick}
      >
        {/* Full container background image */}
        <div
          className="case-study-card-background"
          style={{ backgroundImage: `url(${image})` }}
        >
          {/* Content overlay */}
          <div className="case-study-card-overlay">
            <div className="case-study-card-content">
              {/* Logo */}
              {logo && (
                <div className="case-study-card-logo">
                  <img src={logo} alt="Company logo" />
                </div>
              )}

              {/* Title */}
              <h3 className="case-study-card-title">
                {title}
              </h3>

              {/* CTA Button */}
              <div className="case-study-card-cta call-to-action-container call-to-action-secondary">
                <span className="button-text button-text-secondary">
                  {ctaText}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Case Study Modal */}
      {caseStudyData && (
        <CaseStudyModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          caseStudyData={caseStudyData}
          image={image}
          logo={logo}
        />
      )}
    </>
  );
};

export default CaseStudyCard;
