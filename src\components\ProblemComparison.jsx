import React from "react";
import "./ProblemComparison.css";
import FadeIn from "./animations/FadeIn";

const ProblemItem = ({ icon, text, isPositive }) => (
  <div className={`problem-item ${isPositive ? "positive" : "negative"}`}>
    <span className="problem-icon">{icon}</span>
    <span className="problem-text">{text}</span>
  </div>
);

const RedXIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor" stroke="none">
    <circle cx="12" cy="12" r="10" fill="currentColor"/>
    <path d="m15 9-6 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="m9 9 6 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const GreenCheckIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor" stroke="none">
    <circle cx="12" cy="12" r="10" fill="currentColor"/>
    <path d="m9 12 2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ProblemComparison = () => {
  const roasProblems = [
    { text: "Optimizes for revenue" },
    { text: "Treats all products the same" },
    { text: "Focuses on CTR and CPC" },
    { text: "Reactive campaign structure" },
    { text: "One-size-fits-all media buying" },
    { text: "Outsourced execution" }
  ];

  const paxSolutions = [
    { text: "We optimize for profit" },
    { text: "We segment bids by margin tier" },
    { text: "We focus on contribution margin" },
    { text: "Every campaign is built from the margin up" },
    { text: "Custom strategy per client funnel" },
    { text: "Founder-led strategy with senior operators" }
  ];

  return (
    <div className="problem-comparison-container">
      {/* ROAS Problems Layout */}
      <div className="problem-layout roas-layout">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="problem-title-section">
            <h2 className="section-heading">ROAS Tells You Revenue.</h2>
            <div className="section-description">
              <p>
                By lumping together low- and high-margin SKUs, ignoring COGS, shipping, discounts, and incremental lift, it becomes a vanity metric that can push brands to “grow broke.”
              </p>
            </div>
          </div>
        </FadeIn>
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
          <div className="problem-card roas-card">
            <h4 className="problem-card-title">ROAS approach</h4>
            <div className="problem-items">
              {roasProblems.map((item, index) => (
                <ProblemItem
                  key={`roas-${index}`}
                  icon={<RedXIcon />}
                  text={item.text}
                  isPositive={false}
                />
              ))}
            </div>
          </div>
        </FadeIn>
      </div>

      {/* PAX Solutions Layout */}
      <div className="problem-layout pax-layout">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-300">
          <div className="problem-title-section">
            <h2 className="section-heading">PAX™ Tells You the Truth.</h2>
            <div className="section-description">
              <p>
                We created the PAX framework to focus on what actually drives sustainable growth.
              </p>
            </div>
          </div>
        </FadeIn>
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-200">
          <div className="problem-card pax-card">
            <h4 className="problem-card-title">PAX approach</h4>
            <div className="problem-items">
              {paxSolutions.map((item, index) => (
                <ProblemItem
                  key={`pax-${index}`}
                  icon={<GreenCheckIcon />}
                  text={item.text}
                  isPositive={true}
                />
              ))}
            </div>
          </div>
        </FadeIn>
      </div>
    </div>
  );
};

export default ProblemComparison;
