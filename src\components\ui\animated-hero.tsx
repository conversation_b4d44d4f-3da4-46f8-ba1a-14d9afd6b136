import React, { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { LogoCarousel } from './logo-carousel';
import { logos } from './logo-icons';
import './animated-hero.css';

const Hero: React.FC = () => {
  const [columnCount, setColumnCount] = useState(4);

  useEffect(() => {
    // Function to update column count based on window width
    const updateColumnCount = () => {
      if (window.innerWidth <= 768) {
        setColumnCount(3);
      } else {
        setColumnCount(4);
      }
    };

    // Set column count on initial render
    updateColumnCount();

    // Add event listener for window resize
    window.addEventListener('resize', updateColumnCount);

    // Clean up event listener
    return () => window.removeEventListener('resize', updateColumnCount);
  }, []);

  return (
    <section className="hero-animated">
      <div className="container">
        <div className="hero-content">

          <div className="hero-title">
            <h1 className="headline-wrapper">
              <span className="hero-title-line">
                <span className="hero-title-better">Paid Ads that convert without vanity metrics</span>
              </span>
            </h1>
          </div>

          <div className="hero-text-container">
            <p className="hero-description">
              We build profit-first campaigns across Google, YouTube, Bing & AI Search
              that work in your P&L, <strong>not just in your ad dashboard</strong>.
            </p>
          </div>

          <div className="hero-buttons">
            <div className="call-to-action">
              <a href="#next-steps" className="call-to-action-container call-to-action-primary">
                <span className="button-text button-text-primary cta-text">
                  Get Free Profit Analysis
                </span>
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="cta-arrow"
                >
                  <path
                    d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z"
                    fill="currentColor"
                  />
                </svg>
              </a>
              <a href="#problem" className="call-to-action-container call-to-action-secondary">
                <span className="button-text button-text-secondary cta-text">
                  See How PAX™ Works
                </span>
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="cta-arrow"
                >
                  <path
                    d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z"
                    fill="currentColor"
                  />
                </svg>
              </a>
            </div>
            <div className="rating-container">
              <div className="star-container">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} size={16} fill="#C39753" stroke="none" />
                ))}
              </div>
              <span className="rating-text">G2-verified rating</span>
            </div>
          </div>

          <div className="logos-section-container">
            <div className="logos-title-container">
              <div className="logos-title">The best brands are already here</div>
            </div>
            <div className="logos-carousel-wrapper">
              <LogoCarousel
                columnCount={columnCount}
                logos={logos}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export { Hero };