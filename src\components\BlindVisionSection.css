.blind-vision-section {
  padding: var(--space-30) 0;
  background-color: var(--color-background-primary);
  width: 100%;
  overflow-x: hidden;
}

.blind-vision-header {
  text-align: center;
  margin-bottom: var(--space-20);
  margin-left: auto;
  margin-right: auto;
}

.blind-vision-title {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-section-heading-mobile);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin: 0 0 var(--space-9) 0;
}

.blind-vision-description {
  margin: 0 auto;
}

.blind-vision-description p {
  color: var(--color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-section-description-mobile);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-relaxed);
  margin: 0;
  text-align: center;
}

.blind-vision-large-text {
  margin: 0 auto var(--space-20) auto;
  text-align: center;
}

.blind-vision-large-text p {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin: 0;
}

.blind-vision-features {
  display: flex;
  justify-content: center;
  margin-top: var(--dia-space-12);
  width: 100%;
  gap: var(--dia-space-12);
  margin-bottom: var(--dia-space-16);
  margin-left: auto;
  margin-right: auto;
}

.vision-carousel-wrapper {
  width: 100%;
  max-width: 100vw; /* Never exceed viewport width */
  margin: 2rem auto;
  padding: 0; /* Remove padding that causes overflow */
  overflow: hidden; /* Prevent overflow */
  position: relative;
  box-sizing: border-box;
}

.feature-card {
  width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f7;
  border-radius: 24px;
  border: 1px solid rgba(0,0,0,0.04);
  overflow: hidden;
  padding-top: 0;
  padding-bottom: 0;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.feature-image {
  width: 100%;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-image-placeholder {
  font-size: 80px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-content {
  padding: var(--item-spacing-24);
  text-align: center;
}

.feature-content p {
  color: var(--color-white-solid);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-20);
  font-weight: 400;
  line-height: var(--line-height-28);
  margin: 0;
  text-align: center;
}

.blind-vision-partnership {
  margin: 0 auto var(--space-20) auto;
  text-align: center;
}

.blind-vision-partnership p {
  color: #202124;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-47);
  font-weight: 700;
  line-height: var(--line-height-56);
  letter-spacing: var(--letter-spacing-tight);
  margin: 0;
  text-align: center;
}

.blind-vision-cta-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-9);
  margin: 0 auto var(--space-30) auto;
  text-align: center;
}

.blind-vision-cta-description p {
  color: var(--color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.blind-vision-cta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-7);
  border-radius: var(--radius-full);
  background: var(--color-primary);
  color: var(--color-text-inverse);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  text-decoration: none;
  transition: all var(--transition-base);
  white-space: nowrap;
  box-shadow: var(--shadow-md);
  border: none;
}

.blind-vision-cta:hover {
  transform: translateY(-2px);
  background: var(--color-primary-dark);
  box-shadow: var(--shadow-lg);
}

/* Video player styles */
.video-play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-primary);
  cursor: pointer;
  transition: transform 0.2s;
}

.video-play-button:hover {
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 1024px) {
  .blind-vision-features {
    flex-direction: column;
    align-items: center;
    gap: var(--dia-space-6);
  }

  .feature-card {
    width: 100%;
  }
}

@media (min-width: 768px) {
  .blind-vision-title {
    font-size: var(--font-size-section-heading-tablet);
    line-height: var(--line-height-tight);
  }

  .blind-vision-section {
    padding: var(--space-20) 0;
  }

  .blind-vision-description p {
    font-size: var(--font-size-section-description-tablet);
    line-height: var(--line-height-relaxed);
  }

  .blind-vision-large-text p,
  .blind-vision-partnership p {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
  }

  .blind-vision-cta-description p {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-relaxed);
  }
}

@media (min-width: 1920px) {
  .blind-vision-title {
    font-size: var(--font-size-section-heading-desktop);
    line-height: var(--line-height-tight); /* Added line-height for consistency */
  }
}

@media (max-width: 480px) {
  .blind-vision-title {
    font-size: 36px;
    line-height: 44px;
  }

  .blind-vision-large-text p,
  .blind-vision-partnership p {
    font-size: var(--font-size-28);
    line-height: var(--line-height-36);
  }

  .feature-image-placeholder {
    font-size: 60px;
  }
}
