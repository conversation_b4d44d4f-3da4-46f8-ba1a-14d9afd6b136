import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCarouselTimer } from '../../hooks/useCarouselTimer';
import CarouselNavigation from './CarouselNavigation';
import './ServiceCarousel.css';

/**
 * ServiceCarousel component - Animated carousel with timer-based navigation
 * @param {Object} props
 * @param {Array} props.images - Array of image paths
 * @param {number} props.duration - Duration per slide in milliseconds (default: 10000)
 * @param {string} props.serviceName - Name of the service for accessibility and debugging
 */
const ServiceCarousel = ({
  images = [
    '/images/services/google-ads.webp'
  ],
  duration = 10000,
  serviceName = "Service"
}) => {
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [loadedImages, setLoadedImages] = useState({});

  const {
    currentSlide,
    progress,
    isPlaying,
    carouselRef,
    goToSlide,
    nextSlide,
    prevSlide
  } = useCarouselTimer(images.length, duration);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!carouselRef.current) return;

      // Only handle keyboard events when carousel is focused or contains focus
      const isCarouselFocused = carouselRef.current.contains(document.activeElement);
      if (!isCarouselFocused) return;

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          prevSlide();
          break;
        case 'ArrowRight':
          event.preventDefault();
          nextSlide();
          break;
        case ' ':
        case 'Spacebar':
          event.preventDefault();
          // Toggle play/pause functionality could be added here
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [prevSlide, nextSlide]);

  // Preload images
  useEffect(() => {
    const loadImages = async () => {
      const imagePromises = images.map((src, index) => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => {
            setLoadedImages(prev => ({ ...prev, [index]: src }));
            resolve(src);
          };
          img.onerror = reject;
          img.src = src;
        });
      });

      try {
        await Promise.all(imagePromises);
        setImagesLoaded(true);
      } catch (error) {
        console.error('Error loading carousel images:', error);
        setImagesLoaded(true); // Still show carousel even if some images fail
      }
    };

    loadImages();
  }, [images]);

  // Animation variants for image transitions
  const slideVariants = {
    enter: {
      opacity: 0,
      scale: 1.05,
    },
    center: {
      opacity: 1,
      scale: 1,
    },
    exit: {
      opacity: 0,
      scale: 0.95,
    }
  };

  const slideTransition = {
    duration: 0.5,
    ease: "easeInOut"
  };

  // Function to render slide content (images only)
  const renderSlideContent = () => {
    return (
      <img
        src={loadedImages[currentSlide] || images[currentSlide]}
        alt={`${serviceName} showcase ${currentSlide + 1}`}
        className="service-carousel-image"
        loading="lazy"
      />
    );
  };

  if (!imagesLoaded) {
    return (
      <div className="service-carousel-container" ref={carouselRef}>
        <div className="service-carousel-loading">
          <div className="service-carousel-skeleton" />
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="service-carousel-container"
      ref={carouselRef}
      tabIndex={0}
      role="region"
      aria-label={`${serviceName} showcase carousel`}
      aria-live="polite"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.1 }}
    >
      {/* Navigation - Moved to top for visibility */}
      <CarouselNavigation
        currentSlide={currentSlide}
        slideCount={images.length}
        progress={progress}
        onSlideChange={goToSlide}
        onNext={nextSlide}
        onPrev={prevSlide}
      />

      {/* Main Content Display */}
      <div className="service-carousel-image-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            className="service-carousel-image-wrapper"
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={slideTransition}
          >
            {renderSlideContent()}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Debug info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="service-carousel-debug">
          <small>
            {serviceName} - Slide: {currentSlide + 1}/{images.length} |
            Progress: {Math.round(progress)}% |
            Playing: {isPlaying ? 'Yes' : 'No'}
          </small>
        </div>
      )}
    </motion.div>
  );
};

export default ServiceCarousel;
