import React, { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { LogoCarousel } from './ui/LogoCarousel';
import { logos } from './ui/logo-icons';

const Hero: React.FC = () => {
  const [columnCount, setColumnCount] = useState(4);

  useEffect(() => {
    // Function to update column count based on window width
    const updateColumnCount = () => {
      if (window.innerWidth <= 905) { // Material Design lg breakpoint
        setColumnCount(3);
      } else {
        setColumnCount(4);
      }
    };

    // Set column count on initial render
    updateColumnCount();

    // Add event listener for window resize
    window.addEventListener('resize', updateColumnCount);

    // Clean up event listener
    return () => window.removeEventListener('resize', updateColumnCount);
  }, []);

  return (
    <section
      className="w-full bg-background min-h-screen flex flex-col justify-center items-center box-border md:min-h-[90vh] lg:min-h-[85vh] py-[var(--dia-space-15)] px-[var(--dia-space-9)]"
    >
      <div className="w-full max-w-screen-2xl mx-auto px-4 md:px-6 lg:px-8">
        <div
          className="flex flex-col items-center w-full text-center relative max-w-4xl mx-auto gap-[var(--dia-space-11)] pt-[var(--dia-space-14)]"
        >

          {/* Hero Title */}
          <div className="flex flex-col items-center w-full relative gap-0">
            <h1 className="m-0 p-0">
              <span
                className="flex justify-center items-center flex-nowrap w-full whitespace-nowrap mt-[var(--dia-space-2)]"
              >
                <span
                  className="text-text-primary text-center"
                  style={{
                    fontFamily: 'var(--font-family-inter)',
                    fontSize: 'var(--dia-hero-title-size)',
                    fontWeight: 'var(--dia-hero-title-weight)',
                    lineHeight: 'var(--dia-hero-title-line-height)',
                    letterSpacing: 'var(--dia-hero-title-spacing)'
                  }}
                >
                  Better Than&nbsp;
                </span>
                <span
                  className="text-text-primary"
                  style={{
                    fontSize: 'var(--dia-hero-title-size)',
                    fontWeight: 'var(--dia-hero-title-weight)',
                    letterSpacing: 'var(--dia-hero-title-spacing)'
                  }}
                >
                  Vanity Metrics
                </span>
              </span>
            </h1>
          </div>

          {/* Hero Description */}
          <div className="w-full max-w-3xl">
            <p
              className="text-text-secondary text-center m-0"
              style={{
                fontFamily: 'var(--font-family-inter)',
                fontSize: 'var(--dia-hero-description-size)',
                fontWeight: 'var(--dia-hero-description-weight)',
                lineHeight: 'var(--dia-hero-description-line-height)',
                letterSpacing: 'var(--dia-hero-description-spacing)'
              }}
            >
              We build profit-first campaigns across Google, YouTube, Bing & AI Search
              that work in your P&L, <strong className="font-semibold text-text-primary">not just in your ad dashboard</strong>.
            </p>
          </div>

          {/* Hero Buttons */}
          <div className="flex flex-col items-center w-full gap-[var(--dia-space-14)]">
            <div className="flex flex-col md:flex-row w-full md:w-auto md:justify-center gap-[var(--dia-space-6)]">
              <a
                href="#next-steps"
                className="
                  inline-flex items-center justify-center
                  bg-primary text-white border-none cursor-pointer
                  transition-all duration-200 hover:bg-black
                  w-full md:w-auto
                  py-[var(--dia-space-9)] px-[var(--dia-space-8)] gap-[var(--dia-space-6)]
                  rounded-[var(--dia-radius-standard)] text-[var(--btn-cta-size)]
                "
                style={{
                  fontWeight: 'var(--btn-cta-weight)',
                  lineHeight: 'var(--btn-cta-line-height)',
                  textDecoration: 'none'
                }}
              >
                <span>Get Free Profit Analysis</span>
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="cta-arrow transition-transform duration-200"
                >
                  <path
                    d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z"
                    fill="currentColor"
                  />
                </svg>
              </a>
              <a
                href="#problem"
                className="
                  inline-flex items-center justify-center
                  bg-background-cta-secondary text-text-primary border border-card-border cursor-pointer
                  transition-all duration-200 hover:bg-card-background
                  w-full md:w-auto
                  py-[var(--dia-space-9)] px-[var(--dia-space-8)] gap-[var(--dia-space-6)]
                  rounded-[var(--dia-radius-standard)] text-[var(--btn-cta-size)]
                "
                style={{
                  fontWeight: 'var(--btn-cta-weight)',
                  lineHeight: 'var(--btn-cta-line-height)',
                  textDecoration: 'none'
                }}
              >
                <span>See How PAX™ Works</span>
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="cta-arrow transition-transform duration-200"
                >
                  <path
                    d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z"
                    fill="currentColor"
                  />
                </svg>
              </a>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-[var(--dia-space-6)]">
              <div className="flex items-center gap-[var(--dia-space-2)]">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} size={16} fill="#C39753" stroke="none" />
                ))}
              </div>
              <span
                className="text-text-muted text-[var(--dia-font-size-sm)]"
                style={{
                  fontFamily: 'var(--font-family-inter)',
                  fontWeight: 'var(--dia-font-weight-normal)',
                  letterSpacing: 'var(--dia-body-text-spacing)'
                }}
              >
                G2-verified rating
              </span>
            </div>
          </div>

          {/* Logos Section */}
          <div className="w-full max-w-4xl overflow-x-hidden">
            <div className="text-center w-full mb-[var(--dia-space-14)]">
              <div
                className="text-text-muted text-[var(--dia-font-size-sm)]"
                style={{
                  fontWeight: 'var(--dia-font-weight-medium)',
                  fontFamily: 'var(--font-family-inter)',
                  letterSpacing: 'var(--dia-body-text-spacing)'
                }}
              >
                The best brands are already here
              </div>
            </div>
            <div className="w-full">
              <LogoCarousel
                columnCount={columnCount}
                logos={logos}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
