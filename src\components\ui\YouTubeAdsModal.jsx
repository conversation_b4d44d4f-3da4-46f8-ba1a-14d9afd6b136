import React, { useEffect } from 'react';
import ReactDOM from 'react-dom';
import './YouTubeAdsModal.css';

/**
 * YouTubeAdsModal component - Full-screen overlay modal for YouTube Ads service details
 * Features:
 * - Full-screen overlay covering entire viewport
 * - Responsive layout: horizontal split (desktop/tablet) vs vertical stack (mobile)
 * - Left/Top section: Overview content
 * - Right/Bottom section: Features list
 * - Click overlay background, ESC key, or close button to close
 * - Smooth open/close animations
 * - Uses diabrowser design tokens for consistency
 */
const YouTubeAdsModal = ({ 
  isOpen, 
  onClose, 
  serviceName = "YouTube Ads"
}) => {
  // Handle escape key, body scroll lock, and cleanup
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Lock body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // Restore body scroll when modal closes
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click to close
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const modalContent = (
    <div 
      className="youtube-ads-modal-backdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="youtube-ads-modal-title"
    >
      <div className="youtube-ads-modal-panel">
        {/* Overview Section */}
        <div className="youtube-ads-modal-overview">
          <div className="youtube-ads-modal-content">
            <h2 id="youtube-ads-modal-title" className="youtube-ads-modal-title">
              YouTube Ads
            </h2>
            <h3 className="youtube-ads-modal-subtitle">
              Build and scale YouTube without guesswork
            </h3>
            <div className="youtube-ads-modal-description">
              <p>
                We turn YouTube and Shorts into a discovery-to-purchase engine that keeps paying for itself. Advanced creative frameworks pair with precise audience layering so every view earns its keep.
              </p>
              <p>
                We optimise for PAX at the asset-group level, retarget warm viewers across Search and Shopping, and deliver weekly insight loops that show exactly which hooks, lengths and CTAs print profit.
              </p>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="youtube-ads-modal-features">
          <div className="youtube-ads-modal-content">
            <h3 className="youtube-ads-modal-features-title">What's Included</h3>
            <div className="youtube-ads-modal-features-list">
              <div className="youtube-ads-modal-feature-item">
                <span className="youtube-ads-modal-checkmark">✓</span>
                <span>Creative Strategy & Production</span>
              </div>
              <div className="youtube-ads-modal-feature-item">
                <span className="youtube-ads-modal-checkmark">✓</span>
                <span>Audience Research & Targeting</span>
              </div>
              <div className="youtube-ads-modal-feature-item">
                <span className="youtube-ads-modal-checkmark">✓</span>
                <span>Campaign Setup & Optimization</span>
              </div>
              <div className="youtube-ads-modal-feature-item">
                <span className="youtube-ads-modal-checkmark">✓</span>
                <span>Performance Analytics & Insights</span>
              </div>
              <div className="youtube-ads-modal-feature-item">
                <span className="youtube-ads-modal-checkmark">✓</span>
                <span>Cross-Platform Retargeting</span>
              </div>
              <div className="youtube-ads-modal-feature-item">
                <span className="youtube-ads-modal-checkmark">✓</span>
                <span>Weekly Performance Reports</span>
              </div>
            </div>
          </div>
        </div>

        {/* Close Button */}
        <button
          className="youtube-ads-modal-close"
          onClick={onClose}
          aria-label="Close modal"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
  );

  // Render modal to document.body using React Portal
  return ReactDOM.createPortal(modalContent, document.body);
};

export default YouTubeAdsModal;
