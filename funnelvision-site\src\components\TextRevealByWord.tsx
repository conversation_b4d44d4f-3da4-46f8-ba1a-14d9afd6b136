import React, { useRef, useEffect, useState } from "react";
import { motion, useScroll, useMotionValueEvent } from "framer-motion";

interface ParsedWord {
  word: string;
  bold: boolean;
}

interface AnimatedWordProps {
  word: string;
  index: number;
  visibility: number;
  isBold: boolean;
}

/**
 * AnimatedWord - Component that handles the animation of individual words
 */
const AnimatedWord: React.FC<AnimatedWordProps> = ({
  word,
  index,
  visibility,
  isBold
}) => {
  // Calculate delay based on word position
  const delay = index * 0.05; // Slightly faster animation for smoother effect
  
  // Calculate y position based on visibility
  const yPosition = visibility === 1 ? 0 : 10;
  
  return (
    <motion.span
      style={{ 
        display: "inline-block",
        opacity: visibility,
        y: yPosition
      }}
      transition={{ 
        duration: 0.3,
        delay: delay,
        ease: "easeOut"
      }}
    >
      {isBold ? <strong>{word}</strong> : word}
    </motion.span>
  );
};

interface ScrollRevealTextProps {
  text: string;
}

/**
 * ScrollRevealText - Handles the scroll-based text reveal animation
 * exactly matching the example screenshots
 */
export const ScrollRevealText: React.FC<ScrollRevealTextProps> = ({ text }) => {
  const containerRef = useRef<HTMLSpanElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  // Track each word's visibility separately
  const [wordVisibility, setWordVisibility] = useState<number[]>([]);
  
  // Parse for **bold** markers
  const parsedWords: ParsedWord[] = [];
  const tokens = text.split(/(\*\*[^*]+\*\*)/); // keep bold tokens
  tokens.forEach(token => {
    if (!token) return;
    if (/^\*\*[^*]+\*\*$/.test(token)) {
      const clean = token.replace(/^\*\*/, '').replace(/\*\*$/, '');
      clean.split(/\s+/).forEach(w => parsedWords.push({ word: w, bold: true }));
    } else {
      token.split(/\s+/).forEach(w => w && parsedWords.push({ word: w, bold: false }));
    }
  });

  const words = parsedWords.map(obj => obj.word);
  
  // Set up initial visibility array - all words hidden
  useEffect(() => {
    setWordVisibility(Array(words.length).fill(0));
  }, [text, words.length]);
  
  // Handle scroll changes
  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    // Map scroll progress to word reveal
    // 0 = completely out of view, 1 = completely in view
    
    // Normalize between 0 and 1 for animation range
    const progress = Math.max(0, Math.min(1, latest * 2));
    
    // Calculate how many words should be visible
    const totalWords = words.length;
    const visibleWordCount = Math.floor(progress * totalWords);
    
    // Update visibility for each word
    setWordVisibility(prev => {
      const newVisibility = [...prev];
      
      // Set visible words
      for (let i = 0; i < visibleWordCount; i++) {
        newVisibility[i] = 1;
      }
      
      // Set invisible words
      for (let i = visibleWordCount; i < totalWords; i++) {
        newVisibility[i] = 0;
      }
      
      return newVisibility;
    });
  });
  
  return (
    <span ref={containerRef} style={{ display: "inline" }}>
      {parsedWords.map((obj, i) => (
        <React.Fragment key={i}>
          <AnimatedWord
            word={obj.word}
            index={i}
            visibility={wordVisibility[i] || 0}
            isBold={obj.bold}
          />
          {i !== words.length - 1 && " "}
        </React.Fragment>
      ))}
    </span>
  );
};

interface LineRevealProps {
  children: React.ReactNode;
}

/**
 * LineReveal - Handles scroll animation for blocks of text
 */
const LineReveal: React.FC<LineRevealProps> = ({ children }) => {
  // Direct string content
  if (typeof children === 'string') {
    return <ScrollRevealText text={children} />;
  }
  
  // React elements with text content
  if (React.isValidElement(children)) {
    const props = children.props as any;
    if (typeof props.children === 'string') {
      // Clone element with original props but replace text with animated version
      return React.cloneElement(children, {
        ...props,
        children: <ScrollRevealText text={props.children} />
      });
    }
  }
  
  return <>{children}</>;
};

interface RevealTextProps {
  content: React.ReactNode;
}

/**
 * RevealText - Main component for scroll-based text reveal
 * Each paragraph is independently animated based on scroll position
 */
export const RevealText: React.FC<RevealTextProps> = ({ content }) => {
  if (!content) return null;
  
  // For direct string content
  if (typeof content === 'string') {
    return <ScrollRevealText text={content} />;
  }
  
  // Process array of React elements (like paragraphs, headings)
  return (
    <>
      {React.Children.map(content, (child) => {
        // Handle direct string content
        if (typeof child === 'string') {
          // Wrap in paragraph while preserving scroll-based animation
          return <p><ScrollRevealText text={child} /></p>;
        }
        
        // Handle React elements
        if (React.isValidElement(child)) {
          // Text block elements get scroll animation
          if (child.type === 'p' || 
              child.type === 'h1' || 
              child.type === 'h2' || 
              child.type === 'h3') {
            return <LineReveal>{child}</LineReveal>;
          }
        }
        
        // Return unchanged for other element types
        return child;
      })}
    </>
  );
};
