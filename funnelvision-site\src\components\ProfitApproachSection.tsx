import React from "react";
import FadeIn from "./animations/FadeIn";
import { BentoCard, BentoGrid } from "./ui/BentoGrid";

interface ApproachCard {
  name: string;
  description: string;
  Icon: React.ComponentType;
}

const ProfitApproachSection: React.FC = () => {
  const approachCards: ApproachCard[] = [
    {
      name: "PAX Protocol™",
      description: "Our proprietary approach to scaling profit through full-funnel intent sculpting and AI-assisted account strategy.",
      Icon: () => (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <rect x="2" y="2" width="6" height="20" rx="1" />
          <rect x="16" y="2" width="6" height="20" rx="1" />
          <rect x="9" y="2" width="6" height="20" rx="1" />
        </svg>
      )
    },
    {
      name: "Margin-Segmented Campaigns",
      description: "Google & Bing accounts built from the COGS up—so you're never scaling unprofitable SKUs again.",
      Icon: () => (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" />
          <polyline points="14 2 14 8 20 8" />
          <line x1="16" y1="13" x2="8" y2="13" />
          <line x1="16" y1="17" x2="8" y2="17" />
        </svg>
      )
    },
    {
      name: "Daily Hands-On Management",
      description: "We don't 'set budgets and check next week.' We're in your account daily. Adjusting bids. Shifting focus. Protecting profit.",
      Icon: () => (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10" />
          <line x1="2" y1="12" x2="22" y2="12" />
          <path d="M12 2a15.3 15.3 0 014 10 15.3 15.3 0 01-4 10 15.3 15.3 0 01-4-10 15.3 15.3 0 014-10z" />
        </svg>
      )
    },
    {
      name: "Full-Funnel Retargeting",
      description: "From cold traffic to past buyers—we tailor strategy and creative for each intent level. Every campaign has a job.",
      Icon: () => (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
          <line x1="16" y1="2" x2="16" y2="6" />
          <line x1="8" y1="2" x2="8" y2="6" />
          <line x1="3" y1="10" x2="21" y2="10" />
        </svg>
      )
    },
    {
      name: "Cross-Platform Visibility",
      description: "Google, YouTube, Bing, AI-powered Search—we follow where your customers actually go.",
      Icon: () => (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9" />
          <path d="M13.73 21a2 2 0 01-3.46 0" />
        </svg>
      )
    },
    {
      name: "Real Reporting, Not Fluff",
      description: "You get weekly snapshots that show what's scaling and what's slipping—with commentary, not just metrics.",
      Icon: () => (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" />
          <path d="M8 12h8" />
          <path d="M12 8v8" />
        </svg>
      )
    }
  ];

  return (
    <section
      id="profit-approach"
      className="profit-approach-section bg-background w-full"
      style={{ padding: 'var(--dia-space-14) 0' }}
    >
      <div className="dia-container-cards">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
          <div
            className="text-center mx-auto"
            style={{
              marginBottom: 'var(--dia-space-12)',
              maxWidth: 'var(--width-843)'
            }}
          >
            <h2 className="section-subheading text-text-primary text-center">
              Built Around Profit.<br />
              Not Platform Promises.
            </h2>
            <div
              className="section-description text-center"
              style={{ marginTop: 'var(--dia-space-6)' }}
            >
              <p className="body-text text-text-secondary">
                Clicks aren't revenue.
                We believe in structured campaigns that move your bottom line, across the whole search ecosystem.
              </p>
            </div>
          </div>
        </FadeIn>

        <BentoGrid className="bento-grid">
          {approachCards.map((card) => (
            <BentoCard
              key={card.name}
              name={card.name}
              description={card.description}
              Icon={card.Icon}
            />
          ))}
        </BentoGrid>
      </div>
    </section>
  );
};

export default ProfitApproachSection;
