import React from "react";
import "./AssistanceDevicesSection.css";
import "./ui/bento-grid.css";
import FadeIn from "./animations/FadeIn";
import { BentoCard, BentoGrid } from "./ui/bento-grid";

const AssistanceDevicesSection = () => {
  return (
    <section className="assistance-devices-section">
      <div className="container">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div className="devices-header">
            <h2 className="section-subheading">Take the next step</h2>
            <div className="section-description">
              <p>
                Project Astra works on Android phones and prototype glasses.
                Cross-device memory means you can switch devices and carry on the
                same conversation.
              </p>
            </div>
          </div>
        </FadeIn>

        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <BentoGrid className="bento-grid">
            <BentoCard 
              name="Mobile" 
              className="lg:col-start-1 lg:row-start-1"
              description="Point your phone camera at what you're interested in, and start a conversation. Or share your screen to unlock a new level of interactive assistance."
              Icon={() => (
                <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17 1.01L7 1C5.9 1 5 1.9 5 3v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z" />
                </svg>
              )}
              href="#mobile"
            />
            
            <BentoCard 
              name="Glasses" 
              className="lg:col-start-2 lg:row-start-1"
              description="Project Astra integrates seamlessly with prototype glasses to see the world as you see it — for an even more immersive and helpful experience."
              Icon={() => (
                <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15.5 5.5c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zM5 12c-2.8 0-5 2.2-5 5s2.2 5 5 5 5-2.2 5-5-2.2-5-5-5zm0 8.5c-1.9 0-3.5-1.6-3.5-3.5s1.6-3.5 3.5-3.5 3.5 1.6 3.5 3.5-1.6 3.5-3.5 3.5zm5.8-10l2.4-2.4.8.8c1.3 1.3 3 2.1 5.1 2.1V9c-1.5 0-2.7-.6-3.6-1.5l-1.9-1.9c-.5-.4-1-.6-1.6-.6s-1.1.2-1.4.6L7.8 8.4c-.4.4-.6.9-.6 1.4 0 .6.2 1.1.6 1.4L11 14v5h2v-6.2l-2.2-2.3zM19 12c-2.8 0-5 2.2-5 5s2.2 5 5 5 5-2.2 5-5-2.2-5-5-5zm0 8.5c-1.9 0-3.5-1.6-3.5-3.5s1.6-3.5 3.5-3.5 3.5 1.6 3.5 3.5-1.6 3.5-3.5 3.5z" />
                </svg>
              )}
              href="#glasses"
            />

            <BentoCard 
              name="Seamless Experience" 
              className="lg:col-start-3 lg:row-start-1"
              description="Cross-device memory means you can switch between your phone and glasses while carrying on the same conversation."
              Icon={() => (
                <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-9c-4.97 0-9 4.03-9 9 0 2.92 1.4 5.5 3.57 7.12l2.07-2.07c-1.01-1.17-1.64-2.68-1.64-4.36 0-3.69 2.96-6.67 6.64-6.68s6.67 2.96 6.68 6.64c.01 3.69-2.96 6.66-6.64 6.67-.78 0-1.53-.13-2.24-.37l-2.12 2.12c1.33.59 2.81.92 4.36.92 5.97 0 10-4.42 10-9.01s-4.03-9-9-9z" />
                </svg>
              )}
              href="#seamless"
            />
          </BentoGrid>
        </FadeIn>
        
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
          <div className="capabilities-section-header">
            <h2 className="section-heading">Secure your spot.</h2>
            <div className="section-description">
            </div>
            <div className="cta-container">
              <a href="#book-a-call" className="btn btn-primary button-hover">
                Book a Strategy Call
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 19 19"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="btn-icon"
                >
                  <path
                    d="M14.4912 14.7393H3.99121V4.23926H9.24121V2.73926H3.99121C3.59339 2.73926 3.21186 2.89729 2.93055 3.1786C2.64925 3.4599 2.49121 3.84143 2.49121 4.23926V14.7393C2.49121 15.1371 2.64925 15.5186 2.93055 15.7999C3.21186 16.0812 3.59339 16.2393 3.99121 16.2393H14.4912C15.3162 16.2393 15.9912 15.5643 15.9912 14.7393V9.48926H14.4912V14.7393ZM10.7412 2.73926V4.23926H13.4337L6.06121 11.6118L7.11871 12.6693L14.4912 5.29676V7.98926H15.9912V2.73926H10.7412Z"
                    fill="white"
                  />
                </svg>
              </a>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default AssistanceDevicesSection;
