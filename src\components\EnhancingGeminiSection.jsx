import React from "react";
import "./EnhancingGeminiSection.css";
import FadeIn from "./animations/FadeIn";
import ScaleIn from "./animations/ScaleIn";
import { LogoCarousel } from "./ui/logo-carousel";
import { logos as logoIcons } from "./ui/logo-icons";
// Ensure ScaleIn CSS is imported
import "./animations/ScaleIn.css";

// Use all logos for variety in different sections
const topRowLogos = [...logoIcons];
const bottomRowLogos = [...logoIcons].reverse();

const EnhancingGeminiSection = () => {
  return (
    <div className="enhancing-gemini-content">
      <div className="container">
        {/* Video Card with Text Overlay - Direct duplication from CapabilitiesSection */}
        <div className="enhancing-gemini-main-content">
          {/* Text Overlay */}
          <div className="enhancing-gemini-text-overlay">
            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
              <h2 className="enhancing-gemini-title">In under 5 minutes</h2>
            </FadeIn>
            
            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-200">
              <p className="enhancing-gemini-description">
                I'll walk you through the only ad strategy that matters in 2025:
                One built to maximize your true profit per click.
              </p>
            </FadeIn>
          </div>
          
          {/* Video Card */}
          <ScaleIn className="delay-100">
            <div className="enhancing-video-card">
              <div className="play-button">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="btn-icon"
                >
                  <path d="M8 5v14l11-7z" fill="white" />
                </svg>
              </div>
            </div>
          </ScaleIn>
        </div>
      </div>
    </div>
  );
};

export default EnhancingGeminiSection;
