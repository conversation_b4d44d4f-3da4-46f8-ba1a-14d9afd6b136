/* Devices section styles */
#our-work,
.devices-section {
  background-color: var(--dia-color-background);
  width: 100%;
  overflow: hidden;
}

.devices-section {
  padding: var(--dia-space-15) 0;
}

/* Diabrowser migration: when .dia-typography and .dia-spacing are applied */
.devices-section.dia-typography.dia-spacing {
  padding: var(--dia-space-12) 0;
  background-color: var(--dia-color-background);
}


/* Our work container styles */
.ourwork-container {
  margin-bottom: var(--dia-space-15);
}

/* Diabrowser migration overrides */
.devices-section.dia-spacing .ourwork-container {
  margin-bottom: var(--dia-space-15);
}

.devices-section.dia-spacing .testimonials-container {
  margin-top: var(--dia-space-12);
}

/* Legacy dorsey-title class replaced with global content hierarchy tokens */
/* section-heading class styles are now defined globally in content-hierarchy.css */

/* Legacy dorsey-description class styles */
.ourwork-container .section-description {
  max-width: var(--width-616);
  margin: 0 auto var(--dia-space-14) auto;
  text-align: center;
}

/* section-description p styles are defined globally in content-hierarchy.css */

.case-studies-grid {
  margin-top: var(--dia-space-12);
  margin-bottom: var(--dia-space-14); /* Increased bottom margin for better section spacing */
}

.case-studies-grid-wrapper {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--dia-space-6);
  width: 100%;
}

/* Responsive grid layout */
@media (min-width: 800px) {
  .case-studies-grid-wrapper {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--dia-space-8);
  }
}

@media (min-width: 1000px) {
  .case-studies-grid-wrapper {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--dia-space-10);
  }
}

.case-carousel-wrapper {
  width: 100%;
  max-width: 100vw; /* Never exceed viewport width */
  margin: 2rem auto;
  padding: 0; /* Remove padding that causes overflow */
  overflow: hidden; /* Prevent overflow */
  position: relative;
  box-sizing: border-box;
}

/* Content blocks */
.content-block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--dia-space-9) var(--dia-space-11);
}

.content-text {
  color: #202124;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-base);
  line-height: var(--line-height-32);
  text-align: center;
  margin-bottom: var(--dia-space-9);
}

.content-highlight {
  font-weight: 700;
  color: #202124;
}

/* Testimonials section placeholder */
.testimonials-section {
  margin-top: var(--item-spacing-80);
}

/* Responsive testimonial columns */
.mobile-column {
  display: none;
}

/* Desktop/tablet layout (default) */
.testimonials-columns-wrapper {
  display: flex;
  justify-content: space-between;
}

/* Mobile layout */
@media (max-width: 767px) {
  .desktop-column {
    display: none;
  }
  
  .mobile-column {
    display: block;
    width: 80%;
    max-width: 320px;
    margin: 0 auto;
  }
  
  .testimonials-columns-wrapper {
    justify-content: center;
  }
}

.testimonials-container {
  margin-bottom: var(--item-spacing-120);
}

.testimonials-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--item-spacing-48);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--radius-lg);
  margin-top: var(--item-spacing-48);
}

.testimonials-note {
  font-family: var(--font-family-inter);
  font-size: var(--font-size-20);
  color: var(--color-grey-25);
  font-style: italic;
}

/* Responsive styles */
@media (max-width: 1000px) {
  .dorsey-title {
    font-size: var(--dia-hero-secondary-size);
    line-height: var(--dia-hero-secondary-line-height);
  }

  .dorsey-description p {
    font-size: var(--dia-section-heading-size);
    line-height: var(--dia-section-heading-line-height);
  }
}

@media (max-width: var(--breakpoint-tablet)) {
  .devices-section {
    padding: var(--item-spacing-80) 0;
  }
  
  .dorsey-title {
    font-size: var(--font-size-h1-tablet);
    line-height: calc(var(--font-size-h1-tablet) * 1.2);
  }
  
  .dorsey-description p {
    font-size: var(--font-size-h4-tablet);
    line-height: calc(var(--font-size-h4-tablet) * 1.2);
  }
}

@media (max-width: var(--breakpoint-mobile)) {
  .devices-section {
    padding: var(--item-spacing-60) 0;
  }
  
  .dorsey-title {
    font-size: var(--font-size-h1-mobile);
    line-height: calc(var(--font-size-h1-mobile) * 1.2);
  }
  
  .dorsey-description p {
    font-size: var(--font-size-h4-mobile);
    line-height: calc(var(--font-size-h4-mobile) * 1.2);
  }
}
