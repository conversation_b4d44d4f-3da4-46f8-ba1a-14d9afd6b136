import React from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import SecondaryScrollMenu from '../components/SecondaryScrollMenu';
import Hero from '../components/Hero';
import LargeTextSection from '../components/LargeTextSection';
import { RevealText } from '../components/TextRevealByWord';
import ProblemSection from '../components/ProblemSection';
import ROASvsPAXSection from '../components/ROASvsPAXSection';
import SolutionsSection from '../components/SolutionsSection';
import ProfitApproachSection from '../components/ProfitApproachSection';
import OurWorkSection from '../components/OurWorkSection';
import TestimonialsSection from '../components/TestimonialsSection';
import NextStepsSection from '../components/NextStepsSection';

const HomePage: React.FC = () => {
  return (
    <div className="app">
      <Header />
      <SecondaryScrollMenu />

      <main className="main-content">
        <section className="section section-light">
          <Hero />
        </section>

        <LargeTextSection>
          <h2>
            <RevealText content="**ROAS is a blunt instrument.** It hides margin bleed, ignores SKU economics, and often misleads your team. **PAX™ (Profit After eXpenditure)** is our north star. **It's how we know what's really working.**" />
          </h2>
        </LargeTextSection>

        <section id="problem" className="section section-gray">
          <ProblemSection />
        </section>

        <section id="roas-vs-pax" className="section section-light">
          <ROASvsPAXSection />
        </section>

        <section id="solutions" className="section section-light">
          <SolutionsSection />
        </section>

        <section id="profit-approach" className="section section-gray">
          <ProfitApproachSection />
        </section>

        <section id="our-work" className="section section-gray">
          <OurWorkSection />
        </section>

        <section id="testimonials" className="section section-light">
          <TestimonialsSection />
        </section>

        <section id="next-steps" className="section section-light">
          <NextStepsSection />
        </section>

        <section id="faq" className="section section-gray">
          <div className="container-base">
            <div className="text-center py-20">
              <h2 className="section-heading text-text-primary mb-6">
                Frequently Asked Questions
              </h2>
              <p className="body-text text-text-secondary max-w-2xl mx-auto">
                Get answers to common questions about our profit-first marketing approach.
              </p>
            </div>
          </div>
        </section>

        <section id="partnerships" className="section section-light">
          <div className="container-base">
            <div className="text-center py-20">
              <h2 className="section-heading text-text-primary mb-6">
                Partnerships
              </h2>
              <p className="body-text text-text-secondary max-w-2xl mx-auto">
                Strategic partnerships that amplify your growth potential.
              </p>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default HomePage;
