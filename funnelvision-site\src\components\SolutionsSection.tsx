import React from "react";
import FadeIn from "./animations/FadeIn";
import ServiceCarousel from "./ui/ServiceCarousel";

interface ServiceData {
  title: string;
  description: string;
  images: string[];
  delay: string;
}

const SolutionsSection: React.FC = () => {
  const services: ServiceData[] = [
    {
      title: "Google Ads",
      description: "High-Precision, High-Margin Campaigns",
      images: [
        '/images/services/google-ads.webp'
      ],
      delay: "delay-200"
    },
    {
      title: "YouTube Ads",
      description: "Build and scale YouTube without guesswork.",
      images: [
        '/images/services/youtube-ads-1.webp'
      ],
      delay: "delay-300"
    },
    {
      title: "AI Discovery",
      description: "Get ahead of the next shift in consumer search.",
      images: [
        '/images/services/ai-discovery.webp'
      ],
      delay: "delay-400"
    },
    {
      title: "Bing Expansion",
      description: "Quiet Profit Channel Most Brands Ignore",
      images: [
        '/images/services/microsoft-bing-ads.webp'
      ],
      delay: "delay-500"
    }
  ];

  return (
    <section
      id="solutions"
      className="solutions-section bg-background w-full max-w-full overflow-x-hidden"
      style={{ padding: 'var(--dia-space-12) 0' }}
    >
      <div className="dia-container-cards">
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
          <div
            className="text-center mx-auto"
            style={{
              marginBottom: 'var(--dia-space-12)',
              maxWidth: 'var(--width-843)'
            }}
          >
            <h2 className="section-subheading text-text-primary text-center">
              Every Campaign<br />
              Optimized for Profit.
            </h2>
            <div
              className="section-description text-center mx-auto"
              style={{ maxWidth: 'var(--width-616)' }}
            >
              <p className="body-text text-text-secondary">
                We're not a generalist agency. We run paid search as your CRO-led growth partner.
              </p>
            </div>
          </div>
        </FadeIn>

        {/* Services Carousels */}
        {services.map((service, index) => (
          <div
            key={service.title}
            className="solutions-carousel-section flex flex-col items-center w-full"
            style={{
              marginTop: index === 0 ? 'var(--dia-space-12)' : 'var(--dia-space-16)',
              marginBottom: 'var(--dia-space-10)'
            }}
          >
            <FadeIn threshold={0.1} rootMargin="0px" className={`fade-up ${service.delay}`}>
              <div
                className="text-center mx-auto"
                style={{
                  marginBottom: 'var(--dia-space-8)',
                  maxWidth: 'var(--width-616)'
                }}
              >
                <h2 className="section-heading text-text-primary text-center">
                  {service.title}
                </h2>
                <div
                  className="section-description text-center"
                  style={{ marginTop: 'var(--dia-space-4)' }}
                >
                  <p className="body-text text-text-secondary">
                    {service.description}
                  </p>
                </div>
              </div>
              <ServiceCarousel
                serviceName={service.title}
                images={service.images}
              />
            </FadeIn>
          </div>
        ))}
      </div>
    </section>
  );
};

export default SolutionsSection;
