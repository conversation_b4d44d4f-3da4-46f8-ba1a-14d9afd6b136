/* Assistance Devices Section styles */
.assistance-devices-section {
  padding: var(--item-spacing-80) 0;
  width: 100%;
  overflow: hidden;
}


/* Keep the existing devices header and description styling */
.devices-header {
  text-align: center;
  margin-bottom: var(--item-spacing-80);
}

.devices-title {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-80);
  font-weight: 700;
  line-height: var(--line-height-96);
  letter-spacing: var(--letter-spacing-tight);
  margin: 0 0 var(--item-spacing-36) 0;
  text-align: center;
}

.devices-description {
  max-width: var(--width-616);
  margin: 0 auto;
}

.devices-description p {
  color: var(--color-grey-63);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-28);
  font-weight: 400;
  line-height: var(--line-height-36);
  margin: 0;
  text-align: center;
}

/* Use capabilities-section classes for the Action Intelligence section */
.cta-container {
  display: flex;
  justify-content: center;
  margin-top: var(--item-spacing-24);
}

/* Responsive styles */
@media (max-width: 768px) {
  .devices-title {
    font-size: var(--font-size-36);
    line-height: var(--line-height-44);
  }
  
  .action-intelligence-title {
    font-size: clamp(3rem, 5%, 4rem); /* Use percentage instead of viewport width */
  }

  .devices-description p {
    font-size: var(--font-size-20);
    line-height: var(--line-height-28);
  }
}
