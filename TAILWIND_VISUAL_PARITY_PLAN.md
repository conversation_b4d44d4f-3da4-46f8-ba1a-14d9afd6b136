# FunnelVision Tailwind Migration - Visual Parity & Optimization Plan

This plan outlines how to verify and polish the migrated **TypeScript + Tailwind CSS** build in `funnelvision-site/` so it matches the original JavaScript version pixel‑for‑pixel.

## 1. Codebase Comparison

| Build | Path | Stack |
| ----- | ---- | ----- |
| **Original** | `src/` | React + CSS |
| **Migrated** | `funnelvision-site/src/` | React + TypeScript + Tailwind |

The new build already defines breakpoints and container/spacing tokens in `design-tokens.css`:

```css
--hero-title-mobile: 4.8rem;
--hero-title-tablet: 6.4rem;
--hero-title-desktop: 7.2rem;
--section-heading-mobile: 2.4rem;
--section-heading-tablet: 3.2rem;
--section-heading-desktop: 3.4rem;
--body-text-mobile: 1.4rem;
--body-text-tablet: 1.6rem;
--body-text-desktop: 2.0rem;
--container-max-width: 1296px;
--container-padding: 1.6rem;
--container-padding-mobile: 1.6rem;
--container-padding-tablet: 2.4rem;
--section-padding-mobile: 4.8rem;
--section-padding-tablet: 6.0rem;
--section-padding-desktop: 8.0rem;
```
【F:funnelvision-site/src/styles/design-tokens.css†L100-L139】

## 2. Visual Diff Table

| Component | JS Build Reference | Tailwind Build Reference | Discrepancy Notes |
| --------- | ----------------- | ----------------------- | ---------------- |
| Header | `src/components/Header.jsx` | `funnelvision-site/src/components/Header.tsx` | Check sticky behavior, mobile menu transitions, and link hover styles.
| Hero | `src/components/*` (original hero) | `funnelvision-site/src/components/Hero.tsx` | Ensure typography and CTA layout match exactly.
| LargeTextSection | `src/components/LargeTextSection.jsx` | `funnelvision-site/src/components/LargeTextSection.tsx` | Verify text reveal animation and spacing.
| ProblemSection | `src/components/ProblemSection.jsx` | `funnelvision-site/src/components/ProblemSection.tsx` | Confirm card grid uses identical column/gutter spacing.
| ROASvsPAXSection | `src/components/ROASvsPAXSection.jsx` | `funnelvision-site/src/components/ROASvsPAXSection.tsx` | Validate chart and caption sizes.
| SolutionsSection | `src/components/SolutionsSection.jsx` | `funnelvision-site/src/components/SolutionsSection.tsx` | Compare icon sizes and padding.
| ProfitApproachSection | `src/components/ProfitApproachSection.jsx` | `funnelvision-site/src/components/ProfitApproachSection.tsx` | Check alignment of image and text blocks.
| Footer | `src/components/Footer.jsx` | `funnelvision-site/src/components/Footer.tsx` | Review social icons and copyright notice.

For each component, capture screenshots at the specified breakpoints and flag any gap (margin, padding, font size, color, alignment). Use the `--section-padding-*` and typography tokens above to ensure parity.

## 3. Responsive Audit

The responsive system follows the 17‑breakpoint strategy documented in `DIABROWSER_RESPONSIVE_IMPLEMENTATION_REPORT.md`:

```text
#### 1. Progressive Container System (17 Breakpoints)
- 400px → 2000px in 100px increments
- Prevents content sprawl and keeps optimal reading width
#### 2. Enhanced Typography System (3-Tier)
- Tier 1 (400px‑799px): mobile typography
- Tier 2 (800px+): major typography jump for h1‑h3
- Tier 3 (1000px+): body text refinement
```
【F:DIABROWSER_RESPONSIVE_IMPLEMENTATION_REPORT.md†L7-L16】

Key container sizes:

```css
@media (min-width: 400px)  { .container { max-width: 400px; } }
@media (min-width: 600px)  { .container { max-width: 600px; } }
@media (min-width: 700px)  { .container { max-width: 700px; } }
@media (min-width: 800px)  { .container { max-width: 800px; } }
@media (min-width: 900px)  { .container { max-width: 900px; } }
@media (min-width: 1000px) { .container { max-width: 1000px; } }
@media (min-width: 1100px) { .container { max-width: 1100px; } }
@media (min-width: 1200px) { .container { max-width: 1200px; } }
@media (min-width: 1300px) { .container { max-width: 1300px; } }
@media (min-width: 1400px) { .container { max-width: 1400px; } }
@media (min-width: 1500px) { .container { max-width: 1500px; } }
@media (min-width: 1600px) { .container { max-width: 1600px; } }
@media (min-width: 1700px) { .container { max-width: 1700px; } }
@media (min-width: 1800px) { .container { max-width: 1800px; } }
@media (min-width: 1900px) { .container { max-width: 1900px; } }
@media (min-width: 2000px) { .container { max-width: 2000px; } }
```
【F:DIABROWSER_RESPONSIVE_IMPLEMENTATION_REPORT.md†L30-L48】

Audit layout at the following six bands: xs (<360px), sm (360‑599px), md (600‑904px), lg (905‑1239px), xl (1240‑1439px), xxl (≥1440px). For each breakpoint, ensure columns, gutters, and outer margins match the original design. Reference the "Visual Impact by Screen Size" checklist for expected behavior.

## 4. Container & Grid Plan

Use the container classes from `design-tokens.css`:

```css
.container-base {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}
.section-base {
  padding: var(--section-padding-mobile) 0;
}
@media (min-width: 800px) {
  .section-base { padding: var(--section-padding-tablet) 0; }
}
@media (min-width: 1000px) {
  .section-base { padding: var(--section-padding-desktop) 0; }
}
```
【F:funnelvision-site/src/styles/design-tokens.css†L244-L267】

Map these containers to the Material grid: 4 columns for `xs`/`sm`, 8 columns for `md`, and 12 columns for `lg`/`xl`/`xxl`. Gutters should follow the `--container-padding-*` tokens.

## 5. Spacing Standardization

Spacing is based on the 0.6 rem grid. The final migration report lists the change from the old 8 px grid:

```text
### Spacing Precision
- Base Grid: 8px → 9.6px (0.6rem base)
- Section Padding: 80px → 80px (5rem)
- Fine Spacing: 8px, 16px, 24px → 9.6px, 12.8px, 16px, 19.2px
- Granular Control: 15 spacing tokens vs 8 original
```
【F:DIABROWSER_MIGRATION_FINAL_REPORT.md†L32-L45】

Ensure all gaps and margins in Tailwind classes map to these token values (e.g., `gap-[2.4rem]` or `mt-[1.6rem]`). Use CSS variables via Tailwind config if needed.

## 6. Implementation Strategy

1. **Visual Snapshot Testing** – Capture screenshots of each component in the original JS build. Compare with the new build at every breakpoint using a diff tool.
2. **Token Audit** – Verify all typography, color, spacing, and container tokens in `design-tokens.css` align with those defined in `src/styles/funnelvision.css`. Any missing tokens should be added to the Tailwind config.
3. **Component Review** – Walk through each component in the Visual Diff Table. For discrepancies, update the Tailwind classes or create custom utilities referencing the tokens.
4. **Responsive Verification** – Follow the responsive guidelines in the responsive implementation report to ensure layout, font-size jumps, and navigation behavior are identical.
5. **Performance Check** – Keep CSS bundle small; remove unused classes. Confirm animations and interactivity match (Hero star rating, menu transitions, etc.).
6. **Sign‑off Matrix** – For each component and breakpoint, mark status as:
   - ✅ Pixel perfect
   - ⚠️ Needs adjustment (list issue)
   - ❌ Missing feature

## 7. Rollout Recommendation

Because all core systems are already defined, a **single‑pass update** should suffice. Start from the header and hero, then move through each section. Total effort estimate: **2‑3 days** for thorough review and fixes.

Following this plan will deliver a pixel‑perfect TypeScript + Tailwind build that mirrors the original JavaScript site while benefiting from the new token system and responsive architecture.
