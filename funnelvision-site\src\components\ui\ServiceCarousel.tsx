import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ServiceCarouselProps {
  images?: string[];
  duration?: number;
  serviceName?: string;
}

/**
 * ServiceCarousel component - Animated carousel with timer-based navigation
 */
const ServiceCarousel: React.FC<ServiceCarouselProps> = ({
  images = [
    '/images/services/google-ads.webp'
  ],
  duration = 10000,
  serviceName = "Service"
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [progress, setProgress] = useState(0);

  // Auto-advance slides
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % images.length);
      setProgress(0);
    }, duration);

    return () => clearInterval(interval);
  }, [currentSlide, isPlaying, duration, images.length]);

  // Progress animation
  useEffect(() => {
    if (!isPlaying) return;

    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          return 0;
        }
        return prev + (100 / (duration / 100));
      });
    }, 100);

    return () => clearInterval(progressInterval);
  }, [currentSlide, isPlaying, duration]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    setProgress(0);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % images.length);
    setProgress(0);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + images.length) % images.length);
    setProgress(0);
  };

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      {/* Navigation Controls */}
      <div className="flex items-center justify-center gap-4 mb-8">
        <button
          onClick={prevSlide}
          className="p-2 rounded-full bg-background-elevated hover:bg-gray-200 transition-colors"
          aria-label="Previous slide"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="15,18 9,12 15,6"></polyline>
          </svg>
        </button>

        {/* Progress Indicators */}
        <div className="flex items-center gap-2">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className="relative w-8 h-2 bg-gray-200 rounded-full overflow-hidden"
              aria-label={`Go to slide ${index + 1}`}
            >
              {index === currentSlide && (
                <motion.div
                  className="absolute top-0 left-0 h-full bg-primary rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.1 }}
                />
              )}
              {index < currentSlide && (
                <div className="absolute top-0 left-0 w-full h-full bg-primary rounded-full" />
              )}
            </button>
          ))}
        </div>

        <button
          onClick={nextSlide}
          className="p-2 rounded-full bg-background-elevated hover:bg-gray-200 transition-colors"
          aria-label="Next slide"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        </button>
      </div>

      {/* Image Container */}
      <div className="relative w-full h-96 bg-background-elevated rounded-standard overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.img
            key={currentSlide}
            src={images[currentSlide]}
            alt={`${serviceName} slide ${currentSlide + 1}`}
            className="absolute inset-0 w-full h-full object-cover"
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.5 }}
            onMouseEnter={() => setIsPlaying(false)}
            onMouseLeave={() => setIsPlaying(true)}
          />
        </AnimatePresence>
      </div>

      {/* Slide Counter */}
      <div className="text-center mt-4">
        <span className="text-sm text-text-muted">
          {currentSlide + 1} / {images.length}
        </span>
      </div>
    </div>
  );
};

export default ServiceCarousel;
