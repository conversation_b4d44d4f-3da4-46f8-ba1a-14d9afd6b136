/* Scale-in animation styles */
.gemini-video-block {
  position: relative;
  transition: transform 0.3s cubic-bezier(0.19, 1, 0.22, 1),
              opacity 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: transform, opacity;
  transform-origin: center bottom;
  background: none;
  border: none;
  box-shadow: none;
}

/* Base style for all cards that will use the hover effect */
.video-card {
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 0;
}

/* Hover outline effect */
.video-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: #1d3c2a;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 1;
}

/* Use proper masking technique for outline effect */
.video-card::before {
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Make outline visible on hover */
.gemini-video-block:hover .video-card::before {
  opacity: 1;
}

/* Fallback for browsers that don't support masking */
@supports not (mask-composite: exclude) {
  .video-card::before {
    border: 2px solid rgba(59, 107, 255, 0.8);
    background: none;
  }
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .gemini-video-block {
    transition: opacity 0.1s ease-in-out;
    transform: none !important;
  }
}
