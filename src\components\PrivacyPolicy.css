.privacy-policy-page {
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  min-height: 100vh;
  padding-top: var(--space-8); /* Account for header */
}

/* Diabrowser migration: when .dia-typography is applied */
.privacy-policy-page.dia-typography {
  background-color: var(--dia-color-background);
  color: var(--dia-color-text-primary);
  padding-top: var(--dia-space-11); /* Account for header */
}

/* Header Section */
.privacy-header {
  background-color: var(--color-background-primary);
  padding: var(--space-10) 0 var(--space-8) 0;
  border-bottom: 1px solid var(--color-border-light);
}

/* Diabrowser migration overrides */
.privacy-policy-page.dia-typography .privacy-header {
  background-color: var(--dia-color-background);
  padding: var(--dia-space-14) 0 var(--dia-space-12) 0;
  border-bottom: 1px solid var(--dia-color-text-light);
}

.privacy-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.privacy-meta {
  margin-top: var(--space-4);
}

.privacy-last-updated {
  color: var(--color-text-muted);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.privacy-navigation {
  margin-top: var(--space-4);
}

.back-to-home-link {
  display: inline-flex;
  align-items: center;
  color: var(--color-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--transition-base);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border-light);
  background-color: var(--color-background-primary);
}

.back-to-home-link:hover {
  color: var(--color-primary-hover);
  background-color: var(--color-primary-light);
}

/* Content Section */
.privacy-content {
  padding: var(--space-10) 0;
}

.privacy-content-wrapper {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.privacy-section {
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--color-border-light);
}

.privacy-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

/* Typography */
.privacy-text {
  margin-top: var(--space-4);
}

.privacy-text p {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-loose);
  letter-spacing: var(--letter-spacing-normal);
  margin: 0 0 var(--space-3) 0;
}

.privacy-text p:last-child {
  margin-bottom: 0;
}

.privacy-text ul {
  margin: var(--space-3) 0;
  padding-left: var(--space-4);
  list-style-type: disc;
}

.privacy-text li {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-loose);
  letter-spacing: var(--letter-spacing-normal);
  margin-bottom: var(--space-2);
}

.privacy-text li:last-child {
  margin-bottom: 0;
}

/* Contact Info */
.contact-info {
  background-color: var(--color-background-secondary);
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  margin: var(--space-4) 0;
}

.contact-info p {
  margin: 0 0 var(--space-1) 0;
}

.contact-info p:last-child {
  margin-bottom: 0;
}

.contact-info strong {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

/* Responsive Design */
@media (max-width: var(--breakpoint-tablet)) {
  .privacy-policy-page {
    padding-top: var(--space-6);
  }

  .privacy-header {
    padding: var(--space-8) 0 var(--space-6) 0;
  }

  .privacy-content {
    padding: var(--space-8) 0;
  }

  .privacy-content-wrapper {
    gap: var(--space-6);
  }

  .privacy-section {
    padding-bottom: var(--space-4);
  }

  .privacy-text {
    margin-top: var(--space-3);
  }

  .privacy-text p {
    font-size: var(--font-size-base);
    line-height: var(--line-height-extra-loose);
  }

  .privacy-text li {
    font-size: var(--font-size-base);
    line-height: var(--line-height-extra-loose);
  }
}

@media (max-width: var(--breakpoint-mobile)) {
  .privacy-policy-page {
    padding-top: var(--space-4);
  }

  .privacy-header {
    padding: var(--space-6) 0 var(--space-4) 0;
  }

  .privacy-content {
    padding: var(--space-6) 0;
  }

  .privacy-content-wrapper {
    gap: var(--space-4);
  }

  .privacy-section {
    padding-bottom: var(--space-3);
  }

  .privacy-text {
    margin-top: var(--space-2);
  }

  .privacy-text ul {
    padding-left: var(--space-3);
  }

  .contact-info {
    padding: var(--space-3);
  }
}
