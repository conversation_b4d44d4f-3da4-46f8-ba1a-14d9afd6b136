/* Microsoft Ads Modal Styles */
.microsoft-ads-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: var(--dia-space-6);
  animation: fadeIn 0.3s ease-out;
}

.microsoft-ads-modal-panel {
  position: relative;
  background-color: var(--dia-color-background-light); /* #F9F6F2 */
  border-radius: 16px; /* Site standard border radius */
  box-shadow: var(--shadow-2xl);
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  overflow: hidden;
  animation: slideIn 0.4s ease-out;
}

/* Overview Section (Left/Top) */
.microsoft-ads-modal-overview {
  flex: 1;
  padding: var(--dia-space-12); /* 50px */
  overflow-y: auto;
  display: flex;
  align-items: center;
}

.microsoft-ads-modal-content {
  width: 100%;
  max-width: 500px;
}

.microsoft-ads-modal-title {
  font-size: var(--dia-font-size-3xl); /* 26px */
  font-weight: var(--dia-font-weight-semibold); /* 600 */
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-4) 0; /* 12px bottom margin */
  line-height: 1.2;
}

.microsoft-ads-modal-subtitle {
  font-size: var(--dia-font-size-xl); /* 20px */
  font-weight: var(--dia-font-weight-medium); /* 500 */
  color: var(--dia-color-text-secondary);
  margin: 0 0 var(--dia-space-8) 0; /* 20px bottom margin */
  line-height: 1.3;
}

.microsoft-ads-modal-description {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-6); /* 16px between paragraphs */
}

.microsoft-ads-modal-description p {
  font-size: var(--dia-font-size-base); /* 16px */
  font-weight: var(--dia-font-weight-normal); /* 400 */
  color: var(--dia-color-text-primary);
  line-height: 1.6;
  margin: 0;
}

/* Features Section (Right/Bottom) */
.microsoft-ads-modal-features {
  flex: 1;
  padding: var(--dia-space-12); /* 50px */
  background-color: var(--dia-color-background); /* Slightly different background */
  overflow-y: auto;
  display: flex;
  align-items: center;
}

.microsoft-ads-modal-features-title {
  font-size: var(--dia-font-size-2xl); /* 22px */
  font-weight: var(--dia-font-weight-semibold); /* 600 */
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-8) 0; /* 20px bottom margin */
  line-height: 1.2;
}

.microsoft-ads-modal-features-list {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-6); /* 16px between items */
}

.microsoft-ads-modal-feature-item {
  display: flex;
  align-items: center;
  gap: var(--dia-space-4); /* 12px between checkmark and text */
  font-size: var(--dia-font-size-base); /* 16px */
  font-weight: var(--dia-font-weight-normal); /* 400 */
  color: var(--dia-color-text-primary);
  line-height: 1.5;
}

.microsoft-ads-modal-checkmark {
  color: var(--dia-color-teal-green); /* Brand accent color */
  font-weight: var(--dia-font-weight-semibold); /* 600 */
  font-size: var(--dia-font-size-lg); /* 18px */
  flex-shrink: 0;
}

/* Close Button */
.microsoft-ads-modal-close {
  position: absolute;
  bottom: var(--dia-space-6); /* 16px from bottom */
  right: var(--dia-space-6); /* 16px from right */
  width: 48px;
  height: 48px;
  background-color: var(--dia-color-text-primary);
  color: var(--dia-color-background);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-base);
  z-index: 10;
}

.microsoft-ads-modal-close:hover {
  background-color: var(--dia-color-text-secondary);
  transform: scale(1.05);
}

.microsoft-ads-modal-close:focus-visible {
  outline: 2px solid var(--dia-color-text-primary);
  outline-offset: 2px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .microsoft-ads-modal-backdrop {
    padding: var(--dia-space-4);
  }

  .microsoft-ads-modal-panel {
    flex-direction: column;
    max-height: 95vh;
  }

  .microsoft-ads-modal-overview,
  .microsoft-ads-modal-features {
    padding: var(--dia-space-8); /* 20px on mobile */
  }

  .microsoft-ads-modal-title {
    font-size: var(--dia-font-size-2xl); /* 22px on mobile */
  }

  .microsoft-ads-modal-subtitle {
    font-size: var(--dia-font-size-lg); /* 18px on mobile */
    margin-bottom: var(--dia-space-6); /* 16px bottom margin */
  }

  .microsoft-ads-modal-features-title {
    font-size: var(--dia-font-size-xl); /* 20px on mobile */
  }

  .microsoft-ads-modal-description {
    gap: var(--dia-space-4); /* 12px between paragraphs on mobile */
  }

  .microsoft-ads-modal-features-list {
    gap: var(--dia-space-4); /* 12px between items on mobile */
  }

  .microsoft-ads-modal-feature-item {
    gap: var(--dia-space-3); /* 10px between checkmark and text on mobile */
    font-size: var(--dia-font-size-sm); /* 14px on mobile */
  }

  .microsoft-ads-modal-checkmark {
    font-size: var(--dia-font-size-base); /* 16px on mobile */
  }

  .microsoft-ads-modal-close {
    width: 40px;
    height: 40px;
    bottom: var(--dia-space-4); /* 12px from bottom on mobile */
    right: var(--dia-space-4); /* 12px from right on mobile */
  }
}
