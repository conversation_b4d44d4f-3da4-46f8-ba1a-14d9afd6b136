import React from "react";
import FadeIn from "./animations/FadeIn";
import ProblemComparison from "./ProblemComparison";

const ROASvsPAXSection: React.FC = () => {
  return (
    <section
      id="roas-vs-pax"
      className="bg-background w-full relative overflow-hidden"
      style={{ padding: 'var(--dia-space-14) 0' }}
    >
      <div
        className="w-full mx-auto box-border"
        style={{
          maxWidth: 'var(--width-1070)',
          padding: '0 var(--dia-space-6)'
        }}
      >
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
          <div className="flex justify-center items-stretch w-full relative">
            <ProblemComparison />
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default ROASvsPAXSection;
