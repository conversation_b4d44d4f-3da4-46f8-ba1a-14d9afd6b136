.enhancing-gemini-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 24px;
  position: relative;
  margin: var(--dia-space-14) 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
}

.enhancing-gemini-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 24px;
  position: relative;
  width: 100%;
  max-width: 1296px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
  overflow-x: hidden;
}

/* Main content wrapper for video and text overlay */
.enhancing-gemini-main-content {
  position: relative;
  width: 100%;
  margin: var(--dia-space-12) auto;
}

/* Text overlay positioned on top of video */
.enhancing-gemini-text-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10; /* Ensure text is above video */
  padding: 0 20px;
  box-sizing: border-box;
  text-align: center;
}

.capabilities-video.enhancing-video {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--dia-space-14);
  padding: 0 clamp(16px, 5%, 113px);
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.enhancing-gemini-content-wrapper {
  display: flex;
  width: 843px;
  height: 320px;
  max-width: 843px;
  padding: 36px 0;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  position: relative;
}

.enhancing-gemini-text-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
}

.enhancing-gemini-heading {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
  position: relative;
}

.enhancing-gemini-title {
  color: white;
  text-align: center;
  font-family: var(--font-family-inter);
  font-size: var(--dia-section-heading-size); /* Use diabrowser h2 size */
  font-style: normal;
  font-weight: var(--dia-section-heading-weight); /* Use diabrowser h2 weight (300) */
  line-height: var(--dia-section-heading-line-height); /* Use diabrowser h2 line height */
  letter-spacing: var(--dia-section-heading-spacing);
  margin: 0 0 var(--dia-space-6) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}

.enhancing-gemini-description {
  color: white;
  text-align: center;
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size); /* Use diabrowser body text size */
  font-style: normal;
  font-weight: var(--dia-body-text-weight); /* Use diabrowser body text weight */
  line-height: var(--dia-body-text-line-height); /* Use diabrowser body text line height */
  letter-spacing: var(--dia-body-text-spacing);
  margin: 0;
  max-width: 600px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}

/* Old .container and .enhancing-gemini-video styles for standalone video are removed */

/* Video card styling - Simple solid card with play button */
.enhancing-video-card {
  max-width: 100%;
  height: 601px;
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.enhancing-video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-primary);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.play-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
}

.play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 1024px) {
  .enhancing-gemini-container {
    padding: 120px 24px 24px 24px;
  }

  .enhancing-gemini-title {
    font-size: var(--dia-section-heading-size); /* Use diabrowser h2 size */
  }
}

@media (max-width: 768px) {
  .enhancing-gemini-section {
    margin: var(--dia-space-12) 0;
  }

  .enhancing-gemini-container {
    padding: 80px 16px 16px 16px;
  }

  .enhancing-gemini-content {
    gap: var(--dia-space-12);
  }

  .enhancing-gemini-title {
    font-size: var(--dia-section-heading-size); /* Use diabrowser h2 size */
  }

  .enhancing-gemini-description {
    font-size: var(--dia-body-text-size); /* Use diabrowser body text */
  }

  .enhancing-gemini-cta {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .enhancing-gemini-title {
    font-size: var(--dia-section-heading-size); /* Use diabrowser h2 size */
  }
}

/* Pre-Logos Message */
.pre-logos-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1200px;
  margin: var(--dia-space-12) auto 20px;
  padding: 0 var(--dia-space-6);
  text-align: center;
}

.pre-logos-text {
  color: var(--color-text-primary);
  font-family: var(--font-family-inter);
  font-size: 22px;
  font-weight: 500;
  line-height: 32px;
  margin-bottom: var(--dia-space-9);
}

/* Client Logo Section */
.client-logos-section {
  width: 100%;
  padding: 20px 0 20px;
  overflow: hidden;
  background-color: var(--color-background);
}

/* Logo carousel content wrapper */
.logo-carousel-content-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
}

/* Logo carousel header */
.logo-carousel-header {
  text-align: center;
  margin-bottom: var(--dia-space-12);
}

.client-logos-title {
  font-family: var(--font-family-inter);
  font-size: 32px;
  font-weight: 600;
  line-height: 40px;
  text-align: center;
  letter-spacing: -0.015em; /* Adjusted for Inter font metrics */
  color: var(--color-text-primary, #111827);
  margin-bottom: 16px;
}

.client-logos-subtitle {
  font-family: var(--font-family-inter);
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  text-align: center;
  color: var(--color-text-secondary, #6B7280);
  max-width: 600px;
  margin: 0 auto;
}

/* Logo carousel styling */
.client-logos-section .logo-carousel-container {
  margin: 40px 0;
}

/* Add space between carousels */
.client-logos-section .logo-carousel-container + .logo-carousel-container {
  margin-top: 24px;
}

/* Style overrides for logos to match previous styling */
.client-logos-section .logo-svg,
.client-logos-section .logo-item img {
  filter: grayscale(100%);
  opacity: 0.7;
  transition: filter 0.3s ease, opacity 0.3s ease;
  max-height: 56px;
  width: auto;
}

.client-logos-section .logo-item:hover .logo-svg,
.client-logos-section .logo-item:hover img {
  filter: grayscale(0%);
  opacity: 1;
}

.logo-carousel {
  display: flex;
  gap: 140px; /* Much larger gap between logos */
  align-items: center;
  padding: var(--dia-space-2) 0;
  width: fit-content; /* Important for infinite loop */
}

.logo-item {
  flex: 0 0 auto;
  height: 45px;
  width: auto; /* Allow natural width */
  padding: 0 var(--dia-space-6); /* Add padding instead of fixed width */
  display: flex;
  align-items: center;
  justify-content: center;
}

.client-logo {
  max-width: 110px;
  max-height: 40px;
  object-fit: contain;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  filter: grayscale(100%);
}

.client-logo:hover {
  opacity: 1;
  filter: grayscale(0%);
}

/* Responsive media queries for the client logos section */
@media (max-width: 1200px) {
  .client-logos-title {
    font-size: 28px;
    line-height: 36px;
  }
  
  .client-logos-subtitle {
    font-size: 16px;
    line-height: 26px;
  }
}

@media (max-width: 768px) {
  .client-logos-section {
    padding: var(--dia-space-12) 0 var(--dia-space-9);
  }

  .client-logos-title {
    font-size: 26px;
    line-height: 34px;
    letter-spacing: -0.01em; /* Adjust for Inter font at smaller sizes */
  }
  
  .client-logos-subtitle {
    font-size: 15px;
    line-height: 24px;
    max-width: 500px;
  }
  
  .logo-carousel-header {
    margin-bottom: 32px;
  }
  
  .client-logos-section .logo-carousel-container {
    margin: 24px 0;
  }
}

@media (max-width: 480px) {
  .client-logos-section {
    padding: var(--dia-space-9) 0 var(--dia-space-6);
  }

  .client-logos-title {
    font-size: 24px;
    line-height: 32px;
  }
  
  .client-logos-subtitle {
    font-size: 14px;
    line-height: 22px;
  }
  
  .logo-carousel-content-wrapper {
    padding: 0 16px;
  }
  
  .logo-carousel-header {
    margin-bottom: 24px;
  }
}

/* This ensures we always see the right number of logos on screen */
.logo-carousel-container {
  /* Fixed container width based on visible logos - matching width in image */
  width: 100%;
  max-width: 1180px;
  margin-left: auto;
  margin-right: auto;
}
