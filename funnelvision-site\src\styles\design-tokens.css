/* 
 * FunnelVision Design System - Design Tokens
 * Consolidated design tokens for TypeScript + Tailwind implementation
 * Based on diabrowser.com's design principles and scaling approach
 * 
 * These tokens complement Tailwind's theme configuration and provide
 * CSS custom properties for complex component styling
 */

:root {
  /*
   * DIABROWSER TYPOGRAPHY SCALE
   * Based on diabrowser.com's exact rem values and scaling approach
   */

  /* Base Font Sizes - CORRECTED TO EXACT PIXEL MATCH */
  --dia-font-size-xs: 0.8125rem;  /* 13px - EXACT match with original */
  --dia-font-size-sm: 0.875rem;   /* 14px - EXACT match with original */
  --dia-font-size-base: 1rem;     /* 16px - EXACT match with original */
  --dia-font-size-lg: 1.125rem;   /* 18px - EXACT match with original */
  --dia-font-size-xl: 1.25rem;    /* 20px - EXACT match with original */
  --dia-font-size-2xl: 1.375rem;  /* 22px - EXACT match with original */
  --dia-font-size-3xl: 1.625rem;  /* 26px - EXACT match with original */
  --dia-font-size-4xl: 2rem;      /* 32px - EXACT match with original */
  --dia-font-size-5xl: 3rem;      /* 48px - EXACT match with original */
  --dia-font-size-6xl: 4rem;      /* 64px - EXACT match with original */
  --dia-font-size-7xl: 4.5rem;    /* 72px - EXACT match with original */
  --dia-font-size-8xl: 5.25rem;   /* 84px - EXACT match with original */

  /*
   * DIABROWSER FONT WEIGHTS
   * Much lighter than current system
   */
  --dia-font-weight-light: 350;    /* For headings - diabrowser's approach */
  --dia-font-weight-normal: 400;   /* For body text */
  --dia-font-weight-medium: 500;   /* Rarely used */
  --dia-font-weight-semibold: 600; /* h3/h4 headings */

  /*
   * DIABROWSER SPACING GRID - CORRECTED TO EXACT PIXEL MATCH
   * Converted from original 1rem=10px system to 1rem=16px system
   * Each value now matches original pixel dimensions exactly
   */
  --dia-space-1: 0.375rem;    /* 6px - EXACT match with original */
  --dia-space-2: 0.5rem;      /* 8px - EXACT match with original */
  --dia-space-3: 0.625rem;    /* 10px - EXACT match with original */
  --dia-space-4: 0.75rem;     /* 12px - EXACT match with original */
  --dia-space-5: 0.875rem;    /* 14px - EXACT match with original */
  --dia-space-6: 1rem;        /* 16px - EXACT match with original */
  --dia-space-7: 1.125rem;    /* 18px - EXACT match with original */
  --dia-space-8: 1.25rem;     /* 20px - EXACT match with original */
  --dia-space-9: 1.5rem;      /* 24px - EXACT match with original */
  --dia-space-10: 1.875rem;   /* 30px - EXACT match with original */
  --dia-space-11: 2.5rem;     /* 40px - EXACT match with original */
  --dia-space-12: 3.125rem;   /* 50px - EXACT match with original */
  --dia-space-13: 3.75rem;    /* 60px - EXACT match with original */
  --dia-space-14: 5rem;       /* 80px - EXACT match with original */
  --dia-space-15: 6.25rem;    /* 100px - EXACT match with original */
  --dia-space-16: 10rem;      /* 160px - EXACT match with original */
  --dia-space-18: 11.25rem;   /* 180px - EXACT match with original */

  /*
   * DIABROWSER COLOR SYSTEM
   * Monochromatic approach with white cards as default
   */
  --dia-color-black: #202124;
  --dia-color-white: #FFFFFF;         /* Pure white for cards */
  --dia-color-grey: #D9D9D9;
  --dia-color-dark-grey: #f2f2f3;

  /* Text Colors - Optimized for light mode comfort */
  --dia-color-text-primary: #202124;        /* Dark gray instead of harsh black */
  --dia-color-text-secondary: #202124;      /* Medium gray for comfortable reading */
  --dia-color-text-highlight: #96969e;          /* Lighter gray for important content on cards */
  --dia-color-text-muted: hsl(0, 0%, 55%);          /* Lighter gray for less important content */
  --dia-color-text-light: #96969e;          /* Very light gray for subtle text */

  /* Background Colors - Optimized light mode hierarchy */
  --dia-color-background: #FFFFFF;                   /* Site background - clean off-white */
  --dia-color-background-light: #F9F6F2;             /* Light background - subtle variation */
  --dia-color-background-overlay: rgba(0, 0, 0, 0.08); /* bg-black/8 */
  --dia-color-header-background: #FAFAFA;             /* Header - slightly elevated off-white */
  --dia-color-overlay-background: #F5F5F5;           /* Overlay background - subtle variation */
  --dia-color-cta-background-primary: hsl(0, 0%, 15%); /* CTA background - comfortable dark gray */
  --dia-color-cta-background-secondary: #F5F5F5;     /* Secondary CTA background */

  /* Card Colors - Enhanced white-based light mode system */
  --dia-color-card-background: #EFEAE5;             /* Default card background - pure white (perfect) */
  --dia-color-card-background-alt: #D6DBD9;         /* Alternative card background - matches site (perfect) */
  --dia-color-card-border: 0 solid #F5F3EF;         /* Softer border that blends better */
  --dia-color-card-highlight: hsl(0, 0%, 100%);     /* Pure white for top highlights */
  --dia-color-card-shadow-light: 0 solid #f2f2f3; /* Light shadow for depth */

  /* Enhanced Light Mode Card System */
  --dia-color-card-border-hover: 0 solid #f2f2f3;   /* Slightly darker border on hover */
  --dia-color-card-background-elevated: 0 solid #f2f2f3;    /* Slightly elevated card background */
  --dia-color-card-background-pressed: #FDFDFD;     /* Pressed/active card background */

  /* Accent Colors (Minimal Usage) */
  --dia-color-teal-green: #1b3d3c;                           /* Brand accent */

  /*
   * DIABROWSER BREAKPOINTS
   * 17-breakpoint system for progressive container sizing
   */
  --dia-breakpoint-micro: 400px;       /* Micro mobile */
  --dia-breakpoint-small: 600px;       /* Small mobile */
  --dia-breakpoint-large-mobile: 700px; /* Large mobile */
  --dia-breakpoint-tablet: 800px;      /* Typography major jump */
  --dia-breakpoint-tablet-large: 900px; /* Large tablet */
  --dia-breakpoint-desktop: 1000px;    /* Body text refinement */
  --dia-breakpoint-desktop-medium: 1100px; /* Medium desktop */
  --dia-breakpoint-desktop-large: 1200px;  /* Large desktop */
  --dia-breakpoint-xl: 1300px;         /* XL desktop */
  --dia-breakpoint-xxl: 1400px;        /* XXL desktop */
  --dia-breakpoint-ultra: 1500px;      /* Ultra desktop */
  --dia-breakpoint-wide: 1600px;       /* Wide desktop */
  --dia-breakpoint-super: 1700px;      /* Super wide */
  --dia-breakpoint-mega: 1800px;       /* Mega wide */
  --dia-breakpoint-giga: 1900px;       /* Giga wide */
  --dia-breakpoint-max: 2000px;        /* Maximum width */

  /*
   * COMPONENT-SPECIFIC TOKENS
   * Typography tokens for specific UI components
   */

  /* Bento Cards - CORRECTED TO EXACT PIXEL MATCH */
  --bento-title-size: 1.125rem;      /* 18px - EXACT match */
  --bento-title-weight: 600;
  --bento-title-line-height: 1.3;
  --bento-description-size: 1rem;    /* 16px - EXACT match */
  --bento-description-weight: 400;
  --bento-description-line-height: 1.5;

  /* Carousel Cards - CORRECTED TO EXACT PIXEL MATCH */
  --card-tag-size: 0.8125rem;        /* 13px - EXACT match */
  --card-tag-weight: 500;
  --card-tag-line-height: 1.2;
  --card-title-size: 1.125rem;       /* 18px - EXACT match */
  --card-title-weight: 500;
  --card-title-line-height: 1.3;
  --card-content-size: 0.875rem;     /* 14px - EXACT match */
  --card-content-weight: 400;
  --card-content-line-height: 1.5;
  --card-subtitle-size: 1rem;        /* 16px - EXACT match */
  --card-subtitle-weight: 500;
  --card-subtitle-line-height: 1.4;

  /* Testimonial Cards - CORRECTED TO EXACT PIXEL MATCH */
  --testimonial-text-size: 0.875rem; /* 14px - EXACT match */
  --testimonial-text-weight: 400;
  --testimonial-text-line-height: 1.5;
  --testimonial-name-size: 1rem;     /* 16px - EXACT match */
  --testimonial-name-weight: 500;
  --testimonial-name-line-height: 1.3;
  --testimonial-role-size: 0.875rem; /* 14px - EXACT match */
  --testimonial-role-weight: 400;
  --testimonial-role-line-height: 1.4;

  /* Navigation Elements - CORRECTED TO EXACT PIXEL MATCH */
  --nav-link-size: 1rem;             /* 16px - EXACT match */
  --nav-link-weight: 400;
  --nav-link-line-height: 1.5;
  --nav-link-active-weight: 600;
  --nav-brand-size: 1.125rem;        /* 18px - EXACT match */
  --nav-brand-weight: 500;
  --nav-brand-line-height: 1.2;
  --secondary-nav-link-size: 0.875rem; /* 14px - EXACT match */

  /* Button Text - CORRECTED TO EXACT PIXEL MATCH */
  --btn-primary-size: 1rem;          /* 16px - EXACT match */
  --btn-primary-weight: 500;
  --btn-primary-line-height: 1.2;
  --btn-secondary-size: 1rem;        /* 16px - EXACT match */
  --btn-secondary-weight: 500;
  --btn-secondary-line-height: 1.2;
  --btn-cta-size: 1rem;              /* 16px - EXACT match */
  --btn-cta-weight: 500;
  --btn-cta-line-height: 1.2;

  /* Footer Elements - CORRECTED TO EXACT PIXEL MATCH */
  --footer-title-size: 1.5rem;       /* 24px - EXACT match */
  --footer-title-weight: 400;
  --footer-title-line-height: 1.3;
  --footer-link-size: 0.875rem;      /* 14px - EXACT match */
  --footer-link-weight: 400;
  --footer-link-line-height: 1.5;
  --footer-copyright-size: 0.8125rem; /* 13px - EXACT match */
  --footer-copyright-weight: 400;
  --footer-copyright-line-height: 1.4;

  /* Form Elements - CORRECTED TO EXACT PIXEL MATCH */
  --form-label-size: 0.875rem;       /* 14px - EXACT match */
  --form-label-weight: 500;
  --form-label-line-height: 1.4;
  --form-input-size: 1rem;           /* 16px - EXACT match */
  --form-input-weight: 400;
  --form-input-line-height: 1.5;

  /* 
   * LIGHT MODE HIGHLIGHT SYSTEM
   * Overhead lighting simulation for elevated UI elements
   */
  --highlight-subtle: rgba(255, 255, 255, 0.6);
  --highlight-medium: rgba(255, 255, 255, 0.8);
  --highlight-strong: rgba(255, 255, 255, 1.0);
  --highlight-top-border: 1px solid var(--highlight-medium);

  /*
   * DIABROWSER RESPONSIVE TYPOGRAPHY SYSTEM
   * Diabrowser breakpoints: 800px and 1000px
   */

  /* Hero Typography - Responsive scaling */
  --dia-hero-title-size: var(--dia-font-size-5xl);      /* 48px mobile, 72px desktop */
  --dia-hero-title-weight: var(--dia-font-weight-light); /* 350 - diabrowser approach */
  --dia-hero-title-line-height: 1.1;                    /* Tight line height for impact */
  --dia-hero-title-spacing: -0.04em;                    /* Tight letter spacing */
  --dia-hero-secondary-size: var(--dia-font-size-4xl);  /* 32px mobile, 64px desktop */
  --dia-hero-description-size: var(--dia-font-size-lg); /* 18px mobile, 20px desktop */
  --dia-hero-description-weight: var(--dia-font-weight-normal); /* 400 */
  --dia-hero-description-line-height: 1.5;              /* Comfortable reading */
  --dia-hero-description-spacing: -0.02em;              /* Subtle letter spacing */

  /* Section Typography - Responsive scaling */
  --dia-section-heading-size: var(--dia-font-size-2xl); /* 22px mobile, 34px desktop */
  --dia-section-description-size: var(--dia-font-size-base); /* 16px mobile, 20px desktop */
  --dia-section-subheading-size: var(--dia-font-size-4xl); /* 32px - large text sections */
  --dia-font-size-subheading-mobile: var(--dia-font-size-2xl); /* 22px mobile */
  --dia-hero-secondary-weight: var(--dia-font-weight-light); /* 350 */
  --dia-hero-secondary-line-height: 1.2; /* Comfortable for large text */

  /* Body Typography - Responsive scaling */
  --dia-body-text-size: var(--dia-font-size-sm);        /* 14px mobile, 20px desktop */
  --dia-body-text-large-size: var(--dia-font-size-base); /* 16px mobile, 20px desktop */

  /* Navigation Typography */
  --dia-nav-link-size: var(--dia-font-size-base);       /* 16px */
  --dia-nav-brand-size: var(--dia-font-size-lg);        /* 18px */

  /* Button Typography */
  --dia-button-text-size: var(--dia-font-size-base);    /* 16px */
  --dia-button-text-large-size: var(--dia-font-size-lg); /* 18px */

  /* Card Typography */
  --dia-card-title-size: var(--dia-font-size-lg);       /* 18px */
  --dia-card-description-size: var(--dia-font-size-sm); /* 14px */
  --dia-card-meta-size: var(--dia-font-size-xs);        /* 13px */

  /* Footer Typography */
  --dia-footer-title-size: var(--dia-font-size-2xl);    /* 22px */
  --dia-footer-link-size: var(--dia-font-size-sm);      /* 14px */
  --dia-footer-copyright-size: var(--dia-font-size-xs); /* 13px */

  /* Line Heights */
  --dia-line-height-tight: 1.2;
  --dia-line-height-normal: 1.4;
  --dia-line-height-relaxed: 1.6;

  /*
   * RESPONSIVE TYPOGRAPHY SCALING
   * Diabrowser breakpoints: 800px and 1000px
   */

  /* Hero Title Responsive Scaling */
  --hero-title-mobile: 4.8rem;       /* 48px */
  --hero-title-tablet: 6.4rem;       /* 64px */
  --hero-title-desktop: 7.2rem;      /* 72px */

  /* Section Heading Responsive Scaling */
  --section-heading-mobile: 2.4rem;  /* 24px */
  --section-heading-tablet: 3.2rem;  /* 32px */
  --section-heading-desktop: 3.4rem; /* 34px */

  /* Body Text Responsive Scaling */
  --body-text-mobile: 1.4rem;        /* 14px */
  --body-text-tablet: 1.6rem;        /* 16px */
  --body-text-desktop: 2.0rem;       /* 20px */

  /*
   * DIABROWSER CONTAINER SYSTEM
   * 17-breakpoint progressive container sizing (400px → 2000px in 100px increments)
   */
  --dia-container-micro: 400px;
  --dia-container-small: 500px;
  --dia-container-medium: 600px;
  --dia-container-large-mobile: 700px;
  --dia-container-tablet: 800px;
  --dia-container-tablet-large: 900px;
  --dia-container-desktop: 1000px;
  --dia-container-desktop-medium: 1100px;
  --dia-container-desktop-large: 1200px;
  --dia-container-xl: 1300px;
  --dia-container-xxl: 1400px;
  --dia-container-ultra: 1500px;
  --dia-container-wide: 1600px;
  --dia-container-super: 1700px;
  --dia-container-mega: 1800px;
  --dia-container-giga: 1900px;
  --dia-container-max: 2000px;

  /*
   * CONTAINER SYSTEM
   * Consistent container widths and spacing
   */
  --container-max-width: 1296px;
  --container-padding: 1.6rem;       /* 16px */
  --container-padding-mobile: 1.6rem; /* 16px */
  --container-padding-tablet: 2.4rem; /* 24px */

  /* Section Spacing */
  --section-padding-mobile: 4.8rem;  /* 48px */
  --section-padding-tablet: 6.0rem;  /* 60px */
  --section-padding-desktop: 8.0rem; /* 80px */

  /*
   * DIABROWSER SHADOW SYSTEM
   * Enhanced light mode shadow system - two-shadow technique
   */
  --dia-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(255, 255, 255, 0.3);
  --dia-shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(255, 255, 255, 0.4);
  --dia-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(255, 255, 255, 0.5);
  --dia-shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.15), 0 16px 32px rgba(255, 255, 255, 0.6);

  /* Mobile-optimized shadows for performance */
  --dia-shadow-sm-mobile: 0 1px 2px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(255, 255, 255, 0.2);
  --dia-shadow-md-mobile: 0 1px 3px rgba(0, 0, 0, 0.06), 0 4px 8px rgba(255, 255, 255, 0.3);

  /*
   * DIABROWSER BORDER RADIUS SYSTEM
   * Universal 16px radius system
   */
  --dia-radius-standard: 16px;  /* Universal standard radius */
  --dia-radius-full: 9999px;    /* Circles and pills only */

  /* 
   * ANIMATION AND INTERACTION
   */
  --transition-base: 0.2s ease;
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --hover-transform: translateY(-2px);
  --pressed-transform: translateY(-1px);

  /*
   * DIABROWSER CARD SYSTEM
   * Enhanced Card Base Styles - Light mode elevation system
   */
  --dia-card-border-radius: var(--dia-radius-standard); /* Universal 16px radius */
  --dia-card-border: 1px solid rgba(0, 0, 0, 0.04); /* Universal card border */
  --dia-card-border-hover: 1px solid rgba(0, 0, 0, 0.08); /* Hover border */
  --dia-card-border-top-highlight: 1px solid rgba(255, 255, 255, 0.8); /* Top highlight for elevation */
  --dia-card-box-shadow: var(--dia-shadow-sm);
  --dia-card-hover-box-shadow: var(--dia-shadow-md);
  --dia-card-pressed-box-shadow: var(--dia-shadow-lg);
  --dia-card-hover-transform: translateY(-2px);
  --dia-card-pressed-transform: translateY(-1px);

  /*
   * DIABROWSER BUTTON SYSTEM
   */
  --dia-button-border-radius: var(--dia-radius-standard); /* Universal 16px radius */
  --dia-button-transition: 0.2s ease;
  --dia-button-hover-transform: translateY(-1px);
  --dia-button-pressed-transform: translateY(0px);

  /*
   * LEGACY COMPATIBILITY
   * For gradual migration from old system
   */
  --width-1070: 1070px;
  --width-843: 843px;   /* Solutions section header max-width */
  --width-616: 616px;   /* Solutions section description max-width */
  --color-border-light: rgba(0, 0, 0, 0.04);

  /* Legacy laptop font sizes - for backward compatibility */
  --font-size-h1-laptop: var(--dia-hero-secondary-size);  /* 36px mobile, 64px desktop */
  --font-size-h3-laptop: var(--dia-section-heading-size); /* 24px mobile, 34px desktop */

  /* Legacy breakpoint variables - for backward compatibility */
  --breakpoint-laptop: 1000px;
  --breakpoint-tablet: 800px;
  --breakpoint-mobile: 480px;
}

/* 
 * RESPONSIVE TYPOGRAPHY CLASSES
 * Utility classes for responsive text scaling
 */

/* Hero Title */
.hero-title {
  font-size: var(--hero-title-mobile);
  font-weight: 350;
  line-height: 1.1;
  letter-spacing: -0.04em;
}

@media (min-width: 800px) {
  .hero-title {
    font-size: var(--hero-title-tablet);
  }
}

@media (min-width: 1000px) {
  .hero-title {
    font-size: var(--hero-title-desktop);
  }
}

/* Section Heading */
.section-heading {
  font-size: var(--section-heading-mobile);
  font-weight: 350;
  line-height: 1.2;
  letter-spacing: -0.04em;
}

@media (min-width: 800px) {
  .section-heading {
    font-size: var(--section-heading-tablet);
  }
}

@media (min-width: 1000px) {
  .section-heading {
    font-size: var(--section-heading-desktop);
  }
}

/* Body Text */
.body-text {
  font-size: var(--body-text-mobile);
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: -0.02em;
}

@media (min-width: 800px) {
  .body-text {
    font-size: var(--body-text-tablet);
  }
}

@media (min-width: 1000px) {
  .body-text {
    font-size: var(--body-text-desktop);
  }
}

/* 
 * COMPONENT UTILITY CLASSES
 * Reusable classes for common component patterns
 */

/* Card Base - Using Diabrowser tokens */
.card-base {
  background: var(--dia-color-card-background);
  border: var(--dia-card-border);
  border-radius: var(--dia-card-border-radius);
  box-shadow: var(--dia-card-box-shadow);
  transition: var(--transition-base);
}

.card-base:hover {
  transform: var(--dia-card-hover-transform);
  box-shadow: var(--dia-card-hover-box-shadow);
  border: var(--dia-card-border-hover);
}

/* Button Base - Using Diabrowser tokens */
.btn-base {
  font-family: inherit;
  font-weight: var(--dia-font-weight-medium);
  font-size: var(--dia-button-text-size);
  border-radius: var(--dia-button-border-radius);
  transition: var(--dia-button-transition);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--dia-space-2);
}

.btn-base:hover {
  transform: var(--dia-button-hover-transform);
}

.btn-base:active {
  transform: var(--dia-button-pressed-transform);
}

/* Container - Using Diabrowser tokens */
.container-base {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

/* Section - Using Diabrowser tokens */
.section-base {
  padding: var(--section-padding-mobile) 0;
}

@media (min-width: 800px) {
  .section-base {
    padding: var(--section-padding-tablet) 0;
  }
}

@media (min-width: 1000px) {
  .section-base {
    padding: var(--section-padding-desktop) 0;
  }
}

/*
 * DIABROWSER UTILITY CLASSES
 * Typography classes using diabrowser tokens
 */

/* Diabrowser Typography Classes */
.dia-hero-title {
  font-size: var(--dia-hero-title-size);
  font-weight: var(--dia-font-weight-light);
  line-height: 1.1;
  letter-spacing: -0.04em;
}

.dia-section-heading {
  font-size: var(--dia-section-heading-size);
  font-weight: var(--dia-font-weight-light);
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.dia-body-text {
  font-size: var(--dia-body-text-size);
  font-weight: var(--dia-font-weight-normal);
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.dia-card-title {
  font-size: var(--dia-card-title-size);
  font-weight: var(--dia-font-weight-medium);
  line-height: 1.3;
}

.dia-card-description {
  font-size: var(--dia-card-description-size);
  font-weight: var(--dia-font-weight-normal);
  line-height: 1.5;
}

/*
 * HEADER-SPECIFIC STYLES
 * CTA arrow hover animation
 */
.header-actions .btn:hover .cta-arrow,
a:hover .cta-arrow {
  transform: translateX(4px);
}

/*
 * HERO-SPECIFIC RESPONSIVE STYLES
 * Responsive adjustments for hero section
 */

/* Mobile responsive adjustments */
@media screen and (max-width: 800px) {
  .hero-title-line {
    flex-direction: column !important;
    white-space: normal !important;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 768px) and (max-width: 991px) {
  .hero-title-line {
    flex-wrap: wrap;
  }
}

/* Desktop responsive adjustments */
@media (min-width: 800px) {
  :root {
    --dia-hero-title-size: var(--dia-font-size-6xl); /* 64px on tablet+ */
  }
}

@media (min-width: 1000px) {
  :root {
    --dia-hero-title-size: var(--dia-font-size-7xl); /* 72px on desktop */
    --dia-hero-description-size: var(--dia-font-size-xl); /* 20px on desktop */
  }
}

/*
 * LARGE TEXT SECTION STYLES
 * Typography and spacing for large text sections
 */

/*
 * LARGE TEXT SECTION - MATERIAL DESIGN IMPLEMENTATION
 * Typography scaling with Material Design breakpoints
 */

.large-text-content h2 {
  margin: 0;
  font-family: var(--font-family-inter);
  font-size: 2rem; /* 32dp - Material Design mobile large text */
  font-weight: var(--dia-hero-secondary-weight);
  line-height: var(--dia-hero-secondary-line-height);
  color: var(--dia-color-text-secondary);
}

.large-text-content strong {
  font-weight: var(--dia-hero-secondary-weight);
  color: var(--dia-color-text-primary);
}

/* Material Design responsive typography */
@media (min-width: 600px) {
  .large-text-content h2 {
    font-size: 2.5rem; /* 40dp - Material Design md large text */
  }
}

@media (min-width: 905px) {
  .large-text-content h2 {
    font-size: 3rem; /* 48dp - Material Design lg large text */
  }
}

@media (min-width: 1240px) {
  .large-text-content h2 {
    font-size: 3.5rem; /* 56dp - Material Design xl large text */
  }
}

@media (min-width: 1440px) {
  .large-text-content h2 {
    font-size: 4rem; /* 64dp - Material Design xxl large text */
  }
}

/*
 * BENTO ALT GRID RESPONSIVE STYLES
 * Matching original BentoAltGrid breakpoints
 */

/* Responsive grid layout for bento-alt cards */
@media (min-width: 800px) {
  .bento-alt-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--dia-space-8) !important;
  }
}

@media (min-width: 1000px) {
  .bento-alt-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: var(--dia-space-10) !important;
  }
}

/* Bento Alt Card responsive adjustments */
@media (max-width: 1000px) {
  .bento-alt-card {
    padding: var(--dia-space-9) !important;
  }
}

@media (max-width: 800px) {
  .bento-alt-card {
    padding: var(--dia-space-8) !important;
    min-height: 250px !important;
  }
}

@media (max-width: 480px) {
  .bento-alt-card {
    min-height: 220px !important;
  }
}

/*
 * PROBLEM COMPARISON RESPONSIVE STYLES
 * Matching original ProblemComparison breakpoints
 */

/* Responsive layout for comparison cards */
@media (max-width: 1000px) {
  .comparison-layout {
    flex-direction: column !important;
    gap: var(--dia-space-8) !important;
  }

  .comparison-layout.reverse {
    flex-direction: column !important;
  }
}

@media (max-width: 800px) {
  .comparison-card {
    padding: var(--dia-space-8) !important;
  }

  .comparison-text {
    padding: var(--dia-space-6) !important;
  }
}

@media (max-width: 480px) {
  .comparison-card h4 {
    font-size: var(--dia-font-size-3xl) !important;
    margin-bottom: var(--dia-space-8) !important;
  }

  .comparison-card {
    padding: var(--dia-space-6) !important;
  }
}

/*
 * SOLUTIONS SECTION RESPONSIVE STYLES
 * Matching original SolutionsSection breakpoints
 */

/* Solutions carousel responsive adjustments */
@media (max-width: 800px) {
  .solutions-carousel-section {
    margin-top: var(--dia-space-14) !important;
    margin-bottom: var(--dia-space-8) !important;
  }

  .solutions-carousel-section:first-of-type {
    margin-top: var(--dia-space-10) !important;
  }

  .solutions-carousel-header {
    margin-bottom: var(--dia-space-6) !important;
  }
}

@media (max-width: 480px) {
  .solutions-section {
    padding: var(--dia-space-6) 0 !important;
  }
}

/*
 * PROFIT APPROACH SECTION RESPONSIVE STYLES
 * Matching original ProfitApproachSection breakpoints
 */

/* Profit approach responsive adjustments */
@media (max-width: 800px) {
  .profit-approach-section {
    padding: var(--dia-space-12) 0 !important;
  }

  .profit-approach-content {
    padding: 0 var(--dia-space-4) !important;
  }

  .profit-approach-header {
    margin-bottom: var(--dia-space-10) !important;
  }
}

@media (max-width: 480px) {
  .profit-approach-section {
    padding: var(--dia-space-10) 0 !important;
  }

  .profit-approach-content {
    padding: 0 var(--dia-space-3) !important;
  }
}

/* Override bento card background color for profit approach section */
.profit-approach-section .bento-card {
  background-color: var(--dia-color-card-background) !important;
}

/*
 * FOOTER RESPONSIVE STYLES
 * Matching original Footer breakpoints
 */

/* Footer hover effects */
.footer-link:hover {
  color: var(--dia-color-background) !important;
}

.social-link:hover {
  color: var(--dia-color-background) !important;
  transform: translateY(-1px);
}

/* Footer responsive adjustments */
@media (max-width: 768px) {
  .footer-main {
    flex-direction: column !important;
    gap: var(--dia-space-6) !important;
    text-align: center;
  }

  .footer-brand {
    max-width: 100% !important;
  }

  .footer-nav {
    justify-content: center !important;
    gap: var(--dia-space-4) !important;
  }

  .footer-bottom {
    flex-direction: column !important;
    gap: var(--dia-space-4) !important;
    text-align: center;
  }

  .footer-social {
    order: -1 !important; /* Show social links first on mobile */
  }
}

/*
 * COMPREHENSIVE DEBUG CSS - DO NOT REMOVE
 * Visual debugging for pixel-perfect reconstruction
 */

/* Container boundaries with colored borders */
.debug-container {
  border: 2px solid #ff0000 !important;
  background: rgba(255, 0, 0, 0.05) !important;
  position: relative !important;
}

.debug-container::before {
  content: 'CONTAINER' !important;
  position: absolute !important;
  top: -20px !important;
  left: 0 !important;
  background: #ff0000 !important;
  color: white !important;
  padding: 2px 6px !important;
  font-size: 10px !important;
  font-weight: bold !important;
  z-index: 10000 !important;
}

/* Component boundaries */
.debug-header {
  border: 3px solid #00ff00 !important;
  background: rgba(0, 255, 0, 0.05) !important;
}

.debug-hero {
  border: 3px solid #0000ff !important;
  background: rgba(0, 0, 255, 0.05) !important;
}

.debug-section {
  border: 2px solid #ff00ff !important;
  background: rgba(255, 0, 255, 0.03) !important;
}

/* Grid column visualizations */
.debug-grid {
  background-image: repeating-linear-gradient(
    to right,
    rgba(255, 0, 0, 0.1) 0,
    rgba(255, 0, 0, 0.1) 1px,
    transparent 1px,
    transparent calc(100% / 12)
  ) !important;
}

/* Spacing measurements */
.debug-spacing {
  position: relative !important;
}

.debug-spacing::after {
  content: attr(data-spacing) !important;
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  background: #ffff00 !important;
  color: #000 !important;
  padding: 1px 4px !important;
  font-size: 9px !important;
  font-weight: bold !important;
  z-index: 10001 !important;
}

/* Breakpoint indicators */
.debug-breakpoint::before {
  content: 'XS' !important;
  position: fixed !important;
  top: 10px !important;
  right: 10px !important;
  background: #000 !important;
  color: #fff !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: bold !important;
  z-index: 10002 !important;
  border-radius: 4px !important;
}

@media (min-width: 360px) {
  .debug-breakpoint::before {
    content: 'SM' !important;
    background: #333 !important;
  }
}

@media (min-width: 600px) {
  .debug-breakpoint::before {
    content: 'MD' !important;
    background: #666 !important;
  }
}

@media (min-width: 905px) {
  .debug-breakpoint::before {
    content: 'LG' !important;
    background: #999 !important;
  }
}

@media (min-width: 1240px) {
  .debug-breakpoint::before {
    content: 'XL' !important;
    background: #ccc !important;
    color: #000 !important;
  }
}

@media (min-width: 1440px) {
  .debug-breakpoint::before {
    content: 'XXL' !important;
    background: #fff !important;
    color: #000 !important;
  }
}

/* Typography debugging */
.debug-text {
  position: relative !important;
}

.debug-text::before {
  content: attr(data-font-size) ' / ' attr(data-line-height) !important;
  position: absolute !important;
  top: -15px !important;
  left: 0 !important;
  background: #00ffff !important;
  color: #000 !important;
  padding: 1px 4px !important;
  font-size: 8px !important;
  font-weight: bold !important;
  z-index: 10003 !important;
}

/*
 * PROGRESSIVE CONTAINER SYSTEM
 * 17-breakpoint system with Material grid mapping
 * 4 columns xs/sm, 8 columns md, 12 columns lg/xl/xxl
 */

/* Base container class */
.dia-container-progressive {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--container-padding);
  box-sizing: border-box;
}

/* Progressive container sizing - 400px to 2000px in 100px increments */
@media (min-width: 400px) {
  .dia-container-progressive {
    max-width: var(--dia-container-micro); /* 400px */
  }
}

@media (min-width: 500px) {
  .dia-container-progressive {
    max-width: var(--dia-container-small); /* 500px */
  }
}

@media (min-width: 600px) {
  .dia-container-progressive {
    max-width: var(--dia-container-medium); /* 600px */
  }
}

@media (min-width: 700px) {
  .dia-container-progressive {
    max-width: var(--dia-container-large-mobile); /* 700px */
  }
}

@media (min-width: 800px) {
  .dia-container-progressive {
    max-width: var(--dia-container-tablet); /* 800px */
  }
}

@media (min-width: 900px) {
  .dia-container-progressive {
    max-width: var(--dia-container-tablet-large); /* 900px */
  }
}

@media (min-width: 1000px) {
  .dia-container-progressive {
    max-width: var(--dia-container-desktop); /* 1000px */
  }
}

@media (min-width: 1100px) {
  .dia-container-progressive {
    max-width: var(--dia-container-desktop-medium); /* 1100px */
  }
}

@media (min-width: 1200px) {
  .dia-container-progressive {
    max-width: var(--dia-container-desktop-large); /* 1200px */
  }
}

@media (min-width: 1300px) {
  .dia-container-progressive {
    max-width: var(--dia-container-xl); /* 1300px */
  }
}

@media (min-width: 1400px) {
  .dia-container-progressive {
    max-width: var(--dia-container-xxl); /* 1400px */
  }
}

@media (min-width: 1500px) {
  .dia-container-progressive {
    max-width: var(--dia-container-ultra); /* 1500px */
  }
}

@media (min-width: 1600px) {
  .dia-container-progressive {
    max-width: var(--dia-container-wide); /* 1600px */
  }
}

@media (min-width: 1700px) {
  .dia-container-progressive {
    max-width: var(--dia-container-super); /* 1700px */
  }
}

@media (min-width: 1800px) {
  .dia-container-progressive {
    max-width: var(--dia-container-mega); /* 1800px */
  }
}

@media (min-width: 1900px) {
  .dia-container-progressive {
    max-width: var(--dia-container-giga); /* 1900px */
  }
}

@media (min-width: 2000px) {
  .dia-container-progressive {
    max-width: var(--dia-container-max); /* 2000px */
  }
}

/*
 * MATERIAL DESIGN GRID SYSTEM
 * Column snap implementation with proper gutter/margin distinction
 */

/* Base Material Design grid container */
.material-grid {
  display: grid;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

/* xs: 0-359px - 4 columns, 16dp gutter, 16dp margin */
.material-grid {
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem; /* 16dp gutter */
  padding: 0 1rem; /* 16dp margin */
  max-width: none;
}

/* sm: 360-599px - 4 columns, 16dp gutter, 16dp margin */
@media (min-width: 360px) {
  .material-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem; /* 16dp gutter */
    padding: 0 1rem; /* 16dp margin */
  }
}

/* md: 600-904px - 8 columns, 16dp gutter, 24dp margin */
@media (min-width: 600px) {
  .material-grid {
    grid-template-columns: repeat(8, 1fr);
    gap: 1rem; /* 16dp gutter */
    padding: 0 1.5rem; /* 24dp margin */
  }
}

/* lg: 905-1239px - 12 columns, 24dp gutter, 24dp margin */
@media (min-width: 905px) {
  .material-grid {
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem; /* 24dp gutter */
    padding: 0 1.5rem; /* 24dp margin */
  }
}

/* xl: 1240-1439px - 12 columns, 24dp gutter, 24dp margin, max 1600px */
@media (min-width: 1240px) {
  .material-grid {
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem; /* 24dp gutter */
    padding: 0 1.5rem; /* 24dp margin */
    max-width: 1600px; /* Do not exceed 1600px content width */
  }
}

/* xxl: ≥1440px - 12 columns, 24dp gutter, auto margin, max 1600px */
@media (min-width: 1440px) {
  .material-grid {
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem; /* 24dp gutter */
    padding: 0 1.5rem; /* Auto margins to center, minimum 24dp */
    max-width: 1600px; /* Keep content width ≤ 1600px */
  }
}

/*
 * MATERIAL DESIGN COLUMN SPAN UTILITIES
 * Column snap implementation for all breakpoints
 */

/* Base column spans (xs/sm - 4 columns) */
.col-span-1 { grid-column: span 1; }
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }

/* Full width utility */
.col-full { grid-column: 1 / -1; }

/* md column spans (8 columns) */
@media (min-width: 600px) {
  .md\:col-span-1 { grid-column: span 1; }
  .md\:col-span-2 { grid-column: span 2; }
  .md\:col-span-3 { grid-column: span 3; }
  .md\:col-span-4 { grid-column: span 4; }
  .md\:col-span-5 { grid-column: span 5; }
  .md\:col-span-6 { grid-column: span 6; }
  .md\:col-span-7 { grid-column: span 7; }
  .md\:col-span-8 { grid-column: span 8; }
}

/* lg/xl/xxl column spans (12 columns) */
@media (min-width: 905px) {
  .lg\:col-span-1 { grid-column: span 1; }
  .lg\:col-span-2 { grid-column: span 2; }
  .lg\:col-span-3 { grid-column: span 3; }
  .lg\:col-span-4 { grid-column: span 4; }
  .lg\:col-span-5 { grid-column: span 5; }
  .lg\:col-span-6 { grid-column: span 6; }
  .lg\:col-span-7 { grid-column: span 7; }
  .lg\:col-span-8 { grid-column: span 8; }
  .lg\:col-span-9 { grid-column: span 9; }
  .lg\:col-span-10 { grid-column: span 10; }
  .lg\:col-span-11 { grid-column: span 11; }
  .lg\:col-span-12 { grid-column: span 12; }
}

/* Full width utilities */
.col-full { grid-column: 1 / -1; }

/* Column start utilities for precise positioning */
.col-start-1 { grid-column-start: 1; }
.col-start-2 { grid-column-start: 2; }
.col-start-3 { grid-column-start: 3; }
.col-start-4 { grid-column-start: 4; }

@media (min-width: 600px) {
  .md\:col-start-1 { grid-column-start: 1; }
  .md\:col-start-2 { grid-column-start: 2; }
  .md\:col-start-3 { grid-column-start: 3; }
  .md\:col-start-4 { grid-column-start: 4; }
  .md\:col-start-5 { grid-column-start: 5; }
  .md\:col-start-6 { grid-column-start: 6; }
  .md\:col-start-7 { grid-column-start: 7; }
  .md\:col-start-8 { grid-column-start: 8; }
}

@media (min-width: 905px) {
  .lg\:col-start-1 { grid-column-start: 1; }
  .lg\:col-start-2 { grid-column-start: 2; }
  .lg\:col-start-3 { grid-column-start: 3; }
  .lg\:col-start-4 { grid-column-start: 4; }
  .lg\:col-start-5 { grid-column-start: 5; }
  .lg\:col-start-6 { grid-column-start: 6; }
  .lg\:col-start-7 { grid-column-start: 7; }
  .lg\:col-start-8 { grid-column-start: 8; }
  .lg\:col-start-9 { grid-column-start: 9; }
  .lg\:col-start-10 { grid-column-start: 10; }
  .lg\:col-start-11 { grid-column-start: 11; }
  .lg\:col-start-12 { grid-column-start: 12; }
}

/*
 * MATERIAL DESIGN RESPONSIVE PATTERNS
 * Reveal → Transform → Reflow implementation
 */

/* Navigation patterns */
.nav-overlay {
  /* Mobile: overlay navigation */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.5);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.nav-overlay.open {
  transform: translateX(0);
}

/* lg: Transform to persistent navigation */
@media (min-width: 905px) {
  .nav-persistent {
    position: static;
    transform: none;
    background: transparent;
    z-index: auto;
  }
}

/* Card layout patterns */
.card-list {
  /* xs/sm: Single column list */
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* md: Transform to 2-column grid */
@media (min-width: 600px) {
  .card-grid-md {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

/* lg: Reflow to 3-column grid */
@media (min-width: 905px) {
  .card-grid-lg {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

/* Content reveal patterns */
.content-reveal {
  /* xs: Hidden content */
  display: none;
}

/* md: Reveal content */
@media (min-width: 600px) {
  .content-reveal-md {
    display: block;
  }
}

/* lg: Transform content layout */
@media (min-width: 905px) {
  .content-transform-lg {
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }
}

/* Side panel patterns */
.side-panel {
  /* xs/sm: Overlay panel */
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  height: 100vh;
  background: white;
  transition: right 0.3s ease;
  z-index: 1001;
}

.side-panel.open {
  right: 0;
}

/* lg: Transform to persistent side panel */
@media (min-width: 905px) {
  .side-panel-persistent {
    position: static;
    width: auto;
    height: auto;
    right: auto;
    background: transparent;
    transition: none;
  }
}

/* Image scaling patterns */
.image-scale {
  /* xs: Full-bleed images */
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

/* md: Transform to contained images */
@media (min-width: 600px) {
  .image-contained {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    border-radius: 16px;
  }
}

/* Button group patterns */
.button-group {
  /* xs: Stacked buttons */
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.button-group .btn {
  width: 100%;
}

/* md: Transform to horizontal layout */
@media (min-width: 600px) {
  .button-group-horizontal {
    flex-direction: row;
    width: auto;
  }

  .button-group-horizontal .btn {
    width: auto;
  }
}

/* Grid item span utilities */
.dia-col-span-1 { grid-column: span 1; }
.dia-col-span-2 { grid-column: span 2; }
.dia-col-span-3 { grid-column: span 3; }
.dia-col-span-4 { grid-column: span 4; }
.dia-col-span-5 { grid-column: span 5; }
.dia-col-span-6 { grid-column: span 6; }
.dia-col-span-7 { grid-column: span 7; }
.dia-col-span-8 { grid-column: span 8; }
.dia-col-span-9 { grid-column: span 9; }
.dia-col-span-10 { grid-column: span 10; }
.dia-col-span-11 { grid-column: span 11; }
.dia-col-span-12 { grid-column: span 12; }

/* Responsive span utilities */
@media (min-width: 800px) {
  .dia-col-span-md-1 { grid-column: span 1; }
  .dia-col-span-md-2 { grid-column: span 2; }
  .dia-col-span-md-3 { grid-column: span 3; }
  .dia-col-span-md-4 { grid-column: span 4; }
  .dia-col-span-md-5 { grid-column: span 5; }
  .dia-col-span-md-6 { grid-column: span 6; }
  .dia-col-span-md-7 { grid-column: span 7; }
  .dia-col-span-md-8 { grid-column: span 8; }
}

@media (min-width: 1000px) {
  .dia-col-span-lg-1 { grid-column: span 1; }
  .dia-col-span-lg-2 { grid-column: span 2; }
  .dia-col-span-lg-3 { grid-column: span 3; }
  .dia-col-span-lg-4 { grid-column: span 4; }
  .dia-col-span-lg-5 { grid-column: span 5; }
  .dia-col-span-lg-6 { grid-column: span 6; }
  .dia-col-span-lg-7 { grid-column: span 7; }
  .dia-col-span-lg-8 { grid-column: span 8; }
  .dia-col-span-lg-9 { grid-column: span 9; }
  .dia-col-span-lg-10 { grid-column: span 10; }
  .dia-col-span-lg-11 { grid-column: span 11; }
  .dia-col-span-lg-12 { grid-column: span 12; }
}

/*
 * SPACING UTILITIES
 * Standardized spacing utilities using 0.6rem base grid
 */

/* Gap utilities using diabrowser tokens */
.dia-gap-1 { gap: var(--dia-space-1); }
.dia-gap-2 { gap: var(--dia-space-2); }
.dia-gap-3 { gap: var(--dia-space-3); }
.dia-gap-4 { gap: var(--dia-space-4); }
.dia-gap-5 { gap: var(--dia-space-5); }
.dia-gap-6 { gap: var(--dia-space-6); }
.dia-gap-7 { gap: var(--dia-space-7); }
.dia-gap-8 { gap: var(--dia-space-8); }
.dia-gap-9 { gap: var(--dia-space-9); }
.dia-gap-10 { gap: var(--dia-space-10); }
.dia-gap-11 { gap: var(--dia-space-11); }
.dia-gap-12 { gap: var(--dia-space-12); }
.dia-gap-13 { gap: var(--dia-space-13); }
.dia-gap-14 { gap: var(--dia-space-14); }
.dia-gap-15 { gap: var(--dia-space-15); }
.dia-gap-16 { gap: var(--dia-space-16); }
.dia-gap-18 { gap: var(--dia-space-18); }

/* Margin utilities using diabrowser tokens */
.dia-m-1 { margin: var(--dia-space-1); }
.dia-m-2 { margin: var(--dia-space-2); }
.dia-m-3 { margin: var(--dia-space-3); }
.dia-m-4 { margin: var(--dia-space-4); }
.dia-m-5 { margin: var(--dia-space-5); }
.dia-m-6 { margin: var(--dia-space-6); }
.dia-m-7 { margin: var(--dia-space-7); }
.dia-m-8 { margin: var(--dia-space-8); }
.dia-m-9 { margin: var(--dia-space-9); }
.dia-m-10 { margin: var(--dia-space-10); }
.dia-m-11 { margin: var(--dia-space-11); }
.dia-m-12 { margin: var(--dia-space-12); }
.dia-m-13 { margin: var(--dia-space-13); }
.dia-m-14 { margin: var(--dia-space-14); }
.dia-m-15 { margin: var(--dia-space-15); }
.dia-m-16 { margin: var(--dia-space-16); }
.dia-m-18 { margin: var(--dia-space-18); }

/* Margin top utilities */
.dia-mt-1 { margin-top: var(--dia-space-1); }
.dia-mt-2 { margin-top: var(--dia-space-2); }
.dia-mt-3 { margin-top: var(--dia-space-3); }
.dia-mt-4 { margin-top: var(--dia-space-4); }
.dia-mt-5 { margin-top: var(--dia-space-5); }
.dia-mt-6 { margin-top: var(--dia-space-6); }
.dia-mt-7 { margin-top: var(--dia-space-7); }
.dia-mt-8 { margin-top: var(--dia-space-8); }
.dia-mt-9 { margin-top: var(--dia-space-9); }
.dia-mt-10 { margin-top: var(--dia-space-10); }
.dia-mt-11 { margin-top: var(--dia-space-11); }
.dia-mt-12 { margin-top: var(--dia-space-12); }
.dia-mt-13 { margin-top: var(--dia-space-13); }
.dia-mt-14 { margin-top: var(--dia-space-14); }
.dia-mt-15 { margin-top: var(--dia-space-15); }
.dia-mt-16 { margin-top: var(--dia-space-16); }
.dia-mt-18 { margin-top: var(--dia-space-18); }

/* Margin bottom utilities */
.dia-mb-1 { margin-bottom: var(--dia-space-1); }
.dia-mb-2 { margin-bottom: var(--dia-space-2); }
.dia-mb-3 { margin-bottom: var(--dia-space-3); }
.dia-mb-4 { margin-bottom: var(--dia-space-4); }
.dia-mb-5 { margin-bottom: var(--dia-space-5); }
.dia-mb-6 { margin-bottom: var(--dia-space-6); }
.dia-mb-7 { margin-bottom: var(--dia-space-7); }
.dia-mb-8 { margin-bottom: var(--dia-space-8); }
.dia-mb-9 { margin-bottom: var(--dia-space-9); }
.dia-mb-10 { margin-bottom: var(--dia-space-10); }
.dia-mb-11 { margin-bottom: var(--dia-space-11); }
.dia-mb-12 { margin-bottom: var(--dia-space-12); }
.dia-mb-13 { margin-bottom: var(--dia-space-13); }
.dia-mb-14 { margin-bottom: var(--dia-space-14); }
.dia-mb-15 { margin-bottom: var(--dia-space-15); }
.dia-mb-16 { margin-bottom: var(--dia-space-16); }
.dia-mb-18 { margin-bottom: var(--dia-space-18); }

/* Padding utilities using diabrowser tokens */
.dia-p-1 { padding: var(--dia-space-1); }
.dia-p-2 { padding: var(--dia-space-2); }
.dia-p-3 { padding: var(--dia-space-3); }
.dia-p-4 { padding: var(--dia-space-4); }
.dia-p-5 { padding: var(--dia-space-5); }
.dia-p-6 { padding: var(--dia-space-6); }
.dia-p-7 { padding: var(--dia-space-7); }
.dia-p-8 { padding: var(--dia-space-8); }
.dia-p-9 { padding: var(--dia-space-9); }
.dia-p-10 { padding: var(--dia-space-10); }
.dia-p-11 { padding: var(--dia-space-11); }
.dia-p-12 { padding: var(--dia-space-12); }
.dia-p-13 { padding: var(--dia-space-13); }
.dia-p-14 { padding: var(--dia-space-14); }
.dia-p-15 { padding: var(--dia-space-15); }
.dia-p-16 { padding: var(--dia-space-16); }
.dia-p-18 { padding: var(--dia-space-18); }

/*
 * ANIMATION SYSTEM
 * Matching original animations for smooth transitions
 */

/* Fade-in animation */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Fade-up animation with delays */
.fade-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Delay classes for staggered animations */
.delay-100 { transition-delay: 0.1s; }
.delay-200 { transition-delay: 0.2s; }
.delay-300 { transition-delay: 0.3s; }
.delay-400 { transition-delay: 0.4s; }
.delay-500 { transition-delay: 0.5s; }

/* Button hover animations */
.btn-hover {
  transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-hover:hover {
  transform: var(--dia-button-hover-transform);
}

.btn-hover:active {
  transform: var(--dia-button-pressed-transform);
}

/* Card hover animations */
.card-hover {
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.card-hover:hover {
  transform: var(--dia-card-hover-transform);
  box-shadow: var(--dia-card-hover-box-shadow);
}

/* Link hover animations */
.link-hover {
  transition: color 0.2s ease;
}

/* Navigation link animations */
.nav-link {
  position: relative;
  transition: color 0.2s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .fade-up,
  .btn-hover,
  .card-hover,
  .link-hover,
  .nav-link {
    transition: none;
    transform: none;
    animation: none;
  }

  .nav-link::after {
    transition: none;
  }
}
