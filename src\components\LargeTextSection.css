/* Adjust spacing for the large text section */
.large-text-section {
  padding: var(--dia-space-12) 0; /* Remove hardcoded horizontal padding - let container handle it */
  background-color: var(--dia-color-background);
}

/* Diabrowser migration: when .dia-typography is applied */
.large-text-section.dia-typography {
  background-color: var(--dia-color-background);
  padding: var(--dia-space-12) 0; /* Remove horizontal padding - let container handle it */
}

/* Increase space between hero section and large text section by 75% */
.large-text-section-wrapper {
  margin-top: 8.75rem; /* Added top margin */
  margin-bottom: 9rem; /* Added bottom margin for 80% more space before Enhancing section */
}

.large-text-container {
  max-width: 800px;
  margin: 0 auto;
}

.large-text-content {
  text-align: center;
  /* Now using section-subheading styles */
}

.large-text-content.section-subheading h2 {
  margin: 0;
  font-family: var(--font-family-inter) !important;
  font-size: var(--dia-font-size-subheading-mobile) !important;
  font-weight: var(--dia-hero-secondary-weight) !important;
  line-height: var(--dia-hero-secondary-line-height) !important;
  color: var(--dia-color-text-secondary) !important;
}

.large-text-content strong {
  font-weight: var(--dia-hero-secondary-weight);
  color: var(--color-text-primary);
}

/* Responsive design */
@media (min-width: 768px) {
  .large-text-content.section-subheading h2 {
    font-size: var(--dia-font-size-4xl) !important;
  }
}

@media (min-width: 1920px) {
  .large-text-content.section-subheading h2 {
    font-size: var(--dia-font-size-4xl) !important;
  }
}

@media (max-width: var(--breakpoint-tablet)) {
  .large-text-section {
    padding: var(--dia-space-4) 0;
  }
}

@media (max-width: 480px) {
  .large-text-content {
    padding: 0;
  }

  .large-text-content.section-subheading h2 {
    font-size: var(--dia-font-size-2xl) !important;
  }
}
