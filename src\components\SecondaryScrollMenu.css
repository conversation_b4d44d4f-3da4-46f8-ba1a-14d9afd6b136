/* Component-specific variables that supplement the global design system */
.secondary-scroll-menu {
  --color-text-on-dark-primary: var(--dia-color-white-solid);
  --color-text-on-dark-secondary: rgba(255, 255, 255, 0.7);
  --color-border-on-dark: rgba(255, 255, 255, 0.12);

  /* Main container styles */
  position: fixed;
  top: 74px; /* 64px header height + 10px gap */
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  max-width: 700px;
  z-index: 99; /* Just below header */
  background-color: var(--dia-color-header-background);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-slow);
  opacity: 0;
  visibility: hidden;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--dia-color-border-light);
  border-radius: var(--radius-standard); /* Use 16px standard radius */
  padding: var(--dia-space-1);
  pointer-events: auto;
  will-change: opacity, visibility, transform;
}

/* When header is hidden, move secondary menu up but preserve 10px gap */
.secondary-scroll-menu.header-hidden {
  top: 10px; /* Keep just the 10px gap, remove the 64px header height */
  transform: translateX(-50%); /* Maintain the horizontal centering */
}

.secondary-scroll-menu.visible {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
}

/* Menu items container */
.menu-items {
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 100%;
  overflow-x: auto;
  white-space: nowrap;
  -ms-overflow-style: none;
  scrollbar-width: none;
  margin: 0;
  padding: 0;
  gap: 8px;
}

.menu-items::-webkit-scrollbar {
  display: none;
}

/* Standard menu item styles - matching main nav pattern */
.menu-item {
  position: relative;
  height: 32px;
  padding: var(--dia-space-1) var(--dia-space-4);
  border: none;
  background: transparent;
  color: var(--dia-color-text-secondary); /* Match main nav default */
  font-family: var(--font-family-inter);
  font-size: var(--dia-secondary-nav-link-size);
  font-weight: var(--dia-nav-link-weight);
  letter-spacing: var(--dia-body-text-spacing);
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: var(--radius-standard); /* Use standard 16px radius */
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  white-space: nowrap;
  text-decoration: none;
}

.menu-item:hover {
  color: var(--dia-color-text-primary);
  background-color: var(--dia-color-background-overlay);
  transform: translateY(-1px);
}

.menu-item:hover::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background-color: var(--dia-color-text-primary);
  opacity: 0.5;
}

.menu-item.active {
  color: var(--dia-color-text-primary);
  font-weight: var(--dia-nav-link-active-weight); /* Match main nav active */
  background: transparent; /* Remove black background */
  box-shadow: none; /* Remove shadow */
}

.menu-item.active::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background-color: var(--dia-color-text-primary);
  opacity: 1;
}

/* Tubelight Navigation Styles */
.tubelight-container {
  display: flex;
  align-items: center;
  gap: 3px;
  height: 100%;
  max-width: 100%;
  width: auto;
  overflow-x: hidden;
}

.tubelight-item {
  position: relative;
  cursor: pointer;
  font-size: var(--dia-secondary-nav-link-size);
  font-weight: var(--dia-nav-link-weight);
  font-family: var(--font-family-inter);
  letter-spacing: var(--dia-body-text-spacing);
  padding: var(--dia-space-1) var(--dia-space-4);
  border-radius: var(--radius-standard); /* Use standard 16px radius */
  background-color: transparent;
  color: var(--dia-color-text-secondary);
  border: none;
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  white-space: nowrap;
  text-decoration: none;
}

.tubelight-item:hover {
  color: var(--dia-color-text-primary);
  font-weight: var(--dia-nav-link-active-weight);
  transform: translateY(-1px);
}

.tubelight-item:hover::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background-color: var(--dia-color-text-primary);
  opacity: 0.5;
}

.tubelight-active {
  color: var(--dia-color-text-primary); /* Match main nav active */
  font-weight: var(--dia-nav-link-active-weight); /* Match main nav active */
  background: transparent; /* Remove black background */
}

.tubelight-active::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background-color: var(--dia-color-text-primary);
  opacity: 1;
}

.tubelight-active:hover {
  color: var(--dia-color-text-primary);
  font-weight: var(--dia-nav-link-active-weight);
  transform: translateY(-1px);
}

.tubelight-active:hover::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background-color: var(--dia-color-text-primary);
  opacity: 1;
}

.tubelight-text {
  display: block;
}

.tubelight-icon {
  display: none;
}

/* Removed tubelight highlight and glow effects - using main nav pattern instead */

/* Responsive styles for tubelight navbar */
@media (max-width: 768px) {
  .secondary-scroll-menu {
    top: 74px; /* Keep it at the top like desktop */
    max-width: 92%; /* Wider on mobile */
    padding: 3px;
  }
  
  .tubelight-item {
    padding: var(--dia-space-1) var(--dia-space-3);
    font-size: var(--dia-secondary-nav-link-size);
    letter-spacing: var(--dia-body-text-spacing); /* Use consistent spacing */
  }
  
  /* Allow text to remain visible but make the items more compact */
  .tubelight-container {
    gap: 1px;
  }
}

/* Additional styles for very small screens */
@media (max-width: 480px) {
  .secondary-scroll-menu {
    max-width: 96%; /* Reduced to prevent overflow */
    transform: translateX(-50%);
  }
  
  .tubelight-item {
    font-size: var(--dia-secondary-nav-link-size);
  }
  
  .tubelight-container {
    gap: 0;
    max-width: 100%;
    overflow-x: hidden;
  }
}
/* Additional responsive adjustments for legacy menu items */
@media (max-width: 768px) {
  .menu-item {
    font-size: var(--dia-secondary-nav-link-size);
    padding: var(--dia-space-1) var(--dia-space-3); /* Adjust padding for mobile */
  }
}
