/*
 * ROAS vs PAX Comparison - Fresh Implementation
 * Optimized comparison cards with distinct backgrounds and consistent light mode styling
 */

.comparison-container {
  width: 100%;
  margin: 0 auto;
  background: transparent;
  border: none;
  box-shadow: none;
  border-radius: 0;
}

/* Card container with optimized spacing */
.comparison-cards {
  display: flex;
  flex-direction: row;
  gap: var(--dia-space-8);
  width: 100%;
  align-items: stretch;
}

/* Base comparison card styling */
.comparison-card {
  flex: 1;
  padding: var(--dia-space-10);
  border-radius: var(--card-border-radius);
  transition: var(--card-transition);
  position: relative;
  display: flex;
  flex-direction: column;
}

.comparison-card:hover {
  transform: var(--card-hover-transform);
}

/* ROAS Card - White background with border */
.roas-card {
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
}

.roas-card:hover {
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

/* PAX Card - Alternative background for distinction */
.pax-card {
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background-alt);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
}

.pax-card:hover {
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

/* Enhanced comparison card title */
.comparison-title {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-comparison-header-size);
  font-weight: var(--dia-comparison-header-weight);
  line-height: var(--dia-comparison-header-line-height);
  margin-bottom: var(--dia-space-6);
  text-align: left;
  letter-spacing: var(--letter-spacing-tight);
}

/* Responsive typography for comparison title */
@media (min-width: 800px) {
  .comparison-title {
    font-size: var(--card-comparison-header-tablet);
  }
}

@media (min-width: 1000px) {
  .comparison-title {
    font-size: var(--card-comparison-header-desktop);
  }
}

/* Comparison items container */
.comparison-items {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-6);
  flex: 1;
}

/* Individual comparison item */
.comparison-item {
  display: flex;
  align-items: flex-start;
  gap: var(--dia-space-3);
  padding: var(--dia-space-2) 0;
}

/* Icon styling with enhanced visual hierarchy */
.comparison-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  margin-top: 2px; /* Align with text baseline */
  flex-shrink: 0;
}

.comparison-item.negative .comparison-icon {
  color: var(--color-error);
}

.comparison-item.positive .comparison-icon {
  color: var(--color-success);
}

/* Text styling with improved readability */
.comparison-item.negative .comparison-text {
  color: var(--dia-color-text-secondary);
  font-weight: var(--dia-font-weight-medium);
}

.comparison-item.positive .comparison-text {
  color: var(--dia-color-text-primary);
  font-weight: var(--dia-font-weight-medium);
}

/* Base comparison text styling */
.comparison-text {
  font-family: var(--font-family-inter);
  font-size: var(--card-comparison-item-mobile);
  line-height: var(--card-comparison-item-line-height);
  color: var(--dia-color-text-secondary);
  margin: 0;
}

/* Enhanced responsive design */
@media (min-width: 800px) {
  .comparison-text {
    font-size: var(--card-comparison-item-tablet);
  }

  .comparison-cards {
    gap: var(--dia-space-10);
  }

  .comparison-card {
    padding: var(--dia-space-11);
  }
}

@media (min-width: 1000px) {
  .comparison-text {
    font-size: var(--card-comparison-item-desktop);
  }

  .comparison-cards {
    gap: var(--dia-space-12);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 800px) {
  .comparison-cards {
    flex-direction: column;
    gap: var(--dia-space-6);
  }

  .comparison-card {
    padding: var(--dia-space-8);
  }

  .comparison-title {
    margin-bottom: var(--dia-space-5);
  }

  .comparison-items {
    gap: var(--dia-space-5);
  }
}

@media (max-width: 480px) {
  .comparison-card {
    padding: var(--dia-space-6);
  }

  .comparison-title {
    font-size: var(--dia-font-size-xl);
    margin-bottom: var(--dia-space-4);
  }

  .comparison-text {
    font-size: var(--dia-font-size-sm);
  }

  .comparison-items {
    gap: var(--dia-space-4);
  }

  .comparison-item {
    gap: var(--dia-space-2);
  }
}
