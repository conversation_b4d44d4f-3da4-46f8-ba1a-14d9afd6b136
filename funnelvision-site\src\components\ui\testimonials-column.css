.testimonials-columns-wrapper {
  display: flex;
  justify-content: space-between;
  gap: var(--dia-space-9);
  width: 100%;
  margin-top: var(--dia-space-6);
  position: relative;
  overflow: hidden;
  /* Remove custom max-width - let parent container handle it */
}

.testimonial-column {
  flex-shrink: 0;
  width: 320px;
  height: 540px; /* Fixed height for the column */
  overflow: hidden;
  position: relative;
  flex: 1;
  border-radius: var(--radius-md);
  background: transparent;
  margin: 0 auto;
  max-width: 100%;
}

/* Uniform positioning for columns - no staggering for synchronized speed */
.testimonial-column-md {
  margin-top: 0; /* Removed stagger for uniform speed */
}

.testimonial-column-lg {
  margin-top: 0; /* Removed stagger for uniform speed */
}

@keyframes smooth-scroll {
  0% { transform: translateY(0); }
  100% { transform: translateY(calc(-50%)); }
}

.animate-scroll {
  animation: smooth-scroll var(--scroll-duration, 20s) linear infinite;
  will-change: transform;
  backface-visibility: hidden;
}

.testimonial-column-inner {
  display: flex;
  flex-direction: column;
  padding: 0 4px; /* Small padding to prevent edge clipping */
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Enhanced testimonial cards with consistent light mode styling */
.testimonial-card {
  border: var(--card-border);
  border-radius: var(--card-border-radius);
  padding: var(--dia-space-10);
  box-shadow: var(--card-box-shadow);
  margin-bottom: var(--dia-space-4); /* Spacing between cards */
  width: calc(100% - 8px); /* Account for the padding in testimonial-column-inner */
  text-align: left;
  transition: var(--card-transition);
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background); /* Pure white for all testimonial cards */
}

.testimonial-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

/* Testimonial text - using card content tokens */
.testimonial-text {
  font-family: var(--font-family-inter);
  font-size: var(--dia-testimonial-text-size);
  font-weight: var(--dia-testimonial-text-weight);
  line-height: var(--dia-testimonial-text-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  color: var(--dia-color-text-secondary);
  margin-bottom: var(--dia-space-4);
  text-align: left;
}

/* Responsive adjustments for testimonial text */
@media (min-width: 768px) {
  .testimonial-text {
    font-size: var(--font-size-card-content-tablet);
  }
}

@media (min-width: 1920px) {
  .testimonial-text {
    font-size: var(--font-size-card-content-desktop);
  }
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--dia-space-6);
}

.testimonial-author-image {
  width: 6rem;
  height: 6rem;
  border-radius: var(--radius-md);
  overflow: hidden;
  flex-shrink: 0;
}

.testimonial-author-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-author-info {
  flex: 1;
}

.testimonial-author-name {
  font-family: var(--font-family-inter);
  font-size: var(--dia-testimonial-author-size);
  font-weight: var(--dia-testimonial-author-weight);
  line-height: var(--dia-testimonial-author-line-height);
  color: var(--dia-color-text-secondary);
}

/* Responsive scaling handled by diabrowser tokens */

.testimonial-author-role {
  font-family: var(--font-family-inter);
  font-size: var(--dia-testimonial-role-size);
  font-weight: var(--dia-testimonial-role-weight);
  line-height: var(--dia-testimonial-role-line-height);
  color: var(--dia-color-text-light);
}

/* Responsive adjustments for author role */
@media (min-width: 768px) {
  .testimonial-author-role {
    font-size: var(--dia-testimonial-role-size);
  }
}

@media (min-width: 1920px) {
  .testimonial-author-role {
    font-size: var(--dia-testimonial-role-size);
  }
}

/* Handle different screen sizes */
@media (max-width: var(--breakpoint-desktop)) {
  .testimonials-columns-wrapper {
    gap: var(--item-spacing-16);
  }
  
  .testimonial-column {
    width: 280px;
  }
}

@media (max-width: var(--breakpoint-tablet)) {
  .testimonials-columns-wrapper {
    /* Show only 2 columns on tablet */
    justify-content: center;
    gap: var(--item-spacing-24);
  }
  
  .testimonial-column-lg {
    display: none; /* Hide the third column on tablet */
  }
  
  .testimonial-column {
    width: 280px;
    flex: 0 0 46%;
    height: 520px; /* Adjusted height for tablet */
  }
  
  /* Reset staggered positioning on tablet */
  .testimonial-column-md {
    margin-top: 0;
  }
}

@media (max-width: var(--breakpoint-mobile)) {
  .testimonials-columns-wrapper {
    /* Show only 1 column on mobile */
    flex-direction: column;
    align-items: center;
  }
  
  .testimonial-column-md {
    display: none; /* Hide the second column on mobile */
  }
  
  .testimonial-column {
    width: 100%;
    max-width: 320px;
    height: 480px; /* Slightly shorter on mobile */
  }
  
  .testimonial-card {
    padding: var(--item-spacing-20);
  }
  
  .testimonial-text {
    font-size: var(--font-size-p-mobile);
    line-height: calc(var(--font-size-p-mobile) * 1.5);
  }
  
  .testimonial-author-name {
    font-size: var(--font-size-small-mobile);
    line-height: calc(var(--font-size-small-mobile) * 1.4);
  }
}
