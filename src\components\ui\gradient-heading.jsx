import React from "react"
import { Slot } from "@radix-ui/react-slot"
import "./logo-carousel.css"

/**
 * GradientHeading component that displays text with a gradient background.
 * Adapted to use standard CSS classes instead of Tailwind.
 * 
 * @param {Object} props - The props for the GradientHeading component
 * @param {boolean} props.asChild - Whether the component should render its child as the root
 * @param {('default'|'pink'|'light'|'secondary')} props.variant - The variant of the gradient
 * @param {('default'|'xxs'|'xs'|'sm'|'md'|'lg'|'xl'|'xxl'|'xxxl')} props.size - The size of the heading
 * @param {('default'|'thin'|'base'|'semi'|'bold'|'black')} props.weight - The font weight of the heading
 * @param {string} props.className - Additional className to apply
 * @param {React.ReactNode} props.children - The content of the heading
 */
const GradientHeading = React.forwardRef(
  ({ asChild, variant = "default", size = "md", weight = "bold", className = "", children, ...props }, ref) => {
    const Comp = asChild ? Slot : "h3" // default to 'h3' if not a child
    
    // Build class names from props
    const classes = [
      "gradient-heading",
      `gradient-heading-${variant}`,
      `heading-${size}`,
      `font-${weight}`,
      className
    ].filter(Boolean).join(" ");
    
    return (
      <Comp ref={ref} {...props} className={className}>
        <span className={classes}>
          {children}
        </span>
      </Comp>
    )
  }
)

GradientHeading.displayName = "GradientHeading"

// Export the component
export { GradientHeading }
