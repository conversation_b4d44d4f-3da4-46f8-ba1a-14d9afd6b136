import React, { useEffect } from 'react';
import ReactD<PERSON> from 'react-dom';
import './AIDiscoveryModal.css';

/**
 * AIDiscoveryModal component - Full-screen overlay modal for AI Discovery service details
 * Features:
 * - Full-screen overlay covering entire viewport
 * - Responsive layout: horizontal split (desktop/tablet) vs vertical stack (mobile)
 * - Left/Top section: Overview content
 * - Right/Bottom section: Features list
 * - Click overlay background, ESC key, or close button to close
 * - Smooth open/close animations
 * - Uses diabrowser design tokens for consistency
 */
const AIDiscoveryModal = ({ 
  isOpen, 
  onClose, 
  serviceName = "AI Discovery"
}) => {
  // Handle escape key, body scroll lock, and cleanup
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Lock body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // Restore body scroll when modal closes
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click to close
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const modalContent = (
    <div 
      className="ai-discovery-modal-backdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="ai-discovery-modal-title"
    >
      <div className="ai-discovery-modal-panel">
        {/* Overview Section */}
        <div className="ai-discovery-modal-overview">
          <div className="ai-discovery-modal-content">
            <h2 id="ai-discovery-modal-title" className="ai-discovery-modal-title">
              AI Discovery
            </h2>
            <h3 className="ai-discovery-modal-subtitle">
              Get ahead of the next shift in consumer search
            </h3>
            <div className="ai-discovery-modal-description">
              <p>
                Generative answer engines are reshaping how shoppers find products. We position your brand inside Google AI Overviews, Search Generative Experience pilots and chat-powered shopping surfaces.
              </p>
              <p>
                Structured feeds, conversational keywords and margin-aware bidding make sure you capture intent the moment AI suggests a solution—and you pay only for clicks that meet your true profit threshold.
              </p>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="ai-discovery-modal-features">
          <div className="ai-discovery-modal-content">
            <h3 className="ai-discovery-modal-features-title">What's Included</h3>
            <div className="ai-discovery-modal-features-list">
              <div className="ai-discovery-modal-feature-item">
                <span className="ai-discovery-modal-checkmark">✓</span>
                <span>AI-Optimized Content Strategy</span>
              </div>
              <div className="ai-discovery-modal-feature-item">
                <span className="ai-discovery-modal-checkmark">✓</span>
                <span>Structured Data Implementation</span>
              </div>
              <div className="ai-discovery-modal-feature-item">
                <span className="ai-discovery-modal-checkmark">✓</span>
                <span>Conversational Keyword Research</span>
              </div>
              <div className="ai-discovery-modal-feature-item">
                <span className="ai-discovery-modal-checkmark">✓</span>
                <span>AI Overview Optimization</span>
              </div>
              <div className="ai-discovery-modal-feature-item">
                <span className="ai-discovery-modal-checkmark">✓</span>
                <span>Margin-Aware Bidding Setup</span>
              </div>
              <div className="ai-discovery-modal-feature-item">
                <span className="ai-discovery-modal-checkmark">✓</span>
                <span>AI Performance Monitoring</span>
              </div>
            </div>
          </div>
        </div>

        {/* Close Button */}
        <button
          className="ai-discovery-modal-close"
          onClick={onClose}
          aria-label="Close modal"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
  );

  // Render modal to document.body using React Portal
  return ReactDOM.createPortal(modalContent, document.body);
};

export default AIDiscoveryModal;
