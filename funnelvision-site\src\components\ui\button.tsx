import React from "react";
import { cn } from "../../lib/utils";

// Button variants using Tailwind classes with FunnelVision design system
const buttonVariants = {
  default: "bg-primary text-white hover:bg-primary-hover transition-colors",
  secondary: "bg-background-cta-secondary text-text-primary hover:bg-gray-200 transition-colors",
  ghost: "bg-transparent hover:bg-gray-100 text-text-primary transition-colors",
  link: "bg-transparent underline-offset-4 hover:underline text-primary transition-colors",
  cta: "bg-primary text-white hover:bg-black transition-colors", // Dark CTAs become black on hover
};

// Button sizes using diabrowser spacing
const buttonSizes = {
  default: "h-12 px-6 py-3", // Using diabrowser spacing
  sm: "h-10 px-4 py-2",
  lg: "h-14 px-8 py-4",
};

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: keyof typeof buttonVariants;
  size?: keyof typeof buttonSizes;
  asChild?: boolean;
  href?: string;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({
  className,
  variant = "default",
  size = "default",
  asChild = false,
  children,
  href,
  ...props
}, ref) => {
  
  const baseClasses = "btn-base inline-flex items-center justify-center rounded-standard text-base font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50";
  
  if (href) {
    return (
      <a
        href={href}
        className={cn(
          baseClasses,
          buttonVariants[variant],
          buttonSizes[size],
          className
        )}
        {...(props as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {children}
      </a>
    );
  }
  
  return (
    <button
      ref={ref}
      className={cn(
        baseClasses,
        buttonVariants[variant],
        buttonSizes[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
});

Button.displayName = "Button";

export { Button, buttonVariants };
