/* Import testimonials column styles */
@import "./ui/testimonials-column.css";

/* Testimonials Section - Standalone Section */
.testimonials-section {
  padding: var(--dia-space-14) 0;
  background-color: var(--dia-color-background);
  width: 100%;
}

/* Testimonials uses .dia-container-cards for testimonial card content */

.testimonials-content .section-subheading {
  text-align: center;
  margin-bottom: var(--dia-space-12);
}

/* Responsive adjustments */
@media (max-width: 800px) {
  .testimonials-section {
    padding: var(--dia-space-12) 0;
  }
  
  /* Container padding handled by standard .container class */
  
  .testimonials-content .section-subheading {
    margin-bottom: var(--dia-space-10);
  }
}

@media (max-width: 480px) {
  .testimonials-section {
    padding: var(--dia-space-10) 0;
  }
  
  .testimonials-content {
    padding: 0 var(--dia-space-3);
  }
}
