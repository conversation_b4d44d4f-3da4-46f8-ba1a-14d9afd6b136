/* Case Study Modal Styles */

/* Backdrop Layer - Full-screen overlay with no constraints */
.case-study-modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

/* Panel Layer - Content container with constraints and styling */
.case-study-modal-panel {
  width: 95%;
  max-width: 1400px;
  max-height: 95vh;
  background-color: var(--dia-color-background-light);
  border-radius: 30px;
  overflow: hidden;
  display: flex;
  position: relative;
  animation: slideIn 0.4s ease-out;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Content section - Left side on desktop/tablet */
.case-study-modal-content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--dia-color-background-light);
  order: 1; /* Content appears on left on desktop/tablet */
}

.case-study-modal-content-inner {
  padding: var(--dia-space-14); /* 80px */
  max-width: 800px;
  margin: 0 auto;
}

/* Header section */
.case-study-modal-header {
  margin-bottom: var(--dia-space-12); /* 50px */
}

.case-study-modal-logo {
  margin-bottom: var(--dia-space-8); /* 20px */
}

.case-study-modal-logo img {
  max-height: 60px;
  max-width: 200px;
  object-fit: contain;
}

/* Bunkie Life logo - Ensure original colors are preserved */
.case-study-modal-logo img[src="/images/logos/14.png"] {
  filter: brightness(1) invert(0) !important; /* Keep original colors */
}

.case-study-modal-category {
  font-size: var(--dia-font-size-xl); /* 20px */
  font-weight: var(--dia-font-weight-medium); /* 500 */
  color: var(--dia-color-text-primary);
  margin: 0;
  line-height: 1.3;
}

/* Body content */
.case-study-modal-body {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-12); /* 50px between sections */
}

.case-study-modal-section {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-8); /* 20px */
}

.case-study-modal-section-title {
  font-size: var(--dia-font-size-xl); /* 20px */
  font-weight: var(--dia-font-weight-semibold); /* 600 */
  color: var(--dia-color-text-primary);
  margin: 0;
  line-height: 1.3;
}

.case-study-modal-section-content {
  display: flex;
  flex-direction: column;
  gap: var(--dia-space-6); /* 16px between paragraphs */
}

.case-study-modal-paragraph {
  font-size: var(--dia-font-size-base); /* 16px */
  font-weight: var(--dia-font-weight-normal); /* 400 */
  color: var(--dia-color-text-primary);
  line-height: 1.6;
  margin: 0;
}

/* Table styles */
.case-study-modal-table-container {
  overflow-x: auto;
  margin: var(--dia-space-8) 0; /* 20px top/bottom */
}

.case-study-modal-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--dia-color-white);
  border-radius: var(--dia-space-4); /* 12px */
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.case-study-modal-table th,
.case-study-modal-table td {
  padding: var(--dia-space-6) var(--dia-space-8); /* 16px 20px */
  text-align: left;
  border-bottom: 1px solid var(--dia-color-grey);
}

.case-study-modal-table th {
  background-color: var(--dia-color-dark-grey);
  font-weight: var(--dia-font-weight-semibold); /* 600 */
  font-size: var(--dia-font-size-sm); /* 14px */
  color: var(--dia-color-text-primary);
}

.case-study-modal-table td {
  font-size: var(--dia-font-size-base); /* 16px */
  color: var(--dia-color-text-primary);
}

/* Testimonial styles */
.case-study-modal-testimonial {
  font-size: var(--dia-font-size-lg); /* 18px */
  font-style: italic;
  color: var(--dia-color-text-primary);
  margin: var(--dia-space-8) 0; /* 20px top/bottom */
  padding: var(--dia-space-8); /* 20px */
  background-color: var(--dia-color-dark-grey);
  line-height: 1.5;
}

/* Image section - Right side on desktop/tablet */
.case-study-modal-image-section {
  flex: 1;
  position: relative;
  min-height: 100vh;
  order: 2; /* Image appears on right on desktop/tablet */
}

.case-study-modal-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: var(--dia-space-11); /* 40px */
}

/* Close button - Positioned in bottom-right corner of modal */
.case-study-modal-close {
  position: absolute;
  bottom: var(--dia-space-11); /* 40px from bottom */
  right: var(--dia-space-11); /* 40px from right */
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--dia-color-white);
  border: 2px solid var(--dia-color-grey);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--dia-color-text-primary);
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10; /* Ensure it's above other content */
}

.case-study-modal-close:hover {
  background-color: var(--dia-color-dark-grey);
  border-color: var(--dia-color-text-primary);
  transform: scale(1.05);
}

/* Tablet responsive (768px - 999px) */
@media (max-width: 999px) and (min-width: 768px) {
  .case-study-modal-panel {
    border-radius: 20px; /* Smaller radius on tablet */
  }

  .case-study-modal-content-inner {
    padding: var(--dia-space-12); /* 50px */
  }

  .case-study-modal-header {
    margin-bottom: var(--dia-space-10); /* 30px */
  }

  .case-study-modal-body {
    gap: var(--dia-space-10); /* 30px between sections */
  }

  .case-study-modal-close {
    bottom: var(--dia-space-10); /* 30px from bottom on tablet */
    right: var(--dia-space-10); /* 30px from right on tablet */
    width: 52px;
    height: 52px;
  }
}

/* Mobile responsive (<768px) - Vertical stack layout */
@media (max-width: 767px) {
  .case-study-modal-panel {
    flex-direction: column; /* Stack vertically on mobile */
    border-radius: 20px; /* Smaller radius on mobile */
    width: 95%;
    max-height: 95vh;
  }

  .case-study-modal-image-section {
    order: 1; /* Image appears first */
    flex: 0 0 45vh; /* Fixed height, no grow/shrink */
    min-height: 45vh;
  }

  .case-study-modal-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Prevent distortion */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .case-study-modal-content {
    order: 2; /* Text appears second */
    flex: 1 1 auto; /* Fill remaining space */
    overflow-y: auto; /* Enable scrolling */
  }

  .case-study-modal-close {
    bottom: var(--dia-space-8); /* 20px from bottom on mobile */
    right: var(--dia-space-8); /* 20px from right on mobile */
    width: 48px;
    height: 48px;
  }
  
  .case-study-modal-content-inner {
    padding: var(--dia-space-10); /* 30px */
  }
  
  .case-study-modal-header {
    margin-bottom: var(--dia-space-8); /* 20px */
  }
  
  .case-study-modal-body {
    gap: var(--dia-space-8); /* 20px between sections */
  }
  
  .case-study-modal-logo img {
    max-height: 40px;
    max-width: 150px;
  }
}

/* Small mobile responsive (<480px) */
@media (max-width: 479px) {
  .case-study-modal-panel {
    border-radius: 16px; /* Even smaller radius on small mobile */
    width: 95%;
    max-height: 95vh;
  }

  .case-study-modal-content-inner {
    padding: var(--dia-space-8); /* 20px */
  }

  .case-study-modal-close {
    bottom: var(--dia-space-6); /* 16px from bottom on small mobile */
    right: var(--dia-space-6); /* 16px from right on small mobile */
    width: 44px;
    height: 44px;
  }
  
  .case-study-modal-category {
    font-size: var(--dia-font-size-lg); /* 18px */
  }
  
  .case-study-modal-section-title {
    font-size: var(--dia-font-size-lg); /* 18px */
  }
  
  .case-study-modal-paragraph {
    font-size: var(--dia-font-size-sm); /* 14px */
  }
}
