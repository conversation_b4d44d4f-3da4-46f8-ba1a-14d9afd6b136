import React from "react";
import { GradientHeading } from "./ui/gradient-heading";
import { LogoCarousel } from "./ui/logo-carousel";
import { sampleLogos } from "./ui/logo-icons";
import "./LogoCarouselSection.css";

/**
 * LogoCarouselSection component that displays a section with a heading and logo carousel
 * This can be integrated into any page by importing and using this component
 */
function LogoCarouselSection() {
  return (
    <div className="logo-carousel-section">
      <div className="logo-carousel-container">
        <GradientHeading 
          variant="pink" 
          size="lg"
          className="logo-carousel-heading"
        >
          Our Partners
        </GradientHeading>
        
        <p className="logo-carousel-description">
          FunnelVision collaborates with industry-leading companies to deliver exceptional solutions.
        </p>
        
        <div className="logo-carousel-wrapper">
          <LogoCarousel 
            columnCount={3} 
            logos={sampleLogos} 
          />
        </div>
      </div>
    </div>
  );
}

export default LogoCarouselSection;
