import React, { useRef, useState, useContext, createContext, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useOutsideClick } from "../../hooks/useOutsideClick";
import "./FunnelVisionCardsCarousel.css";

// Utility function to join classnames (simplified version of cn)
const classNames = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// Context for carousel state management
const CarouselContext = createContext({
  activeCardIndex: 0,
  setActiveCardIndex: () => {},
  scrollToCard: () => {},
});

// Hook to use carousel context
const useCarousel = () => {
  const context = useContext(CarouselContext);
  if (!context) {
    throw new Error("useCarousel must be used within a CarouselContextProvider");
  }
  return context;
};

/**
 * Card component for the FunnelVision Cards Carousel
 */
export const Card = React.memo(({ card, index }) => {
  const { category, title, src } = card;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRevealed, setIsRevealed] = useState(false);
  const modalRef = useRef(null);
  const cardRef = useRef(null);
  const { activeCardIndex, setActiveCardIndex, scrollToCard } = useCarousel();
  
  // Close modal when clicked outside
  useOutsideClick(modalRef, () => {
    if (isModalOpen) setIsModalOpen(false);
  });

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (isModalOpen && event.key === "Escape") {
        setIsModalOpen(false);
      }
    };

    window.addEventListener("keydown", handleEscapeKey);
    return () => window.removeEventListener("keydown", handleEscapeKey);
  }, [isModalOpen]);

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isModalOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isModalOpen]);

  // Open modal and set active card
  const openModal = () => {
    setIsModalOpen(true);
    setActiveCardIndex(index);
  };

  // Close modal and scroll back to card
  const closeModal = () => {
    setIsModalOpen(false);
    // Small timeout to let the modal close animation finish
    setTimeout(() => {
      scrollToCard(index);
    }, 300);
  };

  // Toggle the reveal state
  const toggleReveal = (e) => {
    e.stopPropagation();
    setIsRevealed(!isRevealed);
  };

  // Close outside click handler for revealed state
  useOutsideClick(cardRef, () => {
    if (isRevealed) setIsRevealed(false);
  });

  return (
    <>
      <motion.div
        className={`funnelvision-carousel-card ${isRevealed ? 'card-revealed' : ''}`}
        onClick={toggleReveal}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: index * 0.1 }}
        ref={cardRef}
      >
        <div className="funnelvision-carousel-card-image">
          <img src={src} alt={title} />
        </div>
        <div className="funnelvision-carousel-card-content">
          <div className="funnelvision-carousel-card-tag">{category}</div>
          <h3 className="funnelvision-carousel-card-title">{title}</h3>
          
          {/* Toggle button */}
          <button 
            className="card-toggle-button" 
            onClick={toggleReveal}
            aria-label="Show more details"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
        
        {/* Overlay for revealed state */}
        {isRevealed && (
          <div className="card-overlay">
            <div className="card-overlay-content">
              <h3 className="card-overlay-title">{title}</h3>
              
              {card.expandedContent ? (
                <div className="card-expanded-content">
                  {card.expandedContent}
                </div>
              ) : (
                <p className="card-overlay-description">
                  {`${title} provides detailed insights and actionable strategies to improve your marketing ROI. Our data-driven approach ensures you're making informed decisions based on real customer behavior.`}
                </p>
              )}
              
              <a href="#get-started" className="card-overlay-cta" onClick={(e) => e.stopPropagation()}>
                Get started
                <svg 
                  viewBox="0 0 16 16" 
                  fill="currentColor" 
                  className="overlay-arrow"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M8 2L7.3 2.7L11.6 7H2.5V8H11.6L7.3 12.3L8 13L13.5 7.5L8 2Z" />
                </svg>
              </a>
            </div>
            <button 
              className="card-toggle-close" 
              onClick={toggleReveal}
              aria-label="Close details"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          </div>
        )}
      </motion.div>

      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            className="funnelvision-carousel-modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              className="funnelvision-carousel-modal"
              ref={modalRef}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 50 }}
              transition={{ duration: 0.3, type: "spring" }}
            >
              <div className="funnelvision-carousel-modal-header">
                <h2 className="funnelvision-carousel-modal-title">{title}</h2>
                <button
                  className="funnelvision-carousel-modal-close"
                  onClick={closeModal}
                  aria-label="Close modal"
                >
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 6L6 18M6 6L18 18"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
              <div className="funnelvision-carousel-modal-body">
                {card.content}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
});

/**
 * Main Carousel component
 */
export function Carousel({ items }) {
  const [activeCardIndex, setActiveCardIndex] = useState(0);
  const trackRef = useRef(null);
  
  // Function to scroll to a specific card
  const scrollToCard = useCallback((index) => {
    if (trackRef.current) {
      const cards = trackRef.current.children;
      if (cards[index]) {
        const card = cards[index];
        const trackRect = trackRef.current.getBoundingClientRect();
        const cardRect = card.getBoundingClientRect();
        
        // Calculate scroll position to center the card
        const scrollLeft = cardRect.left - trackRect.left - (trackRect.width - cardRect.width) / 2;
        
        // Smooth scroll to position
        trackRef.current.scrollTo({
          left: scrollLeft,
          behavior: "smooth",
        });
      }
    }
  }, []);

  // Navigation handlers
  const handlePrevClick = () => {
    const newIndex = Math.max(0, activeCardIndex - 1);
    setActiveCardIndex(newIndex);
    scrollToCard(newIndex);
  };

  const handleNextClick = () => {
    const newIndex = Math.min(items.length - 1, activeCardIndex + 1);
    setActiveCardIndex(newIndex);
    scrollToCard(newIndex);
  };

  // Provide carousel context
  const contextValue = {
    activeCardIndex,
    setActiveCardIndex,
    scrollToCard,
  };

  return (
    <CarouselContext.Provider value={contextValue}>
      <div className="funnelvision-carousel-container">
        <div className="funnelvision-carousel-content">
          <div
            className="funnelvision-carousel-track"
            ref={trackRef}
            style={{
              overflowX: "auto",
              scrollbarWidth: "none", // Firefox
              msOverflowStyle: "none", // IE/Edge
              WebkitOverflowScrolling: "touch",
            }}
            onScroll={(e) => {
              e.preventDefault();
            }}
          >
            {items}
          </div>
          
          <button
            onClick={handlePrevClick}
            className={classNames(
              "funnelvision-carousel-nav-button left",
              activeCardIndex === 0 ? "opacity-50" : ""
            )}
            disabled={activeCardIndex === 0}
            aria-label="Previous card"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 18L9 12L15 6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          
          <button
            onClick={handleNextClick}
            className={classNames(
              "funnelvision-carousel-nav-button right",
              activeCardIndex === items.length - 1 ? "opacity-50" : ""
            )}
            disabled={activeCardIndex === items.length - 1}
            aria-label="Next card"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 6L15 12L9 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </CarouselContext.Provider>
  );
}
