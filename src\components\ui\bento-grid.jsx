import React from "react";
import PropTypes from "prop-types";

// Utility function for conditional class names
function cn(...classes) {
  return classes.filter(Boolean).join(" ");
}

/**
 * BentoGrid component - Responsive grid layout for bento cards
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components to render in the grid
 * @param {string} [props.className] - Additional CSS class names
 */
const BentoGrid = ({
  children,
  className,
}) => {
  return (
    <div
      className={cn(
        "bento-grid",
        className,
      )}
    >
      {children}
    </div>
  );
};

/**
 * BentoCard component - Card with background, icon, and hover effect
 * @param {Object} props
 * @param {string} props.name - Title of the card
 * @param {string} props.className - CSS class names for styling
 * @param {React.ReactNode} props.background - Background content (usually gradient)
 * @param {Function} props.Icon - Icon component to render
 * @param {string} props.description - Card description text
 * @param {string} props.href - Link destination
 * @param {string} props.cta - Call to action text
 */
const BentoCard = ({
  name,
  className,
  background,
  Icon,
  description,
  href,
  cta = "Learn more",
}) => (
  <div
    key={name}
    className={cn(
      "bento-card group relative",
      className,
    )}
  >
    <div className="bento-content">
      <div>
        <div className="bento-icon-container">
          <Icon />
        </div>
        <h3 className="bento-title">{name}</h3>
        <p className="bento-description">{description}</p>
      </div>
      {href && (
        <div className="bento-footer">
          <a href={href} className="bento-cta">
            {cta}
            <svg
              viewBox="0 0 16 16"
              fill="currentColor"
              className="bento-arrow"
            >
              <path d="M8 2L7.3 2.7L11.6 7H2.5V8H11.6L7.3 12.3L8 13L13.5 7.5L8 2Z" />
            </svg>
          </a>
        </div>
      )}
    </div>
  </div>
);

/**
 * NumberedBentoCard component - Card with number instead of icon
 * @param {Object} props
 * @param {string} props.title - Title of the card
 * @param {string} props.className - CSS class names for styling
 * @param {number} props.number - Step number to display
 * @param {string} props.description - Card description text
 */
const NumberedBentoCard = ({
  title,
  className,
  number,
  description,
}) => (
  <div
    key={title}
    className={cn(
      "bento-card numbered-bento-card group relative",
      className,
    )}
  >
    <div className="bento-content">
      <div>
        <div className="bento-number-container">
          <span className="bento-number">{number}</span>
        </div>
        <h3 className="bento-title">{title}</h3>
        <p className="bento-description">{description}</p>
      </div>
    </div>
  </div>
);

BentoGrid.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

BentoCard.propTypes = {
  name: PropTypes.string.isRequired,
  className: PropTypes.string,
  background: PropTypes.node,
  Icon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,
  description: PropTypes.string.isRequired,
  href: PropTypes.string,
  cta: PropTypes.string, // Optional since it has a default value
};

NumberedBentoCard.propTypes = {
  title: PropTypes.string.isRequired,
  className: PropTypes.string,
  number: PropTypes.number.isRequired,
  description: PropTypes.string.isRequired,
};

export { BentoCard, BentoGrid, NumberedBentoCard };
