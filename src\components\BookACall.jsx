import React, { useEffect } from 'react';
import HeaderMinimal from './HeaderMinimal';
import './BookACall.css';

const BookACall = () => {
  // Load Calendly script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://assets.calendly.com/assets/external/widget.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      // Cleanup script on unmount
      const existingScript = document.querySelector('script[src="https://assets.calendly.com/assets/external/widget.js"]');
      if (existingScript) {
        document.body.removeChild(existingScript);
      }
    };
  }, []);

  return (
    <div className="book-a-call-page">
      {/* Header with logo only */}
      <HeaderMinimal />
      
      {/* Main Content */}
      <main className="book-a-call-main">
        <div className="dia-container-text">
          {/* Section Heading and Description */}
          <div className="book-a-call-header">
            <h1 className="book-a-call-title">
              Book a profit-clarity call with a FunnelVision director.
            </h1>
            <p className="book-a-call-description">
              Hands-on growth partners for 7–9 figure DTC brands ready to jump from solid revenue to predictable PAX-driven profit.
            </p>
          </div>
          
          {/* Calendly Widget Card */}
          <div className="book-a-call-card-container">
            <div className="book-a-call-card">
              {/* Calendly inline widget begin */}
              <div
                className="calendly-inline-widget"
                data-url="https://calendly.com/funnelvisionagency/intro-call-full"
                style={{minWidth: '320px'}}
              ></div>
              {/* Calendly inline widget end */}
            </div>
          </div>
        </div>
      </main>
      
      {/* Simplified Footer */}
      <footer className="book-a-call-footer">
        <div className="dia-container-wide">
          <div className="footer-content">
            <div className="footer-left">
              <span className="footer-copyright">© FunnelVision 2025</span>
            </div>
            <div className="footer-right">
              <a href="/privacy" className="footer-link">Privacy</a>
              <a href="/terms" className="footer-link">Terms & Conditions</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BookACall;
