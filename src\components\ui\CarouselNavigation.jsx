import React from 'react';

/**
 * CarouselNavigation component - Handles dots, dash, and arrow navigation
 * @param {Object} props
 * @param {number} props.currentSlide - Current active slide index
 * @param {number} props.slideCount - Total number of slides
 * @param {number} props.progress - Timer progress (0-100)
 * @param {Function} props.onSlideChange - Callback for slide change
 * @param {Function} props.onNext - Callback for next slide
 * @param {Function} props.onPrev - Callback for previous slide
 */
const CarouselNavigation = ({
  currentSlide,
  slideCount,
  progress,
  onSlideChange,
  onNext,
  onPrev
}) => {
  // Generate navigation items (dots and dash)
  const navigationItems = [];
  
  for (let i = 0; i < slideCount; i++) {
    if (i === currentSlide) {
      // Active slide - show dash with timer
      navigationItems.push(
        <div key={i} className="carousel-nav-dash-container">
          <div className="carousel-nav-dash">
            <div 
              className="carousel-nav-dash-fill"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      );
    } else {
      // Inactive slide - show dot
      navigationItems.push(
        <button
          key={i}
          className="carousel-nav-dot"
          onClick={() => onSlideChange(i)}
          aria-label={`Go to slide ${i + 1}`}
        />
      );
    }
  }

  return (
    <div className="carousel-navigation">
      {/* Left Arrow */}
      <button
        className="carousel-nav-arrow carousel-nav-arrow-left"
        onClick={onPrev}
        aria-label="Previous slide"
      >
        <svg 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            d="M15 18L9 12L15 6" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          />
        </svg>
      </button>

      {/* Dots and Dash Navigation */}
      <div className="carousel-nav-indicators">
        {navigationItems}
      </div>

      {/* Right Arrow */}
      <button
        className="carousel-nav-arrow carousel-nav-arrow-right"
        onClick={onNext}
        aria-label="Next slide"
      >
        <svg 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            d="M9 6L15 12L9 18" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          />
        </svg>
      </button>
    </div>
  );
};

export default CarouselNavigation;
