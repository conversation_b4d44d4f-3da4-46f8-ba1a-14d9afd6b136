import React from 'react';
import FadeIn from './animations/FadeIn';
import './NextStepsSection.css';

const NextStepsSection: React.FC = () => (
  <div className="next-steps-content">
    <div className="dia-container-cards">
      <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
        <div className="nextsteps-header">
          <h2 className="section-subheading text-center">Get Started</h2>
          <div className="section-description text-center">
            <p>
              Ready for a <span className="profit-text">profit-first approach</span> to search marketing?
            </p>
          </div>
        </div>
      </FadeIn>
      <FadeIn threshold={0.1} rootMargin="0px" className="fade-up">
        <div className="numbered-steps-container">
          <div className="numbered-step">
            <div className="step-number">1</div>
            <div className="step-content">
              <h3 className="step-title">Book Your Strategy Call</h3>
              <p className="step-description">
                Schedule a 30-minute profit analysis call to identify where your ad spend is bleeding money and discover quick-win opportunities.
              </p>
            </div>
          </div>
          <div className="numbered-step">
            <div className="step-number">2</div>
            <div className="step-content">
              <h3 className="step-title">Get Your Custom Roadmap</h3>
              <p className="step-description">
                Receive a detailed profit optimization plan showing exactly which campaigns to fix first and how to maximize your POAS.
              </p>
            </div>
          </div>
          <div className="numbered-step">
            <div className="step-number">3</div>
            <div className="step-content">
              <h3 className="step-title">Scale Profitably</h3>
              <p className="step-description">
                Work with our team to implement profit-first campaigns with monthly reporting on real profit metrics, not vanity stats.
              </p>
            </div>
          </div>
        </div>
      </FadeIn>
      <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
        <div className="cta-section-header">
          <h2 className="cta-section-title">Don't let your competitors steal another customer.</h2>
          <div className="cta-container">
            <a href="https://calendly.com/funnelvisionagency/intro-call" className="btn btn-primary button-hover">
              <span className="cta-text">Book Your Free Strategy Call</span>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="cta-arrow">
                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor" />
              </svg>
            </a>
          </div>
        </div>
      </FadeIn>
    </div>
  </div>
);

export default NextStepsSection;
