import { useEffect } from 'react';

/**
 * Hook that alerts when you click outside of the specified element
 * @param ref - React ref object for the element to monitor clicks outside of
 * @param handler - Callback function to execute when click outside is detected
 */
export const useOutsideClick = (
  ref: React.RefObject<HTMLElement | null>,
  handler: () => void
): void => {
  useEffect(() => {
    /**
     * Alert if clicked on outside of element
     */
    function handleClickOutside(event: MouseEvent) {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    }

    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler]);
};
