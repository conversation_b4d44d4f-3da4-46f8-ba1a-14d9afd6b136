.faq-section {
  padding: var(--dia-space-14) 0;
  background-color: var(--color-background-secondary);
  position: relative;
  overflow: hidden;
}

/* Diabrowser migration: when .dia-typography and .dia-spacing are applied */
.faq-section.dia-typography.dia-spacing {
  padding: var(--dia-space-12) 0;
  background-color: var(--dia-color-background);
}

/* Removed radial gradient backgrounds */

.faq-header {
  text-align: center;
  margin-bottom: var(--dia-space-12);
}

/* Diabrowser migration overrides */
.faq-section.dia-spacing .faq-header {
  margin-bottom: var(--dia-space-12);
}

.faq-section.dia-spacing .faq-item {
  padding: var(--dia-space-6) var(--dia-space-4);
  margin-bottom: var(--dia-space-3);
}

.faq-section.dia-spacing .faq-answer.visible {
  margin-top: var(--dia-space-6);
}

/* Note: faq-title uses section-heading class styles instead of direct CSS */

.faq-description {
  /* Using section-description class styles */
  /* Container width handled by dia-container-text */
  margin: 0 auto var(--dia-space-6);
}

.faq-accordion {
  /* Remove custom max-width - let dia-container-text handle it */
  border-radius: var(--card-border-radius);
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
  padding: var(--dia-space-6);
  width: 100%;
}

.faq-item {
  padding: var(--dia-space-6) var(--dia-space-6); /* Match testimonial card generous padding */
  cursor: pointer;
  background: var(--card-background-gradient);
  background-color: var(--dia-color-background-light); /* Pure white for FAQ cards */
  border-radius: var(--card-border-radius);
  margin-bottom: var(--dia-space-4); /* Increase spacing between items like testimonials */
  border: var(--card-border);
  box-shadow: var(--card-box-shadow);
  transition: var(--card-transition);
}

.faq-item:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-question h3 {
  font-family: var(--font-family-inter);
  font-size: var(--card-accordion-title-mobile);
  font-weight: var(--card-accordion-title-weight);
  line-height: var(--line-height-snug);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--dia-color-text-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  margin: 0;
  padding-right: var(--dia-space-6); /* Reduce right padding since we have more overall padding */
}

@media (min-width: 768px) {
  .faq-question h3 {
    font-size: var(--card-accordion-title-tablet);
  }
}

@media (min-width: 1920px) {
  .faq-question h3 {
    font-size: var(--card-accordion-title-desktop);
  }
}

.faq-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  color: var(--color-text-secondary);
  transition: transform var(--transition-base);
}

.faq-item.open {
  background: var(--card-elevated-background-gradient);
  background-color: var(--dia-color-card-background-elevated);
  border-color: var(--dia-color-card-border-hover);
  box-shadow: var(--card-hover-box-shadow);
}

.faq-item.open .faq-icon {
  color: var(--color-accent-primary);
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease, margin 0.3s ease;
  opacity: 0;
  margin-top: 0;
}

.faq-answer.visible {
  max-height: 500px;
  opacity: 1;
  margin-top: var(--dia-space-6); /* Increase spacing between question and answer like testimonials */
}

.faq-answer p {
  font-family: var(--font-family-inter);
  font-size: var(--card-accordion-content-mobile);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-normal);
  color: var(--color-text-secondary);
  margin-bottom: 0; /* Remove bottom margin since we have container padding */
}

@media (min-width: 768px) {
  .faq-answer p {
    font-size: var(--card-accordion-content-tablet);
  }
}

@media (min-width: 1920px) {
  .faq-answer p {
    font-size: var(--card-accordion-content-desktop);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .faq-section {
    padding: var(--dia-space-8) 0;
  }

  /* Title and description now use content hierarchy classes with responsive tokens */

  /* Mobile styles now handled by mobile-first CSS with media queries */
  .faq-answer p {
    padding-right: var(--dia-space-2);
  }
}
