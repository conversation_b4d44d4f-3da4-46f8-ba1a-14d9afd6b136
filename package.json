{"name": "project-astra-landing", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "framer-motion": "^12.18.1", "intersection-observer": "^0.12.2", "lucide-react": "^0.513.0", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^6.1.18"}, "devDependencies": {"@eslint/js": "^8.57.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}}