import React, { useState, useEffect } from "react";

// Icon components for navigation items
const Icons = {
  Problem: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
      <polyline points="22 4 12 14.01 9 11.01"/>
    </svg>
  ),
  Solutions: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  ),
  OurWork: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <rect x="5" y="2" width="14" height="20" rx="2" ry="2"/>
      <line x1="12" y1="18" x2="12" y2="18"/>
    </svg>
  ),
  NextSteps: () => (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
    </svg>
  )
};

interface NavigationItem {
  id: string;
  label: string;
  icon: keyof typeof Icons;
}

const SecondaryScrollMenu: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [activeSection, setActiveSection] = useState("");
  const [headerVisible, setHeaderVisible] = useState(true);

  const navigationItems: NavigationItem[] = [
    { id: "problem", label: "Problem", icon: "Problem" },
    { id: "solutions", label: "Solutions", icon: "Solutions" },
    { id: "our-work", label: "Our Work", icon: "OurWork" },
    { id: "next-steps", label: "Get Started", icon: "NextSteps" },
  ];

  // Listen for header visibility changes
  useEffect(() => {
    const handleHeaderVisibilityChange = (event: CustomEvent<{ visible: boolean }>) => {
      setHeaderVisible(event.detail.visible);
    };
    
    window.addEventListener('headerVisibilityChange', handleHeaderVisibilityChange as EventListener);
    
    return () => {
      window.removeEventListener('headerVisibilityChange', handleHeaderVisibilityChange as EventListener);
    };
  }, []);
  
  useEffect(() => {
    const handleScroll = () => {
      // Get the Problem and FAQ sections positions
      const problemSection = document.getElementById('problem');
      const faqSection = document.getElementById('faq');
      const nextStepsSection = document.getElementById('next-steps');
      
      if (problemSection && faqSection && nextStepsSection) {
        const problemPosition = problemSection.getBoundingClientRect().top;
        const faqPosition = faqSection.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        
        // Make menu visible only when we've scrolled to or past the Problem section
        // AND we haven't reached the FAQ section yet
        if (problemPosition <= windowHeight * 0.25 && faqPosition >= 0) {
          setVisible(true);
        } else {
          setVisible(false);
        }
      }

      // Check which section is most visible
      const sectionIds = ["problem", "solutions", "our-work", "next-steps"];
      
      // Find all sections by their IDs
      const sections = sectionIds.map(id => {
        const element = document.getElementById(id);
        return { id, element };
      }).filter(item => item.element); // Filter out any not found

      // Determine which section is most visible in the viewport
      let mostVisibleSection: string | null = null;
      let maxVisiblePercent = 0;
      
      sections.forEach(({ id, element }) => {
        if (!element) return;
        
        const rect = element.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        
        // Calculate how much of the section is visible
        const visibleTop = Math.max(0, rect.top);
        const visibleBottom = Math.min(viewportHeight, rect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);
        
        // Calculate visible percentage relative to viewport
        const visiblePercent = visibleHeight / viewportHeight;
        
        if (visiblePercent > maxVisiblePercent) {
          maxVisiblePercent = visiblePercent;
          mostVisibleSection = id;
        }
      });
      
      if (mostVisibleSection) {
        setActiveSection(mostVisibleSection);
      }
    };

    // Call once on mount to initialize active section
    handleScroll();
    
    // Add scroll listener
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  
  // Scroll to a section smoothly
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      // Dynamically calculate offset based on header and navbar height
      const headerHeight = 64; // Header height in px
      const navbarHeight = 52; // Approximate navbar height with padding
      const scrollOffset = headerHeight + navbarHeight;
      
      window.scrollTo({
        top: element.offsetTop - scrollOffset,
        behavior: "smooth",
      });
      setActiveSection(sectionId);
    }
  };

  return (
    <div 
      className={`
        fixed left-1/2 transform -translate-x-1/2 w-auto max-w-[700px] z-[99]
        bg-header-background backdrop-blur-[10px] shadow-md transition-all duration-500
        flex items-center justify-center border border-card-border rounded-standard p-1
        pointer-events-auto will-change-[opacity,visibility,transform]
        ${visible ? 'opacity-100 visible flex' : 'opacity-0 invisible'}
        ${headerVisible ? 'top-[74px]' : 'top-[10px]'}
      `}
    >
      <div className="flex items-center justify-center w-auto h-full overflow-x-auto whitespace-nowrap gap-2 m-0 p-0 scrollbar-hide">
        {navigationItems.map((item) => {
          const IconComponent = Icons[item.icon];
          const isActive = activeSection === item.id;
          
          return (
            <button
              key={item.id}
              className={`
                relative h-8 px-4 py-1 border-none bg-transparent cursor-pointer
                transition-all duration-200 rounded-standard flex items-center justify-center
                m-0 whitespace-nowrap no-underline gap-2
                ${isActive 
                  ? 'text-text-primary bg-background-overlay transform -translate-y-0.5' 
                  : 'text-text-secondary hover:text-text-primary hover:bg-background-overlay hover:transform hover:-translate-y-0.5'
                }
              `}
              onClick={() => scrollToSection(item.id)}
            >
              <span className="text-secondary-nav-link-size font-nav-link-weight">
                {item.label}
              </span>
              <span className="flex items-center justify-center">
                <IconComponent />
              </span>
              
              {isActive && (
                <div className="absolute inset-0 pointer-events-none">
                  <div className="relative w-full h-full">
                    {/* Tubelight glow effects */}
                    <div className="absolute inset-0 bg-primary opacity-10 rounded-standard blur-sm"></div>
                    <div className="absolute inset-0 bg-primary opacity-5 rounded-standard blur-md"></div>
                    <div className="absolute inset-0 bg-primary opacity-3 rounded-standard blur-lg"></div>
                  </div>
                </div>
              )}
              
              {/* Underline effect for active state */}
              {isActive && (
                <div className="absolute bottom-0.5 left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 bg-primary rounded-full"></div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default SecondaryScrollMenu;
