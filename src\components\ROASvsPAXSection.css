/*
 * ROAS vs PAX Section - Fresh Implementation
 * Optimized for FunnelVision build with consistent light mode styling
 */

.roas-vs-pax-section {
  padding: var(--dia-space-14) 0;
  background-color: var(--dia-color-background);
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* Fresh container specifically for ROAS vs PAX section */
.roas-vs-pax-container {
  width: 100%;
  max-width: var(--width-1070);
  margin: 0 auto;
  padding: 0 var(--dia-space-6);
  box-sizing: border-box;
}

/* Section header styling */
.roas-vs-pax-header {
  text-align: center;
  margin-bottom: var(--dia-space-12);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.roas-vs-pax-header .section-subheading {
  margin-bottom: var(--dia-space-4);
}

.roas-vs-pax-header .section-description {
  margin-bottom: 0;
}

/* Comparison container with enhanced styling */
.roas-vs-pax-comparison {
  display: flex;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  position: relative;
}

/* Enhanced responsive design */
@media (min-width: 800px) {
  .roas-vs-pax-container {
    padding: 0 var(--dia-space-8);
  }
}

@media (min-width: 1000px) {
  .roas-vs-pax-container {
    padding: 0 var(--dia-space-10);
  }
}

@media (max-width: 800px) {
  .roas-vs-pax-section {
    padding: var(--dia-space-12) 0;
  }

  .roas-vs-pax-header {
    margin-bottom: var(--dia-space-10);
  }
}

@media (max-width: 480px) {
  .roas-vs-pax-section {
    padding: var(--dia-space-10) 0;
  }

  .roas-vs-pax-container {
    padding: 0 var(--dia-space-4);
  }

  .roas-vs-pax-header {
    margin-bottom: var(--dia-space-8);
  }
}
