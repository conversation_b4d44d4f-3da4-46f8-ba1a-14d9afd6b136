import React from "react";
import FadeIn from "./animations/FadeIn";
import ScaleIn from "./animations/ScaleIn";

const VideoIntroSection: React.FC = () => {
  return (
    <section className="section-base bg-background-elevated">
      <div className="container-base">
        {/* Video Card with Text Overlay */}
        <div className="relative">
          {/* Text Overlay */}
          <div className="absolute inset-0 z-10 flex flex-col justify-center items-center text-center text-white">
            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
              <h2 className="hero-secondary text-white mb-6">
                In under 5 minutes
              </h2>
            </FadeIn>
            
            <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-200">
              <p className="body-text text-white max-w-2xl mx-auto">
                I'll walk you through the only ad strategy that matters in 2025:
                One built to maximize your true profit per click.
              </p>
            </FadeIn>
          </div>
          
          {/* Video Card */}
          <ScaleIn className="delay-100">
            <div className="relative w-full h-[600px] bg-gradient-to-br from-primary to-primary-dark rounded-standard overflow-hidden flex items-center justify-center">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12"></div>
              </div>
              
              {/* Play Button */}
              <button className="relative z-20 w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300 hover:scale-110 group">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white ml-1 group-hover:scale-110 transition-transform"
                >
                  <path d="M8 5v14l11-7z" fill="currentColor" />
                </svg>
              </button>
              
              {/* Decorative Elements */}
              <div className="absolute top-10 left-10 w-4 h-4 bg-white bg-opacity-20 rounded-full"></div>
              <div className="absolute top-20 right-20 w-6 h-6 bg-white bg-opacity-15 rounded-full"></div>
              <div className="absolute bottom-20 left-20 w-3 h-3 bg-white bg-opacity-25 rounded-full"></div>
              <div className="absolute bottom-10 right-10 w-5 h-5 bg-white bg-opacity-10 rounded-full"></div>
            </div>
          </ScaleIn>
        </div>
        
        {/* Additional Content */}
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-300">
          <div className="text-center mt-12">
            <p className="body-text text-text-muted max-w-xl mx-auto">
              Watch how leading DTC brands are transforming their ad spend into predictable profit with our proven framework.
            </p>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default VideoIntroSection;
