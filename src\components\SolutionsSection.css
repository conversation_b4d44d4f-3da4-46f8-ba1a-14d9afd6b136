.blind-vision-section {
  padding: var(--dia-space-12) 0;
  background-color: var(--dia-color-background);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.blind-vision-header {
  text-align: center;
  margin-bottom: var(--dia-space-12);
  max-width: var(--width-843);
  margin-left: auto;
  margin-right: auto;
}

/* Remove custom title and description styles - now using content hierarchy classes */
/* .section-heading and .section-description styles are defined globally in content-hierarchy.css */

.blind-vision-header .section-description {
  max-width: var(--width-616);
  margin: 0 auto;
}

.blind-vision-large-text {
  max-width: var(--width-843);
  margin: 0 auto var(--dia-space-14) auto;
  text-align: center;
}

.blind-vision-large-text p {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-hero-secondary-size);
  font-weight: var(--dia-hero-secondary-weight);
  line-height: var(--dia-hero-secondary-line-height);
  letter-spacing: var(--dia-hero-secondary-spacing);
  margin: 0;
}

.blind-vision-features {
  display: flex;
  justify-content: center;
  margin-top: var(--dia-space-6);
  width: 100%;
  gap: var(--dia-space-6);
  margin-bottom: var(--dia-space-10);
  margin-left: auto;
  margin-right: auto;
}

.vision-features-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--dia-space-6);
  width: 100%;
  /* Remove custom max-width and padding - let container handle it */
  margin: var(--dia-space-4) auto 0 auto;
}

/* Responsive grid layout */
@media (min-width: 800px) {
  .vision-features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--dia-space-8);
  }
}

@media (min-width: 1000px) {
  .vision-features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--dia-space-10);
  }
}

.feature-card {
  width: 100%; /* Use full width within grid container */
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--card-background-gradient);
  background-color: var(--dia-color-card-background); /* Pure white for feature cards */
  border-radius: var(--card-border-radius);
  border: var(--card-border);
  overflow: hidden;
  padding-top: 0;
  padding-bottom: 0;
  box-shadow: var(--card-box-shadow);
  transition: var(--card-transition);
}

.feature-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

.feature-image {
  width: 100%;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-image-placeholder {
  font-size: 80px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-content {
  padding: var(--dia-space-3);
  text-align: center;
}

.feature-content p {
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-font-size-xl);
  font-weight: var(--dia-font-weight-normal);
  line-height: var(--dia-body-text-line-height);
  margin: 0;
  text-align: center;
}

.blind-vision-partnership {
  max-width: var(--width-843);
  margin: 0 auto var(--dia-space-10) auto;
  text-align: center;
}

/* Partnership heading now uses section-subheading class */

.blind-vision-cta-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--dia-space-5);
  max-width: var(--width-616);
  margin: 0 auto var(--dia-space-10) auto;
  text-align: center;
}

.blind-vision-cta-description p {
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  font-size: var(--dia-body-text-size);
  font-weight: var(--dia-body-text-weight);
  line-height: var(--dia-body-text-line-height);
  margin: 0;
}

.blind-vision-cta {
  /* Keep button appearance but use standardized properties where possible */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--dia-space-1);
  padding: var(--dia-space-2) var(--dia-space-4);
  border-radius: var(--radius-full);
  background: var(--dia-color-text-primary);
  color: var(--dia-color-background);
  font-family: var(--font-family-inter);
  /* Use larger CTA size for button style */
  font-size: var(--dia-btn-cta-size);
  font-weight: var(--dia-btn-cta-weight);
  line-height: var(--dia-btn-cta-line-height);
  letter-spacing: var(--letter-spacing-normal);
  text-decoration: none;
  transition: all var(--transition-base);
  white-space: nowrap;
  box-shadow: var(--shadow-md);
  border: none;
}

.blind-vision-cta:hover {
  transform: translateY(-2px);
  background: var(--dia-color-text-primary-hover);
  box-shadow: var(--shadow-lg);
}

/* Video player styles */
.video-play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--dia-color-text-primary);
  cursor: pointer;
  transition: transform 0.2s;
  position: relative;
  z-index: 2;
}

.video-play-button:hover {
  transform: scale(1.1);
}

.video-play-button:after {
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12px 0 12px 20px;
  border-color: transparent transparent transparent #ffffff;
  margin-left: 5px;
}

@media (max-width: var(--breakpoint-tablet)) {
  .blind-vision-section {
    padding: var(--dia-space-8) 0;
  }

  /* Content hierarchy classes handle responsive typography automatically */

  /* Partnership heading responsive styles handled by section-subheading class */

  .blind-vision-features {
    flex-direction: column;
    align-items: center;
  }

  .feature-card {
    width: 100%;
    max-width: 400px;
  }

  .blind-vision-cta-description p {
    font-size: var(--font-size-section-description-tablet);
    line-height: var(--line-height-section-description);
  }
}

@media (max-width: var(--breakpoint-mobile)) {
  .blind-vision-section {
    padding: var(--dia-space-6) 0;
  }

  /* Content hierarchy classes handle responsive typography automatically */

  /* Partnership heading responsive styles handled by section-subheading class */

  .blind-vision-cta-description p {
    font-size: var(--font-size-section-description-mobile);
    line-height: var(--line-height-section-description);
  }
}

/* Bento cards now use universal white background - no override needed */

/* Diabrowser migration: BentoGrid spacing adjustments */
.blind-vision-section.dia-spacing .bento-grid {
  gap: var(--dia-space-4);
  margin-top: var(--dia-space-10);
}

.blind-vision-section.dia-spacing .capabilities-content {
  margin-top: var(--dia-space-12);
}

/* Service Carousel Section */
.solutions-carousel-section {
  margin-top: var(--dia-space-16); /* Increased spacing between carousel subsections */
  margin-bottom: var(--dia-space-10); /* Increased bottom spacing */
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* First carousel section - less top margin since it follows the main header */
.solutions-carousel-section:first-of-type {
  margin-top: var(--dia-space-12);
}

.solutions-carousel-header {
  text-align: center;
  margin-bottom: var(--dia-space-8);
  max-width: var(--width-616);
  margin-left: auto;
  margin-right: auto;
}

.solutions-carousel-header .section-description {
  margin-top: var(--dia-space-4);
}

@media (max-width: 800px) {
  .solutions-carousel-section {
    margin-top: var(--dia-space-14); /* Appropriate mobile spacing between subsections */
    margin-bottom: var(--dia-space-8);
  }

  /* First carousel section on mobile */
  .solutions-carousel-section:first-of-type {
    margin-top: var(--dia-space-10);
  }

  .solutions-carousel-header {
    margin-bottom: var(--dia-space-6);
  }
}
