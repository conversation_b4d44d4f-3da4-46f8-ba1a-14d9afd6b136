import React from "react";
import "./DevicesSection.css";
import FadeIn from "./animations/FadeIn";
import ScaleIn from "./animations/ScaleIn";
import { motion } from "framer-motion";
import TestimonialsColumn from "./ui/testimonials-column";

// Testimonials data 
const testimonials = [
  {
    text: "<PERSON><PERSON><PERSON> has a sixth sense for finding growth opportunities. He spotted gaps in our strategy no one else saw and helped us scale BEFORE increasing our budget.",
    image: "/images/testimonials/t1.webp",
    name: "<PERSON>",
    role: "Co-Founder - <PERSON><PERSON>",
  },
  {
    text: "Every recommendation felt tailored to our business, and every week we saw measurable improvements.",
    image: "/images/testimonials/t2.webp",
    name: "<PERSON>iselle",
    role: "Marketing Director - <PERSON><PERSON><PERSON>",
  },
  {
    text: "I don't usually leave testimonials, but the results were too good not to share. This was our most profitable Q4 yet. Hire him if you want to win.",
    image: "/images/testimonials/t3.webp",
    name: "<PERSON>",
    role: "CEO & Founder - <PERSON><PERSON><PERSON>",
  },
  {
    text: "<PERSON><PERSON><PERSON> doesn't sugarcoat it. He showed us exactly where we were losing money and how to fix it. Within weeks, our ad strategy was unrecognizable.",
    image: "/images/testimonials/t4.webp",
    name: "<PERSON> Refat",
    role: "Ecom Director - GoSupps",
  },
  {
    text: "I was skeptical about switching to Google Ads, but this team made it painless. No hand-holding, no fluff—just results.",
    image: "/images/testimonials/t5.webp",
    name: "Roger H",
    role: "Owner - Jewellery Brand",
  },
  {
    text: "Google Ads was always a headache until FunnelVision took over. We're now seeing consistent growth every quarter.",
    image: "/images/testimonials/t6.webp",
    name: "Liam",
    role: "VP Marketing - Adversa",
  },
  {
    text: "Take it from me: they care. They really do. Highly recommend, that's all I can say!",
    image: "/images/testimonials/t7.webp",
    name: "Priscilla Vasquez",
    role: "Ecommerce & Digital Lead - Florent",
  },
  {
    text: "Our CAC dropped overnight, and our revenue soared. There is now more synergy between marketing and sales efforts.",
    image: "/images/testimonials/t8.webp",
    name: "Peeter Kuum",
    role: "Sales Director - Wermo",
  },
  {
    text: "FunnelVision are not just an extension of your marketing team as a paid search agency, but they are collaborative, proactive partners.",
    image: "/images/testimonials/t9.webp",
    name: "Ayanda",
    role: "Head of Demand Generation",
  },
];

// Split testimonials into three columns
const firstColumn = testimonials.slice(0, 3);
const secondColumn = testimonials.slice(3, 6);
const thirdColumn = testimonials.slice(6, 9);

const DevicesSection = () => {
  return (
    <section className="devices-section" style={{ backgroundColor: '#f5f5f7' }}>
      <div className="container">
        <div className="dorsey-container">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
            <h2 className="section-heading">Our work</h2>
            <div className="section-description">
              <p>
                Dorsey Parker is a musician. He also has 8% vision, and is losing
                the sight he has.
              </p>
            </div>
          </FadeIn>

          <ScaleIn className="delay-200">
            <div className="dorsey-video">
              <div className="video-container video-card">
                <div className="video-placeholder">
                  <div className="play-button button-hover">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="btn-icon"
                    >
                      <path d="M8 5v14l11-7z" fill="white" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </ScaleIn>

          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-300">
            <div className="dorsey-story">
              <p>
                Dorsey's using Project Astra to adapt to this change in his life.
                Using his phone, Project Astra describes his surroundings, and
                accesses apps like Lens and Maps to help him explore new places.
              </p>
            </div>
          </FadeIn>
        </div>

        {/* Testimonials Section */}
        <div className="testimonials-container">
          <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100">
            <h2 className="section-heading">What our clients say about us</h2>
          </FadeIn>

          <div className="testimonials-columns-wrapper">
            <TestimonialsColumn testimonials={firstColumn} duration={35} />
            <TestimonialsColumn testimonials={secondColumn} className="testimonial-column-md" duration={45} />
            <TestimonialsColumn testimonials={thirdColumn} className="testimonial-column-lg" duration={55} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default DevicesSection;
