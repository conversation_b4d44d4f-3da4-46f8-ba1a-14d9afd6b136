import { useRef, useEffect, useState, useMemo } from 'react';

/**
 * Custom hook that uses the Intersection Observer API to detect when an element
 * enters or exits the viewport.
 *
 * @param {Object} options - Options for the Intersection Observer.
 *   - `root`: The element that is used as the viewport for checking visibility of the target.
 *   - `rootMargin`: Margin around the root. Can have values similar to the CSS margin property.
 *   - `threshold`: Either a single number or an array of numbers which indicate at what percentage
 *                  of the target's visibility the observer's callback should be executed.
 * @returns {Array} A tuple containing a ref to attach to the observed element
 *          and a boolean indicating if the element is currently visible.
 */
const useIntersectionObserver = ({ root = null, rootMargin = '0px', threshold = 0 } = {}) => {
  const elementRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const memoizedOptions = useMemo(
    () => ({ root, rootMargin, threshold }),
    [root, rootMargin, threshold]
  );

  useEffect(() => {
    // Check if we're in a browser environment with IntersectionObserver support
    if (typeof window === 'undefined' || !window.IntersectionObserver) {
      // If not supported, just set visible to true to avoid breaking animations
      setIsVisible(true);
      return undefined;
    }
    
    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, memoizedOptions);

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [memoizedOptions]);

  return [elementRef, isVisible];
};

export default useIntersectionObserver;
