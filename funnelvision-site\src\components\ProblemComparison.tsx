import React from "react";
import FadeIn from "./animations/FadeIn";

interface ProblemItemProps {
  icon: React.ReactNode;
  text: string;
  isPositive: boolean;
}

const ProblemItem: React.FC<ProblemItemProps> = ({ icon, text, isPositive }) => (
  <div
    className={`flex items-start ${isPositive ? "positive" : "negative"}`}
    style={{ gap: 'var(--dia-space-5)' }}
  >
    <span
      className="flex-shrink-0 text-current"
      style={{ width: 'var(--dia-space-6)', height: 'var(--dia-space-6)' }}
    >
      {icon}
    </span>
    <span
      className="text-text-primary flex-1"
      style={{
        fontSize: 'var(--dia-font-size-base)',
        fontWeight: 'var(--dia-font-weight-normal)',
        lineHeight: '1.6',
        fontFamily: 'var(--font-family-inter)'
      }}
    >
      {text}
    </span>
  </div>
);

const RedXIcon: React.FC = () => (
  <svg viewBox="0 0 24 24" fill="currentColor" stroke="none" className="w-6 h-6 text-red-500">
    <circle cx="12" cy="12" r="10" fill="currentColor"/>
    <path d="m15 9-6 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="m9 9 6 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const GreenCheckIcon: React.FC = () => (
  <svg viewBox="0 0 24 24" fill="currentColor" stroke="none" className="w-6 h-6 text-green-500">
    <circle cx="12" cy="12" r="10" fill="currentColor"/>
    <path d="m9 12 2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

interface ProblemData {
  text: string;
}

const ProblemComparison: React.FC = () => {
  const roasProblems: ProblemData[] = [
    { text: "Optimizes for revenue" },
    { text: "Treats all products the same" },
    { text: "Focuses on CTR and CPC" },
    { text: "Reactive campaign structure" },
    { text: "One-size-fits-all media buying" },
    { text: "Outsourced execution" }
  ];

  const paxSolutions: ProblemData[] = [
    { text: "We optimize for profit" },
    { text: "We segment bids by margin tier" },
    { text: "We focus on contribution margin" },
    { text: "Every campaign is built from the margin up" },
    { text: "Custom strategy per client funnel" },
    { text: "Founder-led strategy with senior operators" }
  ];

  return (
    <div
      className="w-full flex flex-col mx-auto"
      style={{ gap: 'var(--dia-space-16)' }}
    >
      {/* ROAS Problems Layout */}
      <div
        className="comparison-layout flex items-stretch w-full"
        style={{ gap: 'var(--dia-space-10)' }}
      >
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up flex-1 flex">
          <div
            className="comparison-text flex-1 flex flex-col justify-center"
            style={{ padding: 'var(--dia-space-8)' }}
          >
            <h2 className="section-heading text-text-primary" style={{ marginBottom: 'var(--dia-space-4)' }}>
              ROAS Tells You Revenue.
            </h2>
            <div className="section-description">
              <p className="body-text text-text-secondary" style={{ marginBottom: '0' }}>
                Some systems, including heating-only, cooling-only, zone-controlled, and
                heat pump systems require a Nest Power Connector or C wire.
              </p>
            </div>
          </div>
        </FadeIn>

        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-100 flex-1 flex">
          <div
            className="comparison-card flex-1 flex flex-col cursor-pointer transition-all duration-200"
            style={{
              background: 'var(--dia-color-card-background)',
              borderRadius: 'var(--dia-radius-standard)',
              border: '1px solid rgba(0, 0, 0, 0.04)',
              padding: 'var(--dia-space-10)',
              boxShadow: 'var(--dia-shadow-sm)'
            }}
          >
            <h4
              className="text-text-primary text-left"
              style={{
                fontSize: 'var(--dia-font-size-4xl)',
                fontWeight: 'var(--dia-font-weight-medium)',
                lineHeight: '1.2',
                marginBottom: 'var(--dia-space-10)',
                fontFamily: 'var(--font-family-inter)',
                letterSpacing: '0'
              }}
            >
              ROAS approach
            </h4>
            <div
              className="flex flex-col"
              style={{ gap: 'var(--dia-space-6)' }}
            >
              {roasProblems.map((item, index) => (
                <ProblemItem
                  key={`roas-${index}`}
                  icon={<RedXIcon />}
                  text={item.text}
                  isPositive={false}
                />
              ))}
            </div>
          </div>
        </FadeIn>
      </div>

      {/* PAX Solutions Layout */}
      <div
        className="comparison-layout reverse flex flex-row-reverse items-stretch w-full"
        style={{ gap: 'var(--dia-space-10)' }}
      >
        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-300 flex-1 flex">
          <div
            className="comparison-text flex-1 flex flex-col justify-center"
            style={{ padding: 'var(--dia-space-8)' }}
          >
            <h2 className="section-heading text-text-primary" style={{ marginBottom: 'var(--dia-space-4)' }}>
              PAX™ Tells You the Truth.
            </h2>
            <div className="section-description">
              <p className="body-text text-text-secondary" style={{ marginBottom: '0' }}>
                We created the PAX framework to focus on what actually drives sustainable growth.
              </p>
            </div>
          </div>
        </FadeIn>

        <FadeIn threshold={0.1} rootMargin="0px" className="fade-up delay-200 flex-1 flex">
          <div
            className="comparison-card flex-1 flex flex-col cursor-pointer transition-all duration-200"
            style={{
              background: 'var(--dia-color-card-background-alt)',
              borderRadius: 'var(--dia-radius-standard)',
              padding: 'var(--dia-space-10)',
              boxShadow: 'var(--dia-shadow-sm)'
            }}
          >
            <h4
              className="text-text-primary text-left"
              style={{
                fontSize: 'var(--dia-font-size-4xl)',
                fontWeight: 'var(--dia-font-weight-medium)',
                lineHeight: '1.2',
                marginBottom: 'var(--dia-space-10)',
                fontFamily: 'var(--font-family-inter)',
                letterSpacing: '0'
              }}
            >
              PAX™ approach
            </h4>
            <div
              className="flex flex-col"
              style={{ gap: 'var(--dia-space-6)' }}
            >
              {paxSolutions.map((item, index) => (
                <ProblemItem
                  key={`pax-${index}`}
                  icon={<GreenCheckIcon />}
                  text={item.text}
                  isPositive={true}
                />
              ))}
            </div>
          </div>
        </FadeIn>
      </div>
    </div>
  );
};

export default ProblemComparison;
