/* Optimized Bento Grid Styles - Diabrowser Design System */
.bento-grid {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--dia-space-4);
  margin-top: var(--dia-space-6);
}

/* Diabrowser responsive breakpoints: 800px, 1000px */
@media (min-width: 800px) {
  .bento-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--dia-space-5);
  }
}

@media (min-width: 1000px) {
  .bento-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--dia-space-6);
  }
}

/* Simplified grid layout - no complex positioning needed */

/* Optimized Bento Card - Consistent light mode styling */
.bento-card {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  border: var(--card-border); /* Universal thin gray border */
  transition: var(--card-transition);
  height: 100%;
  background: var(--card-background-gradient); /* Light mode gradient */
  background-color: var(--dia-color-card-background); /* Pure white cards */
  box-shadow: var(--card-box-shadow);
  min-height: var(--dia-space-15); /* Consistent minimum height */
}

/* Optimized hover and content structure - Enhanced light mode */
.bento-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--card-hover-box-shadow);
  background: var(--card-hover-background-gradient);
  border-color: var(--dia-color-card-border-hover);
  border-top: var(--card-border-top-highlight);
}

.bento-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: var(--dia-space-12); /* Match testimonial card padding for better breathing room */
}

.bento-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--dia-space-12);
  height: var(--dia-space-12);
  color: var(--dia-color-text-primary);
  background-color: #F9F6F2;
  border-radius: var(--radius-full);
  margin-bottom: var(--dia-space-6); /* Increase spacing after icon like testimonials */
}

/* Number container styling for numbered bento cards */
.bento-number-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--dia-space-12); /* 48px number container */
  height: var(--dia-space-12);
  margin-bottom: var(--dia-space-6);
  background-color: var(--dia-color-text-primary);
  border-radius: 50%;
  color: var(--dia-color-white);
}

.bento-number {
  font-family: var(--font-family-inter);
  font-size: var(--font-size-20);
  font-weight: 600;
  line-height: 1;
}

/* Optimized Typography - Diabrowser tokens */
.bento-title {
  font-family: var(--font-family-inter);
  font-size: var(--dia-bento-title-size);
  font-weight: var(--dia-bento-title-weight);
  line-height: var(--dia-bento-title-line-height);
  letter-spacing: var(--dia-section-heading-spacing);
  color: var(--dia-color-text-primary);
  margin: 0 0 var(--dia-space-6) 0; /* Increase spacing after title like testimonials */
}

.bento-description {
  font-family: var(--font-family-inter);
  font-size: var(--dia-bento-description-size);
  font-weight: var(--dia-bento-description-weight);
  line-height: var(--dia-bento-description-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  color: var(--dia-color-text-secondary);
  margin: 0;
  flex-grow: 1;
}

/* Optimized Footer and CTA */
.bento-footer {
  margin-top: auto;
  padding-top: var(--dia-space-6); /* Increase spacing before footer like testimonials */
}

/*
 * BENTO CTA - Using Unified CTA System
 * Consistent with hero section and site-wide standards
 */

.bento-cta {
  /* Use secondary CTA styling for bento cards */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--cta-gap);
  padding: var(--dia-space-2) var(--dia-space-4); /* Compact sizing for cards */
  border-radius: var(--cta-border-radius);
  min-width: auto; /* Allow compact sizing in cards */
  height: auto; /* Allow flexible height in cards */
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  border: var(--cta-secondary-border);
  font-family: var(--font-family-inter);
  font-size: var(--card-cta-text-size);
  font-weight: var(--dia-btn-secondary-weight);
  line-height: var(--dia-btn-secondary-line-height);
  letter-spacing: var(--dia-body-text-spacing);
  transition: var(--cta-transition);
  white-space: nowrap;
  outline: none;

  /* Secondary CTA colors */
  background: var(--cta-secondary-background);
  color: var(--cta-secondary-text-color);
  box-shadow: var(--cta-secondary-shadow);
}

.bento-cta:hover {
  background: var(--cta-secondary-background-hover);
  box-shadow: var(--cta-secondary-shadow-hover);
  transform: var(--cta-hover-transform);
}

.bento-arrow {
  width: var(--dia-space-3);
  height: var(--dia-space-3);
  margin-left: var(--dia-space-2);
  transition: transform var(--transition-base);
}

.bento-card:hover .bento-arrow {
  transform: translateX(var(--dia-space-1));
}

/* Clean icon styling - no complex hover effects */
.bento-card-icon,
.bento-icon {
  height: auto;
  max-width: 100%;
  color: var(--dia-color-text-primary);
  transition: color var(--transition-base);
}

.bento-card:hover .bento-card-icon,
.bento-card:hover .bento-icon {
  color: var(--dia-color-text-secondary);
}

/* Unified title and description styles - no duplicates */
.capability-title,
.bento-card-title,
.bento-title {
  font-size: var(--dia-bento-title-size);
  line-height: var(--dia-bento-title-line-height);
  font-weight: var(--dia-bento-title-weight);
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  margin: 0 0 var(--dia-space-6) 0;
}

.capability-description,
.bento-card-description,
.bento-description {
  font-size: var(--dia-bento-description-size);
  line-height: var(--dia-bento-description-line-height);
  color: var(--dia-color-text-secondary);
  font-family: var(--font-family-inter);
  margin: 0;
  flex-grow: 1;
}

/* Simplified button styling */
.button-ghost {
  background: transparent;
  color: var(--dia-color-text-primary);
  font-family: var(--font-family-inter);
  font-weight: var(--dia-btn-secondary-weight);
  font-size: var(--dia-btn-secondary-size);
  padding: var(--dia-space-2) var(--dia-space-3);
  border-radius: var(--radius-standard); /* Standardized 16px radius */
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--transition-base);
  text-decoration: none;
}

.button-ghost:hover {
  background-color: var(--dia-color-background-overlay);
}

/* Optimized responsive styles - diabrowser breakpoints */
@media (min-width: 800px) {
  .bento-card {
    min-height: var(--dia-space-16); /* 16rem = 160px */
  }
}

@media (min-width: 1000px) {
  .bento-card {
    min-height: var(--dia-space-18); /* 18rem = 180px */
  }

  /* Helper classes for larger cards if needed */
  .bento-card-lg {
    grid-column: span 2;
  }

  .bento-card-full {
    grid-column: span 3;
  }
}
